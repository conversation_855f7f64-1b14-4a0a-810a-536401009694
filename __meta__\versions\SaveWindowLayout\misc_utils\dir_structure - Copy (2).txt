win32com.gen_py.EAB22AC0-30C1-11CF-A7EB-0000C05BAE0Bx0x1x1.IWebBrowser2.IWebBrowser2(
  AddressBar = True, 
  Application = win32com.gen_py.EAB22AC0-30C1-11CF-A7EB-0000C05BAE0Bx0x1x1.IWebBrowser2.IWebBrowser2(
      AddressBar = True, 
      Application = win32com.gen_py.EAB22AC0-30C1-11CF-A7EB-0000C05BAE0Bx0x1x1.IWebBrowser2.IWebBrowser2(
          AddressBar = True, 
          Application = win32com.gen_py.EAB22AC0-30C1-11CF-A7EB-0000C05BAE0Bx0x1x1.IWebBrowser2.IWebBrowser2(
              AddressBar = True, 
              Application = win32com.gen_py.EAB22AC0-30C1-11CF-A7EB-0000C05BAE0Bx0x1x1.IWebBrowser2.IWebBrowser2(
                  AddressBar = True, 
                  Application = win32com.gen_py.EAB22AC0-30C1-11CF-A7EB-0000C05BAE0Bx0x1x1.IWebBrowser2.IWebBrowser2(...), 
                  Busy = False, 
                  CLSID = PyIID(...), 
                  Container = None, 
                  Document = win32com.client.CDispatch(...), 
                  FullName = 'C:\\Windows\\explorer.exe', 
                  FullScreen = False, 
                  HWND = 6163636, 
                  Height = 443, 
                  Left = 1920, 
                  LocationName = 'SaveWindowLayout', 
                  LocationURL = 'file:///D:/Data/Prosjekte...pts/WIP/SaveWindowLayout', 
                  MenuBar = False, 
                  Name = 'File Explorer', 
                  Offline = False, 
                  Parent = win32com.gen_py.EAB22AC0-30C1-11CF-A7EB-0000C05BAE0Bx0x1x1.IWebBrowser2.IWebBrowser2(...), 
                  Path = 'C:\\Windows\\', 
                  ReadyState = 4, 
                  RegisterAsBrowser = False, 
                  RegisterAsDropTarget = True, 
                  Resizable = True, 
                  Silent = False, 
                  StatusBar = False, 
                  StatusText = '', 
                  TheaterMode = False, 
                  ToolBar = 1, 
                  Top = 0, 
                  TopLevelContainer = True, 
                  Type = <Attribute error: com_error>, 
                  Visible = True, 
                  Width = 1920, 
                  _oleobj_ = PyIDispatch(...), 
                  _prop_map_get_ = {...}, 
                  _prop_map_put_ = {...}, 
                  coclass_clsid = PyIID(...)
                ), 
              Busy = False, 
              CLSID = PyIID(), 
              Container = None, 
              Document = win32com.client.CDispatch(
                  Application = win32com.client.CDispatch(...), 
                  CurrentViewMode = 4, 
                  FocusedItem = win32com.client.CDispatch(...), 
                  Folder = win32com.client.CDispatch(...), 
                  FolderFlags = 1092616192, 
                  GroupBy = 'System.Null', 
                  IconSize = 16, 
                  Parent = <Attribute error: com_error>, 
                  Script = <Attribute error: com_error>, 
                  SortColumns = 'prop:-System.DateModified;', 
                  ViewOptions = 170, 
                  _builtMethods_ = {...}, 
                  _enum_ = None, 
                  _lazydata_ = (...), 
                  _mapCachedItems_ = {...}, 
                  _oleobj_ = PyIDispatch(...), 
                  _olerepr_ = win32com.client.build.LazyDispatchItem(...), 
                  _unicode_to_string_ = None, 
                  _username_ = 'Document'
                ), 
              FullName = 'C:\\Windows\\explorer.exe', 
              FullScreen = False, 
              HWND = 6163636, 
              Height = 443, 
              Left = 1920, 
              LocationName = 'SaveWindowLayout', 
              LocationURL = 'file:///D:/Data/Prosjekte...pts/WIP/SaveWindowLayout', 
              MenuBar = False, 
              Name = 'File Explorer', 
              Offline = False, 
              Parent = win32com.gen_py.EAB22AC0-30C1-11CF-A7EB-0000C05BAE0Bx0x1x1.IWebBrowser2.IWebBrowser2(
                  AddressBar = True, 
                  Application = win32com.gen_py.EAB22AC0-30C1-11CF-A7EB-0000C05BAE0Bx0x1x1.IWebBrowser2.IWebBrowser2(...), 
                  Busy = False, 
                  CLSID = PyIID(...), 
                  Container = None, 
                  Document = win32com.client.CDispatch(...), 
                  FullName = 'C:\\Windows\\explorer.exe', 
                  FullScreen = False, 
                  HWND = 6163636, 
                  Height = 443, 
                  Left = 1920, 
                  LocationName = 'SaveWindowLayout', 
                  LocationURL = 'file:///D:/Data/Prosjekte...pts/WIP/SaveWindowLayout', 
                  MenuBar = False, 
                  Name = 'File Explorer', 
                  Offline = False, 
                  Parent = win32com.gen_py.EAB22AC0-30C1-11CF-A7EB-0000C05BAE0Bx0x1x1.IWebBrowser2.IWebBrowser2(...), 
                  Path = 'C:\\Windows\\', 
                  ReadyState = 4, 
                  RegisterAsBrowser = False, 
                  RegisterAsDropTarget = True, 
                  Resizable = True, 
                  Silent = False, 
                  StatusBar = False, 
                  StatusText = '', 
                  TheaterMode = False, 
                  ToolBar = 1, 
                  Top = 0, 
                  TopLevelContainer = True, 
                  Type = <Attribute error: com_error>, 
                  Visible = True, 
                  Width = 1920, 
                  _oleobj_ = PyIDispatch(...), 
                  _prop_map_get_ = {...}, 
                  _prop_map_put_ = {...}, 
                  coclass_clsid = PyIID(...)
                ), 
              Path = 'C:\\Windows\\', 
              ReadyState = 4, 
              RegisterAsBrowser = False, 
              RegisterAsDropTarget = True, 
              Resizable = True, 
              Silent = False, 
              StatusBar = False, 
              StatusText = '', 
              TheaterMode = False, 
              ToolBar = 1, 
              Top = 0, 
              TopLevelContainer = True, 
              Type = <Attribute error: com_error>, 
              Visible = True, 
              Width = 1920, 
              _oleobj_ = PyIDispatch(), 
              _prop_map_get_ = {
                  'AddressBar': (...), 
                  'Application': (...), 
                  'Busy': (...), 
                  'Container': (...), 
                  'Document': (...), 
                  'FullName': (...), 
                  'FullScreen': (...), 
                  'HWND': (...), 
                  'Height': (...), 
                  'Left': (...), 
                  'LocationName': (...), 
                  'LocationURL': (...), 
                  'MenuBar': (...), 
                  'Name': (...), 
                  'Offline': (...), 
                  'Parent': (...), 
                  'Path': (...), 
                  'ReadyState': (...), 
                  'RegisterAsBrowser': (...), 
                  'RegisterAsDropTarget': (...), 
                  'Resizable': (...), 
                  'Silent': (...), 
                  'StatusBar': (...), 
                  'StatusText': (...), 
                  'TheaterMode': (...), 
                  'ToolBar': (...), 
                  'Top': (...), 
                  'TopLevelContainer': (...), 
                  'Type': (...), 
                  'Visible': (...), 
                  'Width': (...)
                }, 
              _prop_map_put_ = {
                  'AddressBar': (...), 
                  'FullScreen': (...), 
                  'Height': (...), 
                  'Left': (...), 
                  'MenuBar': (...), 
                  'Offline': (...), 
                  'RegisterAsBrowser': (...), 
                  'RegisterAsDropTarget': (...), 
                  'Resizable': (...), 
                  'Silent': (...), 
                  'StatusBar': (...), 
                  'StatusText': (...), 
                  'TheaterMode': (...), 
                  'ToolBar': (...), 
                  'Top': (...), 
                  'Visible': (...), 
                  'Width': (...)
                }, 
              coclass_clsid = PyIID()
            ), 
          Busy = False, 
          CLSID = PyIID(), 
          Container = None, 
          Document = win32com.client.CDispatch(
              Application = win32com.client.CDispatch(
                  Application = win32com.client.CDispatch(...), 
                  Parent = win32com.client.CDispatch(...), 
                  _builtMethods_ = {...}, 
                  _enum_ = None, 
                  _lazydata_ = (...), 
                  _mapCachedItems_ = {...}, 
                  _oleobj_ = PyIDispatch(...), 
                  _olerepr_ = win32com.client.build.LazyDispatchItem(...), 
                  _unicode_to_string_ = None, 
                  _username_ = '<unknown>'
                ), 
              CurrentViewMode = 4, 
              FocusedItem = win32com.client.CDispatch(
                  Application = win32com.client.CDispatch(...), 
                  GetFolder = <Attribute error: com_error>, 
                  GetLink = <Attribute error: com_error>, 
                  IsBrowsable = False, 
                  IsFileSystem = True, 
                  IsFolder = False, 
                  IsLink = False, 
                  ModifyDate = datetime(...), 
                  Name = '-OPEN-VENV-PROMPT.bat', 
                  Parent = win32com.client.CDispatch(...), 
                  Path = 'D:\\Data\\Prosjekter\\Script...ut\\-OPEN-VENV-PROMPT.bat', 
                  Size = 138, 
                  Type = 'Windows Batch File', 
                  _builtMethods_ = {...}, 
                  _enum_ = None, 
                  _lazydata_ = (...), 
                  _mapCachedItems_ = {...}, 
                  _oleobj_ = PyIDispatch(...), 
                  _olerepr_ = win32com.client.build.LazyDispatchItem(...), 
                  _unicode_to_string_ = None, 
                  _username_ = '<unknown>'
                ), 
              Folder = win32com.client.CDispatch(
                  Application = win32com.client.CDispatch(...), 
                  HaveToShowWebViewBarricade = False, 
                  OfflineStatus = <Attribute error: com_error>, 
                  Parent = <Attribute error: com_error>, 
                  ParentFolder = win32com.client.CDispatch(...), 
                  Self = win32com.client.CDispatch(...), 
                  ShowWebViewBarricade = False, 
                  Title = 'SaveWindowLayout', 
                  _builtMethods_ = {...}, 
                  _enum_ = None, 
                  _lazydata_ = (...), 
                  _mapCachedItems_ = {...}, 
                  _oleobj_ = PyIDispatch(...), 
                  _olerepr_ = win32com.client.build.LazyDispatchItem(...), 
                  _unicode_to_string_ = None, 
                  _username_ = '<unknown>'
                ), 
              FolderFlags = 1092616192, 
              GroupBy = 'System.Null', 
              IconSize = 16, 
              Parent = <Attribute error: com_error>, 
              Script = <Attribute error: com_error>, 
              SortColumns = 'prop:-System.DateModified;', 
              ViewOptions = 170, 
              _builtMethods_ = {
                  'FilterView': win32com.client.dynamic.function(...), 
                  'PopupItemMenu': win32com.client.dynamic.function(...), 
                  'SelectItem': win32com.client.dynamic.function(...), 
                  'SelectItemRelative': win32com.client.dynamic.function(...), 
                  'SelectedItems': win32com.client.dynamic.function(...)
                }, 
              _enum_ = None, 
              _lazydata_ = (PyITypeInfo(...), PyITypeComp(...)), 
              _mapCachedItems_ = {}, 
              _oleobj_ = PyIDispatch(), 
              _olerepr_ = win32com.client.build.LazyDispatchItem(
                  bIsDispatch = 0, 
                  bIsSink = 0, 
                  bWritten = 0, 
                  clsid = None, 
                  co_class = None, 
                  defaultDispatchName = None, 
                  doc = None, 
                  hidden = 0, 
                  mapFuncs = {...}, 
                  propMap = {...}, 
                  propMapGet = {...}, 
                  propMapPut = {...}, 
                  python_name = None, 
                  typename = 'LazyDispatchItem'
                ), 
              _unicode_to_string_ = None, 
              _username_ = 'Document'
            ), 
          FullName = 'C:\\Windows\\explorer.exe', 
          FullScreen = False, 
          HWND = 6163636, 
          Height = 443, 
          Left = 1920, 
          LocationName = 'SaveWindowLayout', 
          LocationURL = 'file:///D:/Data/Prosjekte...pts/WIP/SaveWindowLayout', 
          MenuBar = False, 
          Name = 'File Explorer', 
          Offline = False, 
          Parent = win32com.gen_py.EAB22AC0-30C1-11CF-A7EB-0000C05BAE0Bx0x1x1.IWebBrowser2.IWebBrowser2(
              AddressBar = True, 
              Application = win32com.gen_py.EAB22AC0-30C1-11CF-A7EB-0000C05BAE0Bx0x1x1.IWebBrowser2.IWebBrowser2(
                  AddressBar = True, 
                  Application = win32com.gen_py.EAB22AC0-30C1-11CF-A7EB-0000C05BAE0Bx0x1x1.IWebBrowser2.IWebBrowser2(...), 
                  Busy = False, 
                  CLSID = PyIID(...), 
                  Container = None, 
                  Document = win32com.client.CDispatch(...), 
                  FullName = 'C:\\Windows\\explorer.exe', 
                  FullScreen = False, 
                  HWND = 6163636, 
                  Height = 443, 
                  Left = 1920, 
                  LocationName = 'SaveWindowLayout', 
                  LocationURL = 'file:///D:/Data/Prosjekte...pts/WIP/SaveWindowLayout', 
                  MenuBar = False, 
                  Name = 'File Explorer', 
                  Offline = False, 
                  Parent = win32com.gen_py.EAB22AC0-30C1-11CF-A7EB-0000C05BAE0Bx0x1x1.IWebBrowser2.IWebBrowser2(...), 
                  Path = 'C:\\Windows\\', 
                  ReadyState = 4, 
                  RegisterAsBrowser = False, 
                  RegisterAsDropTarget = True, 
                  Resizable = True, 
                  Silent = False, 
                  StatusBar = False, 
                  StatusText = '', 
                  TheaterMode = False, 
                  ToolBar = 1, 
                  Top = 0, 
                  TopLevelContainer = True, 
                  Type = <Attribute error: com_error>, 
                  Visible = True, 
                  Width = 1920, 
                  _oleobj_ = PyIDispatch(...), 
                  _prop_map_get_ = {...}, 
                  _prop_map_put_ = {...}, 
                  coclass_clsid = PyIID(...)
                ), 
              Busy = False, 
              CLSID = PyIID(), 
              Container = None, 
              Document = win32com.client.CDispatch(
                  Application = win32com.client.CDispatch(...), 
                  CurrentViewMode = 4, 
                  FocusedItem = win32com.client.CDispatch(...), 
                  Folder = win32com.client.CDispatch(...), 
                  FolderFlags = 1092616192, 
                  GroupBy = 'System.Null', 
                  IconSize = 16, 
                  Parent = <Attribute error: com_error>, 
                  Script = <Attribute error: com_error>, 
                  SortColumns = 'prop:-System.DateModified;', 
                  ViewOptions = 170, 
                  _builtMethods_ = {...}, 
                  _enum_ = None, 
                  _lazydata_ = (...), 
                  _mapCachedItems_ = {...}, 
                  _oleobj_ = PyIDispatch(...), 
                  _olerepr_ = win32com.client.build.LazyDispatchItem(...), 
                  _unicode_to_string_ = None, 
                  _username_ = 'Document'
                ), 
              FullName = 'C:\\Windows\\explorer.exe', 
              FullScreen = False, 
              HWND = 6163636, 
              Height = 443, 
              Left = 1920, 
              LocationName = 'SaveWindowLayout', 
              LocationURL = 'file:///D:/Data/Prosjekte...pts/WIP/SaveWindowLayout', 
              MenuBar = False, 
              Name = 'File Explorer', 
              Offline = False, 
              Parent = win32com.gen_py.EAB22AC0-30C1-11CF-A7EB-0000C05BAE0Bx0x1x1.IWebBrowser2.IWebBrowser2(
                  AddressBar = True, 
                  Application = win32com.gen_py.EAB22AC0-30C1-11CF-A7EB-0000C05BAE0Bx0x1x1.IWebBrowser2.IWebBrowser2(...), 
                  Busy = False, 
                  CLSID = PyIID(...), 
                  Container = None, 
                  Document = win32com.client.CDispatch(...), 
                  FullName = 'C:\\Windows\\explorer.exe', 
                  FullScreen = False, 
                  HWND = 6163636, 
                  Height = 443, 
                  Left = 1920, 
                  LocationName = 'SaveWindowLayout', 
                  LocationURL = 'file:///D:/Data/Prosjekte...pts/WIP/SaveWindowLayout', 
                  MenuBar = False, 
                  Name = 'File Explorer', 
                  Offline = False, 
                  Parent = win32com.gen_py.EAB22AC0-30C1-11CF-A7EB-0000C05BAE0Bx0x1x1.IWebBrowser2.IWebBrowser2(...), 
                  Path = 'C:\\Windows\\', 
                  ReadyState = 4, 
                  RegisterAsBrowser = False, 
                  RegisterAsDropTarget = True, 
                  Resizable = True, 
                  Silent = False, 
                  StatusBar = False, 
                  StatusText = '', 
                  TheaterMode = False, 
                  ToolBar = 1, 
                  Top = 0, 
                  TopLevelContainer = True, 
                  Type = <Attribute error: com_error>, 
                  Visible = True, 
                  Width = 1920, 
                  _oleobj_ = PyIDispatch(...), 
                  _prop_map_get_ = {...}, 
                  _prop_map_put_ = {...}, 
                  coclass_clsid = PyIID(...)
                ), 
              Path = 'C:\\Windows\\', 
              ReadyState = 4, 
              RegisterAsBrowser = False, 
              RegisterAsDropTarget = True, 
              Resizable = True, 
              Silent = False, 
              StatusBar = False, 
              StatusText = '', 
              TheaterMode = False, 
              ToolBar = 1, 
              Top = 0, 
              TopLevelContainer = True, 
              Type = <Attribute error: com_error>, 
              Visible = True, 
              Width = 1920, 
              _oleobj_ = PyIDispatch(), 
              _prop_map_get_ = {
                  'AddressBar': (...), 
                  'Application': (...), 
                  'Busy': (...), 
                  'Container': (...), 
                  'Document': (...), 
                  'FullName': (...), 
                  'FullScreen': (...), 
                  'HWND': (...), 
                  'Height': (...), 
                  'Left': (...), 
                  'LocationName': (...), 
                  'LocationURL': (...), 
                  'MenuBar': (...), 
                  'Name': (...), 
                  'Offline': (...), 
                  'Parent': (...), 
                  'Path': (...), 
                  'ReadyState': (...), 
                  'RegisterAsBrowser': (...), 
                  'RegisterAsDropTarget': (...), 
                  'Resizable': (...), 
                  'Silent': (...), 
                  'StatusBar': (...), 
                  'StatusText': (...), 
                  'TheaterMode': (...), 
                  'ToolBar': (...), 
                  'Top': (...), 
                  'TopLevelContainer': (...), 
                  'Type': (...), 
                  'Visible': (...), 
                  'Width': (...)
                }, 
              _prop_map_put_ = {
                  'AddressBar': (...), 
                  'FullScreen': (...), 
                  'Height': (...), 
                  'Left': (...), 
                  'MenuBar': (...), 
                  'Offline': (...), 
                  'RegisterAsBrowser': (...), 
                  'RegisterAsDropTarget': (...), 
                  'Resizable': (...), 
                  'Silent': (...), 
                  'StatusBar': (...), 
                  'StatusText': (...), 
                  'TheaterMode': (...), 
                  'ToolBar': (...), 
                  'Top': (...), 
                  'Visible': (...), 
                  'Width': (...)
                }, 
              coclass_clsid = PyIID()
            ), 
          Path = 'C:\\Windows\\', 
          ReadyState = 4, 
          RegisterAsBrowser = False, 
          RegisterAsDropTarget = True, 
          Resizable = True, 
          Silent = False, 
          StatusBar = False, 
          StatusText = '', 
          TheaterMode = False, 
          ToolBar = 1, 
          Top = 0, 
          TopLevelContainer = True, 
          Type = <Attribute error: com_error>, 
          Visible = True, 
          Width = 1920, 
          _oleobj_ = PyIDispatch(), 
          _prop_map_get_ = {
              'AddressBar': (555, 2, (...), (...), 'AddressBar', None), 
              'Application': (200, 2, (...), (...), 'Application', None), 
              'Busy': (212, 2, (...), (...), 'Busy', None), 
              'Container': (202, 2, (...), (...), 'Container', None), 
              'Document': (203, 2, (...), (...), 'Document', None), 
              'FullName': (400, 2, (...), (...), 'FullName', None), 
              'FullScreen': (407, 2, (...), (...), 'FullScreen', None), 
              'HWND': (-515, 2, (...), (...), 'HWND', None), 
              'Height': (209, 2, (...), (...), 'Height', None), 
              'Left': (206, 2, (...), (...), 'Left', None), 
              'LocationName': (210, 2, (...), (...), 'LocationName', None), 
              'LocationURL': (211, 2, (...), (...), 'LocationURL', None), 
              'MenuBar': (406, 2, (...), (...), 'MenuBar', None), 
              'Name': (0, 2, (...), (...), 'Name', None), 
              'Offline': (550, 2, (...), (...), 'Offline', None), 
              'Parent': (201, 2, (...), (...), 'Parent', None), 
              'Path': (401, 2, (...), (...), 'Path', None), 
              'ReadyState': (-525, 2, (...), (...), 'ReadyState', None), 
              'RegisterAsBrowser': (552, 2, (...), (...), 'RegisterAsBrowser', None), 
              'RegisterAsDropTarget': (553, 2, (...), (...), 'RegisterAsDropTarget', None), 
              'Resizable': (556, 2, (...), (...), 'Resizable', None), 
              'Silent': (551, 2, (...), (...), 'Silent', None), 
              'StatusBar': (403, 2, (...), (...), 'StatusBar', None), 
              'StatusText': (404, 2, (...), (...), 'StatusText', None), 
              'TheaterMode': (554, 2, (...), (...), 'TheaterMode', None), 
              'ToolBar': (405, 2, (...), (...), 'ToolBar', None), 
              'Top': (207, 2, (...), (...), 'Top', None), 
              'TopLevelContainer': (204, 2, (...), (...), 'TopLevelContainer', None), 
              'Type': (205, 2, (...), (...), 'Type', None), 
              'Visible': (402, 2, (...), (...), 'Visible', None), 
              'Width': (208, 2, (...), (...), 'Width', None)
            }, 
          _prop_map_put_ = {
              'AddressBar': ((...), (...)), 
              'FullScreen': ((...), (...)), 
              'Height': ((...), (...)), 
              'Left': ((...), (...)), 
              'MenuBar': ((...), (...)), 
              'Offline': ((...), (...)), 
              'RegisterAsBrowser': ((...), (...)), 
              'RegisterAsDropTarget': ((...), (...)), 
              'Resizable': ((...), (...)), 
              'Silent': ((...), (...)), 
              'StatusBar': ((...), (...)), 
              'StatusText': ((...), (...)), 
              'TheaterMode': ((...), (...)), 
              'ToolBar': ((...), (...)), 
              'Top': ((...), (...)), 
              'Visible': ((...), (...)), 
              'Width': ((...), (...))
            }, 
          coclass_clsid = PyIID()
        ), 
      Busy = False, 
      CLSID = PyIID(), 
      Container = None, 
      Document = win32com.client.CDispatch(
          Application = win32com.client.CDispatch(
              Application = win32com.client.CDispatch(
                  Application = win32com.client.CDispatch(...), 
                  Parent = win32com.client.CDispatch(...), 
                  _builtMethods_ = {...}, 
                  _enum_ = None, 
                  _lazydata_ = (...), 
                  _mapCachedItems_ = {...}, 
                  _oleobj_ = PyIDispatch(...), 
                  _olerepr_ = win32com.client.build.LazyDispatchItem(...), 
                  _unicode_to_string_ = None, 
                  _username_ = '<unknown>'
                ), 
              Parent = win32com.client.CDispatch(
                  Application = win32com.client.CDispatch(...), 
                  Parent = win32com.client.CDispatch(...), 
                  _builtMethods_ = {...}, 
                  _enum_ = None, 
                  _lazydata_ = (...), 
                  _mapCachedItems_ = {...}, 
                  _oleobj_ = PyIDispatch(...), 
                  _olerepr_ = win32com.client.build.LazyDispatchItem(...), 
                  _unicode_to_string_ = None, 
                  _username_ = '<unknown>'
                ), 
              _builtMethods_ = {
                  'AddToRecent': win32com.client.dynamic.function(...), 
                  'BrowseForFolder': win32com.client.dynamic.function(...), 
                  'CanStartStopService': win32com.client.dynamic.function(...), 
                  'CascadeWindows': win32com.client.dynamic.function(...), 
                  'ControlPanelItem': win32com.client.dynamic.function(...), 
                  'EjectPC': win32com.client.dynamic.function(...), 
                  'Explore': win32com.client.dynamic.function(...), 
                  'ExplorerPolicy': win32com.client.dynamic.function(...), 
                  'FileRun': win32com.client.dynamic.function(...), 
                  'FindComputer': win32com.client.dynamic.function(...), 
                  'FindFiles': win32com.client.dynamic.function(...), 
                  'FindPrinter': win32com.client.dynamic.function(...), 
                  'GetSetting': win32com.client.dynamic.function(...), 
                  'GetSystemInformation': win32com.client.dynamic.function(...), 
                  'Help': win32com.client.dynamic.function(...), 
                  'IsRestricted': win32com.client.dynamic.function(...), 
                  'IsServiceRunning': win32com.client.dynamic.function(...), 
                  'MinimizeAll': win32com.client.dynamic.function(...), 
                  'NameSpace': win32com.client.dynamic.function(...), 
                  'Open': win32com.client.dynamic.function(...), 
                  'RefreshMenu': win32com.client.dynamic.function(...), 
                  'SearchCommand': win32com.client.dynamic.function(...), 
                  'ServiceStart': win32com.client.dynamic.function(...), 
                  'ServiceStop': win32com.client.dynamic.function(...), 
                  'SetTime': win32com.client.dynamic.function(...), 
                  'ShellExecute': win32com.client.dynamic.function(...), 
                  'ShowBrowserBar': win32com.client.dynamic.function(...), 
                  'ShutdownWindows': win32com.client.dynamic.function(...), 
                  'Suspend': win32com.client.dynamic.function(...), 
                  'TileHorizontally': win32com.client.dynamic.function(...), 
                  'TileVertically': win32com.client.dynamic.function(...), 
                  'ToggleDesktop': win32com.client.dynamic.function(...), 
                  'TrayProperties': win32com.client.dynamic.function(...), 
                  'UndoMinimizeALL': win32com.client.dynamic.function(...), 
                  'WindowSwitcher': win32com.client.dynamic.function(...), 
                  'Windows': win32com.client.dynamic.function(...), 
                  'WindowsSecurity': win32com.client.dynamic.function(...)
                }, 
              _enum_ = None, 
              _lazydata_ = (PyITypeInfo(...), PyITypeComp(...)), 
              _mapCachedItems_ = {}, 
              _oleobj_ = PyIDispatch(), 
              _olerepr_ = win32com.client.build.LazyDispatchItem(
                  bIsDispatch = 0, 
                  bIsSink = 0, 
                  bWritten = 0, 
                  clsid = None, 
                  co_class = None, 
                  defaultDispatchName = None, 
                  doc = None, 
                  hidden = 0, 
                  mapFuncs = {...}, 
                  propMap = {...}, 
                  propMapGet = {...}, 
                  propMapPut = {...}, 
                  python_name = None, 
                  typename = 'LazyDispatchItem'
                ), 
              _unicode_to_string_ = None, 
              _username_ = '<unknown>'
            ), 
          CurrentViewMode = 4, 
          FocusedItem = win32com.client.CDispatch(
              Application = win32com.client.CDispatch(
                  Application = win32com.client.CDispatch(...), 
                  Parent = win32com.client.CDispatch(...), 
                  _builtMethods_ = {...}, 
                  _enum_ = None, 
                  _lazydata_ = (...), 
                  _mapCachedItems_ = {...}, 
                  _oleobj_ = PyIDispatch(...), 
                  _olerepr_ = win32com.client.build.LazyDispatchItem(...), 
                  _unicode_to_string_ = None, 
                  _username_ = '<unknown>'
                ), 
              GetFolder = <Attribute error: com_error>, 
              GetLink = <Attribute error: com_error>, 
              IsBrowsable = False, 
              IsFileSystem = True, 
              IsFolder = False, 
              IsLink = False, 
              ModifyDate = datetime(
                  day = 29, 
                  fold = 0, 
                  hour = 14, 
                  max = datetime(...), 
                  microsecond = 0, 
                  min = datetime(...), 
                  minute = 33, 
                  month = 12, 
                  resolution = timedelta(...), 
                  second = 0, 
                  tzinfo = win32timezone.TimeZoneInfo(...), 
                  year = 2022
                ), 
              Name = '-OPEN-VENV-PROMPT.bat', 
              Parent = win32com.client.CDispatch(
                  Application = win32com.client.CDispatch(...), 
                  HaveToShowWebViewBarricade = False, 
                  OfflineStatus = <Attribute error: com_error>, 
                  Parent = <Attribute error: com_error>, 
                  ParentFolder = win32com.client.CDispatch(...), 
                  Self = win32com.client.CDispatch(...), 
                  ShowWebViewBarricade = False, 
                  Title = 'SaveWindowLayout', 
                  _builtMethods_ = {...}, 
                  _enum_ = None, 
                  _lazydata_ = (...), 
                  _mapCachedItems_ = {...}, 
                  _oleobj_ = PyIDispatch(...), 
                  _olerepr_ = win32com.client.build.LazyDispatchItem(...), 
                  _unicode_to_string_ = None, 
                  _username_ = '<unknown>'
                ), 
              Path = 'D:\\Data\\Prosjekter\\Script...ut\\-OPEN-VENV-PROMPT.bat', 
              Size = 138, 
              Type = 'Windows Batch File', 
              _builtMethods_ = {
                  'ExtendedProperty': win32com.client.dynamic.function(...), 
                  'InvokeVerb': win32com.client.dynamic.function(...), 
                  'InvokeVerbEx': win32com.client.dynamic.function(...), 
                  'Verbs': win32com.client.dynamic.function(...)
                }, 
              _enum_ = None, 
              _lazydata_ = (PyITypeInfo(...), PyITypeComp(...)), 
              _mapCachedItems_ = {}, 
              _oleobj_ = PyIDispatch(), 
              _olerepr_ = win32com.client.build.LazyDispatchItem(
                  bIsDispatch = 0, 
                  bIsSink = 0, 
                  bWritten = 0, 
                  clsid = None, 
                  co_class = None, 
                  defaultDispatchName = 'Name', 
                  doc = None, 
                  hidden = 0, 
                  mapFuncs = {...}, 
                  propMap = {...}, 
                  propMapGet = {...}, 
                  propMapPut = {...}, 
                  python_name = None, 
                  typename = 'LazyDispatchItem'
                ), 
              _unicode_to_string_ = None, 
              _username_ = '<unknown>'
            ), 
          Folder = win32com.client.CDispatch(
              Application = win32com.client.CDispatch(
                  Application = win32com.client.CDispatch(...), 
                  Parent = win32com.client.CDispatch(...), 
                  _builtMethods_ = {...}, 
                  _enum_ = None, 
                  _lazydata_ = (...), 
                  _mapCachedItems_ = {...}, 
                  _oleobj_ = PyIDispatch(...), 
                  _olerepr_ = win32com.client.build.LazyDispatchItem(...), 
                  _unicode_to_string_ = None, 
                  _username_ = '<unknown>'
                ), 
              HaveToShowWebViewBarricade = False, 
              OfflineStatus = <Attribute error: com_error>, 
              Parent = <Attribute error: com_error>, 
              ParentFolder = win32com.client.CDispatch(
                  Application = win32com.client.CDispatch(...), 
                  HaveToShowWebViewBarricade = False, 
                  OfflineStatus = <Attribute error: com_error>, 
                  Parent = <Attribute error: com_error>, 
                  ParentFolder = win32com.client.CDispatch(...), 
                  Self = win32com.client.CDispatch(...), 
                  ShowWebViewBarricade = False, 
                  Title = 'WIP', 
                  _builtMethods_ = {...}, 
                  _enum_ = None, 
                  _lazydata_ = (...), 
                  _mapCachedItems_ = {...}, 
                  _oleobj_ = PyIDispatch(...), 
                  _olerepr_ = win32com.client.build.LazyDispatchItem(...), 
                  _unicode_to_string_ = None, 
                  _username_ = '<unknown>'
                ), 
              Self = win32com.client.CDispatch(
                  Application = win32com.client.CDispatch(...), 
                  GetFolder = win32com.client.CDispatch(...), 
                  GetLink = <Attribute error: com_error>, 
                  IsBrowsable = False, 
                  IsFileSystem = True, 
                  IsFolder = True, 
                  IsLink = False, 
                  ModifyDate = datetime(...), 
                  Name = 'SaveWindowLayout', 
                  Parent = win32com.client.CDispatch(...), 
                  Path = 'D:\\Data\\Prosjekter\\Scripts\\WIP\\SaveWindowLayout', 
                  Size = 0, 
                  Type = 'File folder', 
                  _builtMethods_ = {...}, 
                  _enum_ = None, 
                  _lazydata_ = (...), 
                  _mapCachedItems_ = {...}, 
                  _oleobj_ = PyIDispatch(...), 
                  _olerepr_ = win32com.client.build.LazyDispatchItem(...), 
                  _unicode_to_string_ = None, 
                  _username_ = '<unknown>'
                ), 
              ShowWebViewBarricade = False, 
              Title = 'SaveWindowLayout', 
              _builtMethods_ = {
                  'CopyHere': win32com.client.dynamic.function(...), 
                  'DismissedWebViewBarricade': win32com.client.dynamic.function(...), 
                  'GetDetailsOf': win32com.client.dynamic.function(...), 
                  'Items': win32com.client.dynamic.function(...), 
                  'MoveHere': win32com.client.dynamic.function(...), 
                  'NewFolder': win32com.client.dynamic.function(...), 
                  'ParseName': win32com.client.dynamic.function(...), 
                  'Synchronize': win32com.client.dynamic.function(...)
                }, 
              _enum_ = None, 
              _lazydata_ = (PyITypeInfo(...), PyITypeComp(...)), 
              _mapCachedItems_ = {}, 
              _oleobj_ = PyIDispatch(), 
              _olerepr_ = win32com.client.build.LazyDispatchItem(
                  bIsDispatch = 0, 
                  bIsSink = 0, 
                  bWritten = 0, 
                  clsid = None, 
                  co_class = None, 
                  defaultDispatchName = 'Title', 
                  doc = None, 
                  hidden = 0, 
                  mapFuncs = {...}, 
                  propMap = {...}, 
                  propMapGet = {...}, 
                  propMapPut = {...}, 
                  python_name = None, 
                  typename = 'LazyDispatchItem'
                ), 
              _unicode_to_string_ = None, 
              _username_ = '<unknown>'
            ), 
          FolderFlags = 1092616192, 
          GroupBy = 'System.Null', 
          IconSize = 16, 
          Parent = <Attribute error: com_error>, 
          Script = <Attribute error: com_error>, 
          SortColumns = 'prop:-System.DateModified;', 
          ViewOptions = 170, 
          _builtMethods_ = {
              'FilterView': win32com.client.dynamic.function(), 
              'PopupItemMenu': win32com.client.dynamic.function(), 
              'SelectItem': win32com.client.dynamic.function(), 
              'SelectItemRelative': win32com.client.dynamic.function(), 
              'SelectedItems': win32com.client.dynamic.function()
            }, 
          _enum_ = None, 
          _lazydata_ = (PyITypeInfo(), PyITypeComp()), 
          _mapCachedItems_ = {}, 
          _oleobj_ = PyIDispatch(), 
          _olerepr_ = win32com.client.build.LazyDispatchItem(
              bIsDispatch = 0, 
              bIsSink = 0, 
              bWritten = 0, 
              clsid = None, 
              co_class = None, 
              defaultDispatchName = None, 
              doc = None, 
              hidden = 0, 
              mapFuncs = {
                  'FilterView': win32com.client.build.MapEntry(...), 
                  'PopupItemMenu': win32com.client.build.MapEntry(...), 
                  'SelectItem': win32com.client.build.MapEntry(...), 
                  'SelectItemRelative': win32com.client.build.MapEntry(...), 
                  'SelectedItems': win32com.client.build.MapEntry(...)
                }, 
              propMap = {}, 
              propMapGet = {
                  'Application': win32com.client.build.MapEntry(...), 
                  'CurrentViewMode': win32com.client.build.MapEntry(...), 
                  'FocusedItem': win32com.client.build.MapEntry(...), 
                  'Folder': win32com.client.build.MapEntry(...), 
                  'FolderFlags': win32com.client.build.MapEntry(...), 
                  'GroupBy': win32com.client.build.MapEntry(...), 
                  'IconSize': win32com.client.build.MapEntry(...), 
                  'Parent': win32com.client.build.MapEntry(...), 
                  'Script': win32com.client.build.MapEntry(...), 
                  'SortColumns': win32com.client.build.MapEntry(...), 
                  'ViewOptions': win32com.client.build.MapEntry(...)
                }, 
              propMapPut = {
                  'CurrentViewMode': win32com.client.build.MapEntry(...), 
                  'FolderFlags': win32com.client.build.MapEntry(...), 
                  'GroupBy': win32com.client.build.MapEntry(...), 
                  'IconSize': win32com.client.build.MapEntry(...), 
                  'SortColumns': win32com.client.build.MapEntry(...)
                }, 
              python_name = None, 
              typename = 'LazyDispatchItem'
            ), 
          _unicode_to_string_ = None, 
          _username_ = 'Document'
        ), 
      FullName = 'C:\\Windows\\explorer.exe', 
      FullScreen = False, 
      HWND = 6163636, 
      Height = 443, 
      Left = 1920, 
      LocationName = 'SaveWindowLayout', 
      LocationURL = 'file:///D:/Data/Prosjekte...pts/WIP/SaveWindowLayout', 
      MenuBar = False, 
      Name = 'File Explorer', 
      Offline = False, 
      Parent = win32com.gen_py.EAB22AC0-30C1-11CF-A7EB-0000C05BAE0Bx0x1x1.IWebBrowser2.IWebBrowser2(
          AddressBar = True, 
          Application = win32com.gen_py.EAB22AC0-30C1-11CF-A7EB-0000C05BAE0Bx0x1x1.IWebBrowser2.IWebBrowser2(
              AddressBar = True, 
              Application = win32com.gen_py.EAB22AC0-30C1-11CF-A7EB-0000C05BAE0Bx0x1x1.IWebBrowser2.IWebBrowser2(
                  AddressBar = True, 
                  Application = win32com.gen_py.EAB22AC0-30C1-11CF-A7EB-0000C05BAE0Bx0x1x1.IWebBrowser2.IWebBrowser2(...), 
                  Busy = False, 
                  CLSID = PyIID(...), 
                  Container = None, 
                  Document = win32com.client.CDispatch(...), 
                  FullName = 'C:\\Windows\\explorer.exe', 
                  FullScreen = False, 
                  HWND = 6163636, 
                  Height = 443, 
                  Left = 1920, 
                  LocationName = 'SaveWindowLayout', 
                  LocationURL = 'file:///D:/Data/Prosjekte...pts/WIP/SaveWindowLayout', 
                  MenuBar = False, 
                  Name = 'File Explorer', 
                  Offline = False, 
                  Parent = win32com.gen_py.EAB22AC0-30C1-11CF-A7EB-0000C05BAE0Bx0x1x1.IWebBrowser2.IWebBrowser2(...), 
                  Path = 'C:\\Windows\\', 
                  ReadyState = 4, 
                  RegisterAsBrowser = False, 
                  RegisterAsDropTarget = True, 
                  Resizable = True, 
                  Silent = False, 
                  StatusBar = False, 
                  StatusText = '', 
                  TheaterMode = False, 
                  ToolBar = 1, 
                  Top = 0, 
                  TopLevelContainer = True, 
                  Type = <Attribute error: com_error>, 
                  Visible = True, 
                  Width = 1920, 
                  _oleobj_ = PyIDispatch(...), 
                  _prop_map_get_ = {...}, 
                  _prop_map_put_ = {...}, 
                  coclass_clsid = PyIID(...)
                ), 
              Busy = False, 
              CLSID = PyIID(), 
              Container = None, 
              Document = win32com.client.CDispatch(
                  Application = win32com.client.CDispatch(...), 
                  CurrentViewMode = 4, 
                  FocusedItem = win32com.client.CDispatch(...), 
                  Folder = win32com.client.CDispatch(...), 
                  FolderFlags = 1092616192, 
                  GroupBy = 'System.Null', 
                  IconSize = 16, 
                  Parent = <Attribute error: com_error>, 
                  Script = <Attribute error: com_error>, 
                  SortColumns = 'prop:-System.DateModified;', 
                  ViewOptions = 170, 
                  _builtMethods_ = {...}, 
                  _enum_ = None, 
                  _lazydata_ = (...), 
                  _mapCachedItems_ = {...}, 
                  _oleobj_ = PyIDispatch(...), 
                  _olerepr_ = win32com.client.build.LazyDispatchItem(...), 
                  _unicode_to_string_ = None, 
                  _username_ = 'Document'
                ), 
              FullName = 'C:\\Windows\\explorer.exe', 
              FullScreen = False, 
              HWND = 6163636, 
              Height = 443, 
              Left = 1920, 
              LocationName = 'SaveWindowLayout', 
              LocationURL = 'file:///D:/Data/Prosjekte...pts/WIP/SaveWindowLayout', 
              MenuBar = False, 
              Name = 'File Explorer', 
              Offline = False, 
              Parent = win32com.gen_py.EAB22AC0-30C1-11CF-A7EB-0000C05BAE0Bx0x1x1.IWebBrowser2.IWebBrowser2(
                  AddressBar = True, 
                  Application = win32com.gen_py.EAB22AC0-30C1-11CF-A7EB-0000C05BAE0Bx0x1x1.IWebBrowser2.IWebBrowser2(...), 
                  Busy = False, 
                  CLSID = PyIID(...), 
                  Container = None, 
                  Document = win32com.client.CDispatch(...), 
                  FullName = 'C:\\Windows\\explorer.exe', 
                  FullScreen = False, 
                  HWND = 6163636, 
                  Height = 443, 
                  Left = 1920, 
                  LocationName = 'SaveWindowLayout', 
                  LocationURL = 'file:///D:/Data/Prosjekte...pts/WIP/SaveWindowLayout', 
                  MenuBar = False, 
                  Name = 'File Explorer', 
                  Offline = False, 
                  Parent = win32com.gen_py.EAB22AC0-30C1-11CF-A7EB-0000C05BAE0Bx0x1x1.IWebBrowser2.IWebBrowser2(...), 
                  Path = 'C:\\Windows\\', 
                  ReadyState = 4, 
                  RegisterAsBrowser = False, 
                  RegisterAsDropTarget = True, 
                  Resizable = True, 
                  Silent = False, 
                  StatusBar = False, 
                  StatusText = '', 
                  TheaterMode = False, 
                  ToolBar = 1, 
                  Top = 0, 
                  TopLevelContainer = True, 
                  Type = <Attribute error: com_error>, 
                  Visible = True, 
                  Width = 1920, 
                  _oleobj_ = PyIDispatch(...), 
                  _prop_map_get_ = {...}, 
                  _prop_map_put_ = {...}, 
                  coclass_clsid = PyIID(...)
                ), 
              Path = 'C:\\Windows\\', 
              ReadyState = 4, 
              RegisterAsBrowser = False, 
              RegisterAsDropTarget = True, 
              Resizable = True, 
              Silent = False, 
              StatusBar = False, 
              StatusText = '', 
              TheaterMode = False, 
              ToolBar = 1, 
              Top = 0, 
              TopLevelContainer = True, 
              Type = <Attribute error: com_error>, 
              Visible = True, 
              Width = 1920, 
              _oleobj_ = PyIDispatch(), 
              _prop_map_get_ = {
                  'AddressBar': (...), 
                  'Application': (...), 
                  'Busy': (...), 
                  'Container': (...), 
                  'Document': (...), 
                  'FullName': (...), 
                  'FullScreen': (...), 
                  'HWND': (...), 
                  'Height': (...), 
                  'Left': (...), 
                  'LocationName': (...), 
                  'LocationURL': (...), 
                  'MenuBar': (...), 
                  'Name': (...), 
                  'Offline': (...), 
                  'Parent': (...), 
                  'Path': (...), 
                  'ReadyState': (...), 
                  'RegisterAsBrowser': (...), 
                  'RegisterAsDropTarget': (...), 
                  'Resizable': (...), 
                  'Silent': (...), 
                  'StatusBar': (...), 
                  'StatusText': (...), 
                  'TheaterMode': (...), 
                  'ToolBar': (...), 
                  'Top': (...), 
                  'TopLevelContainer': (...), 
                  'Type': (...), 
                  'Visible': (...), 
                  'Width': (...)
                }, 
              _prop_map_put_ = {
                  'AddressBar': (...), 
                  'FullScreen': (...), 
                  'Height': (...), 
                  'Left': (...), 
                  'MenuBar': (...), 
                  'Offline': (...), 
                  'RegisterAsBrowser': (...), 
                  'RegisterAsDropTarget': (...), 
                  'Resizable': (...), 
                  'Silent': (...), 
                  'StatusBar': (...), 
                  'StatusText': (...), 
                  'TheaterMode': (...), 
                  'ToolBar': (...), 
                  'Top': (...), 
                  'Visible': (...), 
                  'Width': (...)
                }, 
              coclass_clsid = PyIID()
            ), 
          Busy = False, 
          CLSID = PyIID(), 
          Container = None, 
          Document = win32com.client.CDispatch(
              Application = win32com.client.CDispatch(
                  Application = win32com.client.CDispatch(...), 
                  Parent = win32com.client.CDispatch(...), 
                  _builtMethods_ = {...}, 
                  _enum_ = None, 
                  _lazydata_ = (...), 
                  _mapCachedItems_ = {...}, 
                  _oleobj_ = PyIDispatch(...), 
                  _olerepr_ = win32com.client.build.LazyDispatchItem(...), 
                  _unicode_to_string_ = None, 
                  _username_ = '<unknown>'
                ), 
              CurrentViewMode = 4, 
              FocusedItem = win32com.client.CDispatch(
                  Application = win32com.client.CDispatch(...), 
                  GetFolder = <Attribute error: com_error>, 
                  GetLink = <Attribute error: com_error>, 
                  IsBrowsable = False, 
                  IsFileSystem = True, 
                  IsFolder = False, 
                  IsLink = False, 
                  ModifyDate = datetime(...), 
                  Name = '-OPEN-VENV-PROMPT.bat', 
                  Parent = win32com.client.CDispatch(...), 
                  Path = 'D:\\Data\\Prosjekter\\Script...ut\\-OPEN-VENV-PROMPT.bat', 
                  Size = 138, 
                  Type = 'Windows Batch File', 
                  _builtMethods_ = {...}, 
                  _enum_ = None, 
                  _lazydata_ = (...), 
                  _mapCachedItems_ = {...}, 
                  _oleobj_ = PyIDispatch(...), 
                  _olerepr_ = win32com.client.build.LazyDispatchItem(...), 
                  _unicode_to_string_ = None, 
                  _username_ = '<unknown>'
                ), 
              Folder = win32com.client.CDispatch(
                  Application = win32com.client.CDispatch(...), 
                  HaveToShowWebViewBarricade = False, 
                  OfflineStatus = <Attribute error: com_error>, 
                  Parent = <Attribute error: com_error>, 
                  ParentFolder = win32com.client.CDispatch(...), 
                  Self = win32com.client.CDispatch(...), 
                  ShowWebViewBarricade = False, 
                  Title = 'SaveWindowLayout', 
                  _builtMethods_ = {...}, 
                  _enum_ = None, 
                  _lazydata_ = (...), 
                  _mapCachedItems_ = {...}, 
                  _oleobj_ = PyIDispatch(...), 
                  _olerepr_ = win32com.client.build.LazyDispatchItem(...), 
                  _unicode_to_string_ = None, 
                  _username_ = '<unknown>'
                ), 
              FolderFlags = 1092616192, 
              GroupBy = 'System.Null', 
              IconSize = 16, 
              Parent = <Attribute error: com_error>, 
              Script = <Attribute error: com_error>, 
              SortColumns = 'prop:-System.DateModified;', 
              ViewOptions = 170, 
              _builtMethods_ = {
                  'FilterView': win32com.client.dynamic.function(...), 
                  'PopupItemMenu': win32com.client.dynamic.function(...), 
                  'SelectItem': win32com.client.dynamic.function(...), 
                  'SelectItemRelative': win32com.client.dynamic.function(...), 
                  'SelectedItems': win32com.client.dynamic.function(...)
                }, 
              _enum_ = None, 
              _lazydata_ = (PyITypeInfo(...), PyITypeComp(...)), 
              _mapCachedItems_ = {}, 
              _oleobj_ = PyIDispatch(), 
              _olerepr_ = win32com.client.build.LazyDispatchItem(
                  bIsDispatch = 0, 
                  bIsSink = 0, 
                  bWritten = 0, 
                  clsid = None, 
                  co_class = None, 
                  defaultDispatchName = None, 
                  doc = None, 
                  hidden = 0, 
                  mapFuncs = {...}, 
                  propMap = {...}, 
                  propMapGet = {...}, 
                  propMapPut = {...}, 
                  python_name = None, 
                  typename = 'LazyDispatchItem'
                ), 
              _unicode_to_string_ = None, 
              _username_ = 'Document'
            ), 
          FullName = 'C:\\Windows\\explorer.exe', 
          FullScreen = False, 
          HWND = 6163636, 
          Height = 443, 
          Left = 1920, 
          LocationName = 'SaveWindowLayout', 
          LocationURL = 'file:///D:/Data/Prosjekte...pts/WIP/SaveWindowLayout', 
          MenuBar = False, 
          Name = 'File Explorer', 
          Offline = False, 
          Parent = win32com.gen_py.EAB22AC0-30C1-11CF-A7EB-0000C05BAE0Bx0x1x1.IWebBrowser2.IWebBrowser2(
              AddressBar = True, 
              Application = win32com.gen_py.EAB22AC0-30C1-11CF-A7EB-0000C05BAE0Bx0x1x1.IWebBrowser2.IWebBrowser2(
                  AddressBar = True, 
                  Application = win32com.gen_py.EAB22AC0-30C1-11CF-A7EB-0000C05BAE0Bx0x1x1.IWebBrowser2.IWebBrowser2(...), 
                  Busy = False, 
                  CLSID = PyIID(...), 
                  Container = None, 
                  Document = win32com.client.CDispatch(...), 
                  FullName = 'C:\\Windows\\explorer.exe', 
                  FullScreen = False, 
                  HWND = 6163636, 
                  Height = 443, 
                  Left = 1920, 
                  LocationName = 'SaveWindowLayout', 
                  LocationURL = 'file:///D:/Data/Prosjekte...pts/WIP/SaveWindowLayout', 
                  MenuBar = False, 
                  Name = 'File Explorer', 
                  Offline = False, 
                  Parent = win32com.gen_py.EAB22AC0-30C1-11CF-A7EB-0000C05BAE0Bx0x1x1.IWebBrowser2.IWebBrowser2(...), 
                  Path = 'C:\\Windows\\', 
                  ReadyState = 4, 
                  RegisterAsBrowser = False, 
                  RegisterAsDropTarget = True, 
                  Resizable = True, 
                  Silent = False, 
                  StatusBar = False, 
                  StatusText = '', 
                  TheaterMode = False, 
                  ToolBar = 1, 
                  Top = 0, 
                  TopLevelContainer = True, 
                  Type = <Attribute error: com_error>, 
                  Visible = True, 
                  Width = 1920, 
                  _oleobj_ = PyIDispatch(...), 
                  _prop_map_get_ = {...}, 
                  _prop_map_put_ = {...}, 
                  coclass_clsid = PyIID(...)
                ), 
              Busy = False, 
              CLSID = PyIID(), 
              Container = None, 
              Document = win32com.client.CDispatch(
                  Application = win32com.client.CDispatch(...), 
                  CurrentViewMode = 4, 
                  FocusedItem = win32com.client.CDispatch(...), 
                  Folder = win32com.client.CDispatch(...), 
                  FolderFlags = 1092616192, 
                  GroupBy = 'System.Null', 
                  IconSize = 16, 
                  Parent = <Attribute error: com_error>, 
                  Script = <Attribute error: com_error>, 
                  SortColumns = 'prop:-System.DateModified;', 
                  ViewOptions = 170, 
                  _builtMethods_ = {...}, 
                  _enum_ = None, 
                  _lazydata_ = (...), 
                  _mapCachedItems_ = {...}, 
                  _oleobj_ = PyIDispatch(...), 
                  _olerepr_ = win32com.client.build.LazyDispatchItem(...), 
                  _unicode_to_string_ = None, 
                  _username_ = 'Document'
                ), 
              FullName = 'C:\\Windows\\explorer.exe', 
              FullScreen = False, 
              HWND = 6163636, 
              Height = 443, 
              Left = 1920, 
              LocationName = 'SaveWindowLayout', 
              LocationURL = 'file:///D:/Data/Prosjekte...pts/WIP/SaveWindowLayout', 
              MenuBar = False, 
              Name = 'File Explorer', 
              Offline = False, 
              Parent = win32com.gen_py.EAB22AC0-30C1-11CF-A7EB-0000C05BAE0Bx0x1x1.IWebBrowser2.IWebBrowser2(
                  AddressBar = True, 
                  Application = win32com.gen_py.EAB22AC0-30C1-11CF-A7EB-0000C05BAE0Bx0x1x1.IWebBrowser2.IWebBrowser2(...), 
                  Busy = False, 
                  CLSID = PyIID(...), 
                  Container = None, 
                  Document = win32com.client.CDispatch(...), 
                  FullName = 'C:\\Windows\\explorer.exe', 
                  FullScreen = False, 
                  HWND = 6163636, 
                  Height = 443, 
                  Left = 1920, 
                  LocationName = 'SaveWindowLayout', 
                  LocationURL = 'file:///D:/Data/Prosjekte...pts/WIP/SaveWindowLayout', 
                  MenuBar = False, 
                  Name = 'File Explorer', 
                  Offline = False, 
                  Parent = win32com.gen_py.EAB22AC0-30C1-11CF-A7EB-0000C05BAE0Bx0x1x1.IWebBrowser2.IWebBrowser2(...), 
                  Path = 'C:\\Windows\\', 
                  ReadyState = 4, 
                  RegisterAsBrowser = False, 
                  RegisterAsDropTarget = True, 
                  Resizable = True, 
                  Silent = False, 
                  StatusBar = False, 
                  StatusText = '', 
                  TheaterMode = False, 
                  ToolBar = 1, 
                  Top = 0, 
                  TopLevelContainer = True, 
                  Type = <Attribute error: com_error>, 
                  Visible = True, 
                  Width = 1920, 
                  _oleobj_ = PyIDispatch(...), 
                  _prop_map_get_ = {...}, 
                  _prop_map_put_ = {...}, 
                  coclass_clsid = PyIID(...)
                ), 
              Path = 'C:\\Windows\\', 
              ReadyState = 4, 
              RegisterAsBrowser = False, 
              RegisterAsDropTarget = True, 
              Resizable = True, 
              Silent = False, 
              StatusBar = False, 
              StatusText = '', 
              TheaterMode = False, 
              ToolBar = 1, 
              Top = 0, 
              TopLevelContainer = True, 
              Type = <Attribute error: com_error>, 
              Visible = True, 
              Width = 1920, 
              _oleobj_ = PyIDispatch(), 
              _prop_map_get_ = {
                  'AddressBar': (...), 
                  'Application': (...), 
                  'Busy': (...), 
                  'Container': (...), 
                  'Document': (...), 
                  'FullName': (...), 
                  'FullScreen': (...), 
                  'HWND': (...), 
                  'Height': (...), 
                  'Left': (...), 
                  'LocationName': (...), 
                  'LocationURL': (...), 
                  'MenuBar': (...), 
                  'Name': (...), 
                  'Offline': (...), 
                  'Parent': (...), 
                  'Path': (...), 
                  'ReadyState': (...), 
                  'RegisterAsBrowser': (...), 
                  'RegisterAsDropTarget': (...), 
                  'Resizable': (...), 
                  'Silent': (...), 
                  'StatusBar': (...), 
                  'StatusText': (...), 
                  'TheaterMode': (...), 
                  'ToolBar': (...), 
                  'Top': (...), 
                  'TopLevelContainer': (...), 
                  'Type': (...), 
                  'Visible': (...), 
                  'Width': (...)
                }, 
              _prop_map_put_ = {
                  'AddressBar': (...), 
                  'FullScreen': (...), 
                  'Height': (...), 
                  'Left': (...), 
                  'MenuBar': (...), 
                  'Offline': (...), 
                  'RegisterAsBrowser': (...), 
                  'RegisterAsDropTarget': (...), 
                  'Resizable': (...), 
                  'Silent': (...), 
                  'StatusBar': (...), 
                  'StatusText': (...), 
                  'TheaterMode': (...), 
                  'ToolBar': (...), 
                  'Top': (...), 
                  'Visible': (...), 
                  'Width': (...)
                }, 
              coclass_clsid = PyIID()
            ), 
          Path = 'C:\\Windows\\', 
          ReadyState = 4, 
          RegisterAsBrowser = False, 
          RegisterAsDropTarget = True, 
          Resizable = True, 
          Silent = False, 
          StatusBar = False, 
          StatusText = '', 
          TheaterMode = False, 
          ToolBar = 1, 
          Top = 0, 
          TopLevelContainer = True, 
          Type = <Attribute error: com_error>, 
          Visible = True, 
          Width = 1920, 
          _oleobj_ = PyIDispatch(), 
          _prop_map_get_ = {
              'AddressBar': (555, 2, (...), (...), 'AddressBar', None), 
              'Application': (200, 2, (...), (...), 'Application', None), 
              'Busy': (212, 2, (...), (...), 'Busy', None), 
              'Container': (202, 2, (...), (...), 'Container', None), 
              'Document': (203, 2, (...), (...), 'Document', None), 
              'FullName': (400, 2, (...), (...), 'FullName', None), 
              'FullScreen': (407, 2, (...), (...), 'FullScreen', None), 
              'HWND': (-515, 2, (...), (...), 'HWND', None), 
              'Height': (209, 2, (...), (...), 'Height', None), 
              'Left': (206, 2, (...), (...), 'Left', None), 
              'LocationName': (210, 2, (...), (...), 'LocationName', None), 
              'LocationURL': (211, 2, (...), (...), 'LocationURL', None), 
              'MenuBar': (406, 2, (...), (...), 'MenuBar', None), 
              'Name': (0, 2, (...), (...), 'Name', None), 
              'Offline': (550, 2, (...), (...), 'Offline', None), 
              'Parent': (201, 2, (...), (...), 'Parent', None), 
              'Path': (401, 2, (...), (...), 'Path', None), 
              'ReadyState': (-525, 2, (...), (...), 'ReadyState', None), 
              'RegisterAsBrowser': (552, 2, (...), (...), 'RegisterAsBrowser', None), 
              'RegisterAsDropTarget': (553, 2, (...), (...), 'RegisterAsDropTarget', None), 
              'Resizable': (556, 2, (...), (...), 'Resizable', None), 
              'Silent': (551, 2, (...), (...), 'Silent', None), 
              'StatusBar': (403, 2, (...), (...), 'StatusBar', None), 
              'StatusText': (404, 2, (...), (...), 'StatusText', None), 
              'TheaterMode': (554, 2, (...), (...), 'TheaterMode', None), 
              'ToolBar': (405, 2, (...), (...), 'ToolBar', None), 
              'Top': (207, 2, (...), (...), 'Top', None), 
              'TopLevelContainer': (204, 2, (...), (...), 'TopLevelContainer', None), 
              'Type': (205, 2, (...), (...), 'Type', None), 
              'Visible': (402, 2, (...), (...), 'Visible', None), 
              'Width': (208, 2, (...), (...), 'Width', None)
            }, 
          _prop_map_put_ = {
              'AddressBar': ((...), (...)), 
              'FullScreen': ((...), (...)), 
              'Height': ((...), (...)), 
              'Left': ((...), (...)), 
              'MenuBar': ((...), (...)), 
              'Offline': ((...), (...)), 
              'RegisterAsBrowser': ((...), (...)), 
              'RegisterAsDropTarget': ((...), (...)), 
              'Resizable': ((...), (...)), 
              'Silent': ((...), (...)), 
              'StatusBar': ((...), (...)), 
              'StatusText': ((...), (...)), 
              'TheaterMode': ((...), (...)), 
              'ToolBar': ((...), (...)), 
              'Top': ((...), (...)), 
              'Visible': ((...), (...)), 
              'Width': ((...), (...))
            }, 
          coclass_clsid = PyIID()
        ), 
      Path = 'C:\\Windows\\', 
      ReadyState = 4, 
      RegisterAsBrowser = False, 
      RegisterAsDropTarget = True, 
      Resizable = True, 
      Silent = False, 
      StatusBar = False, 
      StatusText = '', 
      TheaterMode = False, 
      ToolBar = 1, 
      Top = 0, 
      TopLevelContainer = True, 
      Type = <Attribute error: com_error>, 
      Visible = True, 
      Width = 1920, 
      _oleobj_ = PyIDispatch(), 
      _prop_map_get_ = {
          'AddressBar': (555, 2, (11, 0), (), 'AddressBar', None), 
          'Application': (200, 2, (9, 0), (), 'Application', None), 
          'Busy': (212, 2, (11, 0), (), 'Busy', None), 
          'Container': (202, 2, (9, 0), (), 'Container', None), 
          'Document': (203, 2, (9, 0), (), 'Document', None), 
          'FullName': (400, 2, (8, 0), (), 'FullName', None), 
          'FullScreen': (407, 2, (11, 0), (), 'FullScreen', None), 
          'HWND': (-515, 2, (20, 0), (), 'HWND', None), 
          'Height': (209, 2, (3, 0), (), 'Height', None), 
          'Left': (206, 2, (3, 0), (), 'Left', None), 
          'LocationName': (210, 2, (8, 0), (), 'LocationName', None), 
          'LocationURL': (211, 2, (8, 0), (), 'LocationURL', None), 
          'MenuBar': (406, 2, (11, 0), (), 'MenuBar', None), 
          'Name': (0, 2, (8, 0), (), 'Name', None), 
          'Offline': (550, 2, (11, 0), (), 'Offline', None), 
          'Parent': (201, 2, (9, 0), (), 'Parent', None), 
          'Path': (401, 2, (8, 0), (), 'Path', None), 
          'ReadyState': (-525, 2, (3, 0), (), 'ReadyState', None), 
          'RegisterAsBrowser': (552, 2, (11, 0), (), 'RegisterAsBrowser', None), 
          'RegisterAsDropTarget': (553, 2, (11, 0), (), 'RegisterAsDropTarget', None), 
          'Resizable': (556, 2, (11, 0), (), 'Resizable', None), 
          'Silent': (551, 2, (11, 0), (), 'Silent', None), 
          'StatusBar': (403, 2, (11, 0), (), 'StatusBar', None), 
          'StatusText': (404, 2, (8, 0), (), 'StatusText', None), 
          'TheaterMode': (554, 2, (11, 0), (), 'TheaterMode', None), 
          'ToolBar': (405, 2, (3, 0), (), 'ToolBar', None), 
          'Top': (207, 2, (3, 0), (), 'Top', None), 
          'TopLevelContainer': (204, 2, (11, 0), (), 'TopLevelContainer', None), 
          'Type': (205, 2, (8, 0), (), 'Type', None), 
          'Visible': (402, 2, (11, 0), (), 'Visible', None), 
          'Width': (208, 2, (3, 0), (), 'Width', None)
        }, 
      _prop_map_put_ = {
          'AddressBar': ((555, 0, 4, 0), ()), 
          'FullScreen': ((407, 0, 4, 0), ()), 
          'Height': ((209, 0, 4, 0), ()), 
          'Left': ((206, 0, 4, 0), ()), 
          'MenuBar': ((406, 0, 4, 0), ()), 
          'Offline': ((550, 0, 4, 0), ()), 
          'RegisterAsBrowser': ((552, 0, 4, 0), ()), 
          'RegisterAsDropTarget': ((553, 0, 4, 0), ()), 
          'Resizable': ((556, 0, 4, 0), ()), 
          'Silent': ((551, 0, 4, 0), ()), 
          'StatusBar': ((403, 0, 4, 0), ()), 
          'StatusText': ((404, 0, 4, 0), ()), 
          'TheaterMode': ((554, 0, 4, 0), ()), 
          'ToolBar': ((405, 0, 4, 0), ()), 
          'Top': ((207, 0, 4, 0), ()), 
          'Visible': ((402, 0, 4, 0), ()), 
          'Width': ((208, 0, 4, 0), ())
        }, 
      coclass_clsid = PyIID()
    ), 
  Busy = False, 
  CLSID = PyIID(), 
  Container = None, 
  Document = win32com.client.CDispatch(
      Application = win32com.client.CDispatch(
          Application = win32com.client.CDispatch(
              Application = win32com.client.CDispatch(
                  Application = win32com.client.CDispatch(...), 
                  Parent = win32com.client.CDispatch(...), 
                  _builtMethods_ = {...}, 
                  _enum_ = None, 
                  _lazydata_ = (...), 
                  _mapCachedItems_ = {...}, 
                  _oleobj_ = PyIDispatch(...), 
                  _olerepr_ = win32com.client.build.LazyDispatchItem(...), 
                  _unicode_to_string_ = None, 
                  _username_ = '<unknown>'
                ), 
              Parent = win32com.client.CDispatch(
                  Application = win32com.client.CDispatch(...), 
                  Parent = win32com.client.CDispatch(...), 
                  _builtMethods_ = {...}, 
                  _enum_ = None, 
                  _lazydata_ = (...), 
                  _mapCachedItems_ = {...}, 
                  _oleobj_ = PyIDispatch(...), 
                  _olerepr_ = win32com.client.build.LazyDispatchItem(...), 
                  _unicode_to_string_ = None, 
                  _username_ = '<unknown>'
                ), 
              _builtMethods_ = {
                  'AddToRecent': win32com.client.dynamic.function(...), 
                  'BrowseForFolder': win32com.client.dynamic.function(...), 
                  'CanStartStopService': win32com.client.dynamic.function(...), 
                  'CascadeWindows': win32com.client.dynamic.function(...), 
                  'ControlPanelItem': win32com.client.dynamic.function(...), 
                  'EjectPC': win32com.client.dynamic.function(...), 
                  'Explore': win32com.client.dynamic.function(...), 
                  'ExplorerPolicy': win32com.client.dynamic.function(...), 
                  'FileRun': win32com.client.dynamic.function(...), 
                  'FindComputer': win32com.client.dynamic.function(...), 
                  'FindFiles': win32com.client.dynamic.function(...), 
                  'FindPrinter': win32com.client.dynamic.function(...), 
                  'GetSetting': win32com.client.dynamic.function(...), 
                  'GetSystemInformation': win32com.client.dynamic.function(...), 
                  'Help': win32com.client.dynamic.function(...), 
                  'IsRestricted': win32com.client.dynamic.function(...), 
                  'IsServiceRunning': win32com.client.dynamic.function(...), 
                  'MinimizeAll': win32com.client.dynamic.function(...), 
                  'NameSpace': win32com.client.dynamic.function(...), 
                  'Open': win32com.client.dynamic.function(...), 
                  'RefreshMenu': win32com.client.dynamic.function(...), 
                  'SearchCommand': win32com.client.dynamic.function(...), 
                  'ServiceStart': win32com.client.dynamic.function(...), 
                  'ServiceStop': win32com.client.dynamic.function(...), 
                  'SetTime': win32com.client.dynamic.function(...), 
                  'ShellExecute': win32com.client.dynamic.function(...), 
                  'ShowBrowserBar': win32com.client.dynamic.function(...), 
                  'ShutdownWindows': win32com.client.dynamic.function(...), 
                  'Suspend': win32com.client.dynamic.function(...), 
                  'TileHorizontally': win32com.client.dynamic.function(...), 
                  'TileVertically': win32com.client.dynamic.function(...), 
                  'ToggleDesktop': win32com.client.dynamic.function(...), 
                  'TrayProperties': win32com.client.dynamic.function(...), 
                  'UndoMinimizeALL': win32com.client.dynamic.function(...), 
                  'WindowSwitcher': win32com.client.dynamic.function(...), 
                  'Windows': win32com.client.dynamic.function(...), 
                  'WindowsSecurity': win32com.client.dynamic.function(...)
                }, 
              _enum_ = None, 
              _lazydata_ = (PyITypeInfo(...), PyITypeComp(...)), 
              _mapCachedItems_ = {}, 
              _oleobj_ = PyIDispatch(), 
              _olerepr_ = win32com.client.build.LazyDispatchItem(
                  bIsDispatch = 0, 
                  bIsSink = 0, 
                  bWritten = 0, 
                  clsid = None, 
                  co_class = None, 
                  defaultDispatchName = None, 
                  doc = None, 
                  hidden = 0, 
                  mapFuncs = {...}, 
                  propMap = {...}, 
                  propMapGet = {...}, 
                  propMapPut = {...}, 
                  python_name = None, 
                  typename = 'LazyDispatchItem'
                ), 
              _unicode_to_string_ = None, 
              _username_ = '<unknown>'
            ), 
          Parent = win32com.client.CDispatch(
              Application = win32com.client.CDispatch(
                  Application = win32com.client.CDispatch(...), 
                  Parent = win32com.client.CDispatch(...), 
                  _builtMethods_ = {...}, 
                  _enum_ = None, 
                  _lazydata_ = (...), 
                  _mapCachedItems_ = {...}, 
                  _oleobj_ = PyIDispatch(...), 
                  _olerepr_ = win32com.client.build.LazyDispatchItem(...), 
                  _unicode_to_string_ = None, 
                  _username_ = '<unknown>'
                ), 
              Parent = win32com.client.CDispatch(
                  Application = win32com.client.CDispatch(...), 
                  Parent = win32com.client.CDispatch(...), 
                  _builtMethods_ = {...}, 
                  _enum_ = None, 
                  _lazydata_ = (...), 
                  _mapCachedItems_ = {...}, 
                  _oleobj_ = PyIDispatch(...), 
                  _olerepr_ = win32com.client.build.LazyDispatchItem(...), 
                  _unicode_to_string_ = None, 
                  _username_ = '<unknown>'
                ), 
              _builtMethods_ = {
                  'AddToRecent': win32com.client.dynamic.function(...), 
                  'BrowseForFolder': win32com.client.dynamic.function(...), 
                  'CanStartStopService': win32com.client.dynamic.function(...), 
                  'CascadeWindows': win32com.client.dynamic.function(...), 
                  'ControlPanelItem': win32com.client.dynamic.function(...), 
                  'EjectPC': win32com.client.dynamic.function(...), 
                  'Explore': win32com.client.dynamic.function(...), 
                  'ExplorerPolicy': win32com.client.dynamic.function(...), 
                  'FileRun': win32com.client.dynamic.function(...), 
                  'FindComputer': win32com.client.dynamic.function(...), 
                  'FindFiles': win32com.client.dynamic.function(...), 
                  'FindPrinter': win32com.client.dynamic.function(...), 
                  'GetSetting': win32com.client.dynamic.function(...), 
                  'GetSystemInformation': win32com.client.dynamic.function(...), 
                  'Help': win32com.client.dynamic.function(...), 
                  'IsRestricted': win32com.client.dynamic.function(...), 
                  'IsServiceRunning': win32com.client.dynamic.function(...), 
                  'MinimizeAll': win32com.client.dynamic.function(...), 
                  'NameSpace': win32com.client.dynamic.function(...), 
                  'Open': win32com.client.dynamic.function(...), 
                  'RefreshMenu': win32com.client.dynamic.function(...), 
                  'SearchCommand': win32com.client.dynamic.function(...), 
                  'ServiceStart': win32com.client.dynamic.function(...), 
                  'ServiceStop': win32com.client.dynamic.function(...), 
                  'SetTime': win32com.client.dynamic.function(...), 
                  'ShellExecute': win32com.client.dynamic.function(...), 
                  'ShowBrowserBar': win32com.client.dynamic.function(...), 
                  'ShutdownWindows': win32com.client.dynamic.function(...), 
                  'Suspend': win32com.client.dynamic.function(...), 
                  'TileHorizontally': win32com.client.dynamic.function(...), 
                  'TileVertically': win32com.client.dynamic.function(...), 
                  'ToggleDesktop': win32com.client.dynamic.function(...), 
                  'TrayProperties': win32com.client.dynamic.function(...), 
                  'UndoMinimizeALL': win32com.client.dynamic.function(...), 
                  'WindowSwitcher': win32com.client.dynamic.function(...), 
                  'Windows': win32com.client.dynamic.function(...), 
                  'WindowsSecurity': win32com.client.dynamic.function(...)
                }, 
              _enum_ = None, 
              _lazydata_ = (PyITypeInfo(...), PyITypeComp(...)), 
              _mapCachedItems_ = {}, 
              _oleobj_ = PyIDispatch(), 
              _olerepr_ = win32com.client.build.LazyDispatchItem(
                  bIsDispatch = 0, 
                  bIsSink = 0, 
                  bWritten = 0, 
                  clsid = None, 
                  co_class = None, 
                  defaultDispatchName = None, 
                  doc = None, 
                  hidden = 0, 
                  mapFuncs = {...}, 
                  propMap = {...}, 
                  propMapGet = {...}, 
                  propMapPut = {...}, 
                  python_name = None, 
                  typename = 'LazyDispatchItem'
                ), 
              _unicode_to_string_ = None, 
              _username_ = '<unknown>'
            ), 
          _builtMethods_ = {
              'AddToRecent': win32com.client.dynamic.function(), 
              'BrowseForFolder': win32com.client.dynamic.function(), 
              'CanStartStopService': win32com.client.dynamic.function(), 
              'CascadeWindows': win32com.client.dynamic.function(), 
              'ControlPanelItem': win32com.client.dynamic.function(), 
              'EjectPC': win32com.client.dynamic.function(), 
              'Explore': win32com.client.dynamic.function(), 
              'ExplorerPolicy': win32com.client.dynamic.function(), 
              'FileRun': win32com.client.dynamic.function(), 
              'FindComputer': win32com.client.dynamic.function(), 
              'FindFiles': win32com.client.dynamic.function(), 
              'FindPrinter': win32com.client.dynamic.function(), 
              'GetSetting': win32com.client.dynamic.function(), 
              'GetSystemInformation': win32com.client.dynamic.function(), 
              'Help': win32com.client.dynamic.function(), 
              'IsRestricted': win32com.client.dynamic.function(), 
              'IsServiceRunning': win32com.client.dynamic.function(), 
              'MinimizeAll': win32com.client.dynamic.function(), 
              'NameSpace': win32com.client.dynamic.function(), 
              'Open': win32com.client.dynamic.function(), 
              'RefreshMenu': win32com.client.dynamic.function(), 
              'SearchCommand': win32com.client.dynamic.function(), 
              'ServiceStart': win32com.client.dynamic.function(), 
              'ServiceStop': win32com.client.dynamic.function(), 
              'SetTime': win32com.client.dynamic.function(), 
              'ShellExecute': win32com.client.dynamic.function(), 
              'ShowBrowserBar': win32com.client.dynamic.function(), 
              'ShutdownWindows': win32com.client.dynamic.function(), 
              'Suspend': win32com.client.dynamic.function(), 
              'TileHorizontally': win32com.client.dynamic.function(), 
              'TileVertically': win32com.client.dynamic.function(), 
              'ToggleDesktop': win32com.client.dynamic.function(), 
              'TrayProperties': win32com.client.dynamic.function(), 
              'UndoMinimizeALL': win32com.client.dynamic.function(), 
              'WindowSwitcher': win32com.client.dynamic.function(), 
              'Windows': win32com.client.dynamic.function(), 
              'WindowsSecurity': win32com.client.dynamic.function()
            }, 
          _enum_ = None, 
          _lazydata_ = (PyITypeInfo(), PyITypeComp()), 
          _mapCachedItems_ = {}, 
          _oleobj_ = PyIDispatch(), 
          _olerepr_ = win32com.client.build.LazyDispatchItem(
              bIsDispatch = 0, 
              bIsSink = 0, 
              bWritten = 0, 
              clsid = None, 
              co_class = None, 
              defaultDispatchName = None, 
              doc = None, 
              hidden = 0, 
              mapFuncs = {
                  'AddToRecent': win32com.client.build.MapEntry(...), 
                  'BrowseForFolder': win32com.client.build.MapEntry(...), 
                  'CanStartStopService': win32com.client.build.MapEntry(...), 
                  'CascadeWindows': win32com.client.build.MapEntry(...), 
                  'ControlPanelItem': win32com.client.build.MapEntry(...), 
                  'EjectPC': win32com.client.build.MapEntry(...), 
                  'Explore': win32com.client.build.MapEntry(...), 
                  'ExplorerPolicy': win32com.client.build.MapEntry(...), 
                  'FileRun': win32com.client.build.MapEntry(...), 
                  'FindComputer': win32com.client.build.MapEntry(...), 
                  'FindFiles': win32com.client.build.MapEntry(...), 
                  'FindPrinter': win32com.client.build.MapEntry(...), 
                  'GetSetting': win32com.client.build.MapEntry(...), 
                  'GetSystemInformation': win32com.client.build.MapEntry(...), 
                  'Help': win32com.client.build.MapEntry(...), 
                  'IsRestricted': win32com.client.build.MapEntry(...), 
                  'IsServiceRunning': win32com.client.build.MapEntry(...), 
                  'MinimizeAll': win32com.client.build.MapEntry(...), 
                  'NameSpace': win32com.client.build.MapEntry(...), 
                  'Open': win32com.client.build.MapEntry(...), 
                  'RefreshMenu': win32com.client.build.MapEntry(...), 
                  'SearchCommand': win32com.client.build.MapEntry(...), 
                  'ServiceStart': win32com.client.build.MapEntry(...), 
                  'ServiceStop': win32com.client.build.MapEntry(...), 
                  'SetTime': win32com.client.build.MapEntry(...), 
                  'ShellExecute': win32com.client.build.MapEntry(...), 
                  'ShowBrowserBar': win32com.client.build.MapEntry(...), 
                  'ShutdownWindows': win32com.client.build.MapEntry(...), 
                  'Suspend': win32com.client.build.MapEntry(...), 
                  'TileHorizontally': win32com.client.build.MapEntry(...), 
                  'TileVertically': win32com.client.build.MapEntry(...), 
                  'ToggleDesktop': win32com.client.build.MapEntry(...), 
                  'TrayProperties': win32com.client.build.MapEntry(...), 
                  'UndoMinimizeALL': win32com.client.build.MapEntry(...), 
                  'WindowSwitcher': win32com.client.build.MapEntry(...), 
                  'Windows': win32com.client.build.MapEntry(...), 
                  'WindowsSecurity': win32com.client.build.MapEntry(...)
                }, 
              propMap = {}, 
              propMapGet = {
                  'Application': win32com.client.build.MapEntry(...), 
                  'Parent': win32com.client.build.MapEntry(...)
                }, 
              propMapPut = {}, 
              python_name = None, 
              typename = 'LazyDispatchItem'
            ), 
          _unicode_to_string_ = None, 
          _username_ = '<unknown>'
        ), 
      CurrentViewMode = 4, 
      FocusedItem = win32com.client.CDispatch(
          Application = win32com.client.CDispatch(
              Application = win32com.client.CDispatch(
                  Application = win32com.client.CDispatch(...), 
                  Parent = win32com.client.CDispatch(...), 
                  _builtMethods_ = {...}, 
                  _enum_ = None, 
                  _lazydata_ = (...), 
                  _mapCachedItems_ = {...}, 
                  _oleobj_ = PyIDispatch(...), 
                  _olerepr_ = win32com.client.build.LazyDispatchItem(...), 
                  _unicode_to_string_ = None, 
                  _username_ = '<unknown>'
                ), 
              Parent = win32com.client.CDispatch(
                  Application = win32com.client.CDispatch(...), 
                  Parent = win32com.client.CDispatch(...), 
                  _builtMethods_ = {...}, 
                  _enum_ = None, 
                  _lazydata_ = (...), 
                  _mapCachedItems_ = {...}, 
                  _oleobj_ = PyIDispatch(...), 
                  _olerepr_ = win32com.client.build.LazyDispatchItem(...), 
                  _unicode_to_string_ = None, 
                  _username_ = '<unknown>'
                ), 
              _builtMethods_ = {
                  'AddToRecent': win32com.client.dynamic.function(...), 
                  'BrowseForFolder': win32com.client.dynamic.function(...), 
                  'CanStartStopService': win32com.client.dynamic.function(...), 
                  'CascadeWindows': win32com.client.dynamic.function(...), 
                  'ControlPanelItem': win32com.client.dynamic.function(...), 
                  'EjectPC': win32com.client.dynamic.function(...), 
                  'Explore': win32com.client.dynamic.function(...), 
                  'ExplorerPolicy': win32com.client.dynamic.function(...), 
                  'FileRun': win32com.client.dynamic.function(...), 
                  'FindComputer': win32com.client.dynamic.function(...), 
                  'FindFiles': win32com.client.dynamic.function(...), 
                  'FindPrinter': win32com.client.dynamic.function(...), 
                  'GetSetting': win32com.client.dynamic.function(...), 
                  'GetSystemInformation': win32com.client.dynamic.function(...), 
                  'Help': win32com.client.dynamic.function(...), 
                  'IsRestricted': win32com.client.dynamic.function(...), 
                  'IsServiceRunning': win32com.client.dynamic.function(...), 
                  'MinimizeAll': win32com.client.dynamic.function(...), 
                  'NameSpace': win32com.client.dynamic.function(...), 
                  'Open': win32com.client.dynamic.function(...), 
                  'RefreshMenu': win32com.client.dynamic.function(...), 
                  'SearchCommand': win32com.client.dynamic.function(...), 
                  'ServiceStart': win32com.client.dynamic.function(...), 
                  'ServiceStop': win32com.client.dynamic.function(...), 
                  'SetTime': win32com.client.dynamic.function(...), 
                  'ShellExecute': win32com.client.dynamic.function(...), 
                  'ShowBrowserBar': win32com.client.dynamic.function(...), 
                  'ShutdownWindows': win32com.client.dynamic.function(...), 
                  'Suspend': win32com.client.dynamic.function(...), 
                  'TileHorizontally': win32com.client.dynamic.function(...), 
                  'TileVertically': win32com.client.dynamic.function(...), 
                  'ToggleDesktop': win32com.client.dynamic.function(...), 
                  'TrayProperties': win32com.client.dynamic.function(...), 
                  'UndoMinimizeALL': win32com.client.dynamic.function(...), 
                  'WindowSwitcher': win32com.client.dynamic.function(...), 
                  'Windows': win32com.client.dynamic.function(...), 
                  'WindowsSecurity': win32com.client.dynamic.function(...)
                }, 
              _enum_ = None, 
              _lazydata_ = (PyITypeInfo(...), PyITypeComp(...)), 
              _mapCachedItems_ = {}, 
              _oleobj_ = PyIDispatch(), 
              _olerepr_ = win32com.client.build.LazyDispatchItem(
                  bIsDispatch = 0, 
                  bIsSink = 0, 
                  bWritten = 0, 
                  clsid = None, 
                  co_class = None, 
                  defaultDispatchName = None, 
                  doc = None, 
                  hidden = 0, 
                  mapFuncs = {...}, 
                  propMap = {...}, 
                  propMapGet = {...}, 
                  propMapPut = {...}, 
                  python_name = None, 
                  typename = 'LazyDispatchItem'
                ), 
              _unicode_to_string_ = None, 
              _username_ = '<unknown>'
            ), 
          GetFolder = <Attribute error: com_error>, 
          GetLink = <Attribute error: com_error>, 
          IsBrowsable = False, 
          IsFileSystem = True, 
          IsFolder = False, 
          IsLink = False, 
          ModifyDate = datetime(
              day = 29, 
              fold = 0, 
              hour = 14, 
              max = datetime(
                  day = 31, 
                  fold = 0, 
                  hour = 23, 
                  max = datetime(...), 
                  microsecond = 999999, 
                  min = datetime(...), 
                  minute = 59, 
                  month = 12, 
                  resolution = timedelta(...), 
                  second = 59, 
                  tzinfo = None, 
                  year = 9999
                ), 
              microsecond = 0, 
              min = datetime(
                  day = 1, 
                  fold = 0, 
                  hour = 0, 
                  max = datetime(...), 
                  microsecond = 0, 
                  min = datetime(...), 
                  minute = 0, 
                  month = 1, 
                  resolution = timedelta(...), 
                  second = 0, 
                  tzinfo = None, 
                  year = 1
                ), 
              minute = 33, 
              month = 12, 
              resolution = timedelta(
                  days = 0, 
                  max = timedelta(...), 
                  microseconds = 1, 
                  min = timedelta(...), 
                  resolution = timedelta(...), 
                  seconds = 0
                ), 
              second = 0, 
              tzinfo = win32timezone.TimeZoneInfo(
                  _tzutc = win32timezone.TimeZoneInfo(...), 
                  daylightName = 'GMT Daylight Time', 
                  displayName = '(UTC+00:00) Dublin, Edinburgh, Lisbon, London', 
                  fixedStandardTime = True, 
                  standardName = 'GMT Standard Time', 
                  staticInfo = win32timezone.TimeZoneDefinition(...), 
                  timeZoneName = 'GMT Standard Time', 
                  tzRegKey = 'SOFTWARE\\Microsoft\\Window...urrentVersion\\Time Zones'
                ), 
              year = 2022
            ), 
          Name = '-OPEN-VENV-PROMPT.bat', 
          Parent = win32com.client.CDispatch(
              Application = win32com.client.CDispatch(
                  Application = win32com.client.CDispatch(...), 
                  Parent = win32com.client.CDispatch(...), 
                  _builtMethods_ = {...}, 
                  _enum_ = None, 
                  _lazydata_ = (...), 
                  _mapCachedItems_ = {...}, 
                  _oleobj_ = PyIDispatch(...), 
                  _olerepr_ = win32com.client.build.LazyDispatchItem(...), 
                  _unicode_to_string_ = None, 
                  _username_ = '<unknown>'
                ), 
              HaveToShowWebViewBarricade = False, 
              OfflineStatus = <Attribute error: com_error>, 
              Parent = <Attribute error: com_error>, 
              ParentFolder = win32com.client.CDispatch(
                  Application = win32com.client.CDispatch(...), 
                  HaveToShowWebViewBarricade = False, 
                  OfflineStatus = <Attribute error: com_error>, 
                  Parent = <Attribute error: com_error>, 
                  ParentFolder = win32com.client.CDispatch(...), 
                  Self = win32com.client.CDispatch(...), 
                  ShowWebViewBarricade = False, 
                  Title = 'WIP', 
                  _builtMethods_ = {...}, 
                  _enum_ = None, 
                  _lazydata_ = (...), 
                  _mapCachedItems_ = {...}, 
                  _oleobj_ = PyIDispatch(...), 
                  _olerepr_ = win32com.client.build.LazyDispatchItem(...), 
                  _unicode_to_string_ = None, 
                  _username_ = '<unknown>'
                ), 
              Self = win32com.client.CDispatch(
                  Application = win32com.client.CDispatch(...), 
                  GetFolder = win32com.client.CDispatch(...), 
                  GetLink = <Attribute error: com_error>, 
                  IsBrowsable = False, 
                  IsFileSystem = True, 
                  IsFolder = True, 
                  IsLink = False, 
                  ModifyDate = datetime(...), 
                  Name = 'SaveWindowLayout', 
                  Parent = win32com.client.CDispatch(...), 
                  Path = 'D:\\Data\\Prosjekter\\Scripts\\WIP\\SaveWindowLayout', 
                  Size = 0, 
                  Type = 'File folder', 
                  _builtMethods_ = {...}, 
                  _enum_ = None, 
                  _lazydata_ = (...), 
                  _mapCachedItems_ = {...}, 
                  _oleobj_ = PyIDispatch(...), 
                  _olerepr_ = win32com.client.build.LazyDispatchItem(...), 
                  _unicode_to_string_ = None, 
                  _username_ = '<unknown>'
                ), 
              ShowWebViewBarricade = False, 
              Title = 'SaveWindowLayout', 
              _builtMethods_ = {
                  'CopyHere': win32com.client.dynamic.function(...), 
                  'DismissedWebViewBarricade': win32com.client.dynamic.function(...), 
                  'GetDetailsOf': win32com.client.dynamic.function(...), 
                  'Items': win32com.client.dynamic.function(...), 
                  'MoveHere': win32com.client.dynamic.function(...), 
                  'NewFolder': win32com.client.dynamic.function(...), 
                  'ParseName': win32com.client.dynamic.function(...), 
                  'Synchronize': win32com.client.dynamic.function(...)
                }, 
              _enum_ = None, 
              _lazydata_ = (PyITypeInfo(...), PyITypeComp(...)), 
              _mapCachedItems_ = {}, 
              _oleobj_ = PyIDispatch(), 
              _olerepr_ = win32com.client.build.LazyDispatchItem(
                  bIsDispatch = 0, 
                  bIsSink = 0, 
                  bWritten = 0, 
                  clsid = None, 
                  co_class = None, 
                  defaultDispatchName = 'Title', 
                  doc = None, 
                  hidden = 0, 
                  mapFuncs = {...}, 
                  propMap = {...}, 
                  propMapGet = {...}, 
                  propMapPut = {...}, 
                  python_name = None, 
                  typename = 'LazyDispatchItem'
                ), 
              _unicode_to_string_ = None, 
              _username_ = '<unknown>'
            ), 
          Path = 'D:\\Data\\Prosjekter\\Script...ut\\-OPEN-VENV-PROMPT.bat', 
          Size = 138, 
          Type = 'Windows Batch File', 
          _builtMethods_ = {
              'ExtendedProperty': win32com.client.dynamic.function(), 
              'InvokeVerb': win32com.client.dynamic.function(), 
              'InvokeVerbEx': win32com.client.dynamic.function(), 
              'Verbs': win32com.client.dynamic.function()
            }, 
          _enum_ = None, 
          _lazydata_ = (PyITypeInfo(), PyITypeComp()), 
          _mapCachedItems_ = {}, 
          _oleobj_ = PyIDispatch(), 
          _olerepr_ = win32com.client.build.LazyDispatchItem(
              bIsDispatch = 0, 
              bIsSink = 0, 
              bWritten = 0, 
              clsid = None, 
              co_class = None, 
              defaultDispatchName = 'Name', 
              doc = None, 
              hidden = 0, 
              mapFuncs = {
                  'ExtendedProperty': win32com.client.build.MapEntry(...), 
                  'InvokeVerb': win32com.client.build.MapEntry(...), 
                  'InvokeVerbEx': win32com.client.build.MapEntry(...), 
                  'Verbs': win32com.client.build.MapEntry(...)
                }, 
              propMap = {}, 
              propMapGet = {
                  'Application': win32com.client.build.MapEntry(...), 
                  'GetFolder': win32com.client.build.MapEntry(...), 
                  'GetLink': win32com.client.build.MapEntry(...), 
                  'IsBrowsable': win32com.client.build.MapEntry(...), 
                  'IsFileSystem': win32com.client.build.MapEntry(...), 
                  'IsFolder': win32com.client.build.MapEntry(...), 
                  'IsLink': win32com.client.build.MapEntry(...), 
                  'ModifyDate': win32com.client.build.MapEntry(...), 
                  'Name': win32com.client.build.MapEntry(...), 
                  'Parent': win32com.client.build.MapEntry(...), 
                  'Path': win32com.client.build.MapEntry(...), 
                  'Size': win32com.client.build.MapEntry(...), 
                  'Type': win32com.client.build.MapEntry(...)
                }, 
              propMapPut = {
                  'ModifyDate': win32com.client.build.MapEntry(...), 
                  'Name': win32com.client.build.MapEntry(...)
                }, 
              python_name = None, 
              typename = 'LazyDispatchItem'
            ), 
          _unicode_to_string_ = None, 
          _username_ = '<unknown>'
        ), 
      Folder = win32com.client.CDispatch(
          Application = win32com.client.CDispatch(
              Application = win32com.client.CDispatch(
                  Application = win32com.client.CDispatch(...), 
                  Parent = win32com.client.CDispatch(...), 
                  _builtMethods_ = {...}, 
                  _enum_ = None, 
                  _lazydata_ = (...), 
                  _mapCachedItems_ = {...}, 
                  _oleobj_ = PyIDispatch(...), 
                  _olerepr_ = win32com.client.build.LazyDispatchItem(...), 
                  _unicode_to_string_ = None, 
                  _username_ = '<unknown>'
                ), 
              Parent = win32com.client.CDispatch(
                  Application = win32com.client.CDispatch(...), 
                  Parent = win32com.client.CDispatch(...), 
                  _builtMethods_ = {...}, 
                  _enum_ = None, 
                  _lazydata_ = (...), 
                  _mapCachedItems_ = {...}, 
                  _oleobj_ = PyIDispatch(...), 
                  _olerepr_ = win32com.client.build.LazyDispatchItem(...), 
                  _unicode_to_string_ = None, 
                  _username_ = '<unknown>'
                ), 
              _builtMethods_ = {
                  'AddToRecent': win32com.client.dynamic.function(...), 
                  'BrowseForFolder': win32com.client.dynamic.function(...), 
                  'CanStartStopService': win32com.client.dynamic.function(...), 
                  'CascadeWindows': win32com.client.dynamic.function(...), 
                  'ControlPanelItem': win32com.client.dynamic.function(...), 
                  'EjectPC': win32com.client.dynamic.function(...), 
                  'Explore': win32com.client.dynamic.function(...), 
                  'ExplorerPolicy': win32com.client.dynamic.function(...), 
                  'FileRun': win32com.client.dynamic.function(...), 
                  'FindComputer': win32com.client.dynamic.function(...), 
                  'FindFiles': win32com.client.dynamic.function(...), 
                  'FindPrinter': win32com.client.dynamic.function(...), 
                  'GetSetting': win32com.client.dynamic.function(...), 
                  'GetSystemInformation': win32com.client.dynamic.function(...), 
                  'Help': win32com.client.dynamic.function(...), 
                  'IsRestricted': win32com.client.dynamic.function(...), 
                  'IsServiceRunning': win32com.client.dynamic.function(...), 
                  'MinimizeAll': win32com.client.dynamic.function(...), 
                  'NameSpace': win32com.client.dynamic.function(...), 
                  'Open': win32com.client.dynamic.function(...), 
                  'RefreshMenu': win32com.client.dynamic.function(...), 
                  'SearchCommand': win32com.client.dynamic.function(...), 
                  'ServiceStart': win32com.client.dynamic.function(...), 
                  'ServiceStop': win32com.client.dynamic.function(...), 
                  'SetTime': win32com.client.dynamic.function(...), 
                  'ShellExecute': win32com.client.dynamic.function(...), 
                  'ShowBrowserBar': win32com.client.dynamic.function(...), 
                  'ShutdownWindows': win32com.client.dynamic.function(...), 
                  'Suspend': win32com.client.dynamic.function(...), 
                  'TileHorizontally': win32com.client.dynamic.function(...), 
                  'TileVertically': win32com.client.dynamic.function(...), 
                  'ToggleDesktop': win32com.client.dynamic.function(...), 
                  'TrayProperties': win32com.client.dynamic.function(...), 
                  'UndoMinimizeALL': win32com.client.dynamic.function(...), 
                  'WindowSwitcher': win32com.client.dynamic.function(...), 
                  'Windows': win32com.client.dynamic.function(...), 
                  'WindowsSecurity': win32com.client.dynamic.function(...)
                }, 
              _enum_ = None, 
              _lazydata_ = (PyITypeInfo(...), PyITypeComp(...)), 
              _mapCachedItems_ = {}, 
              _oleobj_ = PyIDispatch(), 
              _olerepr_ = win32com.client.build.LazyDispatchItem(
                  bIsDispatch = 0, 
                  bIsSink = 0, 
                  bWritten = 0, 
                  clsid = None, 
                  co_class = None, 
                  defaultDispatchName = None, 
                  doc = None, 
                  hidden = 0, 
                  mapFuncs = {...}, 
                  propMap = {...}, 
                  propMapGet = {...}, 
                  propMapPut = {...}, 
                  python_name = None, 
                  typename = 'LazyDispatchItem'
                ), 
              _unicode_to_string_ = None, 
              _username_ = '<unknown>'
            ), 
          HaveToShowWebViewBarricade = False, 
          OfflineStatus = <Attribute error: com_error>, 
          Parent = <Attribute error: com_error>, 
          ParentFolder = win32com.client.CDispatch(
              Application = win32com.client.CDispatch(
                  Application = win32com.client.CDispatch(...), 
                  Parent = win32com.client.CDispatch(...), 
                  _builtMethods_ = {...}, 
                  _enum_ = None, 
                  _lazydata_ = (...), 
                  _mapCachedItems_ = {...}, 
                  _oleobj_ = PyIDispatch(...), 
                  _olerepr_ = win32com.client.build.LazyDispatchItem(...), 
                  _unicode_to_string_ = None, 
                  _username_ = '<unknown>'
                ), 
              HaveToShowWebViewBarricade = False, 
              OfflineStatus = <Attribute error: com_error>, 
              Parent = <Attribute error: com_error>, 
              ParentFolder = win32com.client.CDispatch(
                  Application = win32com.client.CDispatch(...), 
                  HaveToShowWebViewBarricade = False, 
                  OfflineStatus = <Attribute error: com_error>, 
                  Parent = <Attribute error: com_error>, 
                  ParentFolder = win32com.client.CDispatch(...), 
                  Self = win32com.client.CDispatch(...), 
                  ShowWebViewBarricade = False, 
                  Title = 'Scripts', 
                  _builtMethods_ = {...}, 
                  _enum_ = None, 
                  _lazydata_ = (...), 
                  _mapCachedItems_ = {...}, 
                  _oleobj_ = PyIDispatch(...), 
                  _olerepr_ = win32com.client.build.LazyDispatchItem(...), 
                  _unicode_to_string_ = None, 
                  _username_ = '<unknown>'
                ), 
              Self = win32com.client.CDispatch(
                  Application = win32com.client.CDispatch(...), 
                  GetFolder = win32com.client.CDispatch(...), 
                  GetLink = <Attribute error: com_error>, 
                  IsBrowsable = False, 
                  IsFileSystem = True, 
                  IsFolder = True, 
                  IsLink = False, 
                  ModifyDate = datetime(...), 
                  Name = 'WIP', 
                  Parent = win32com.client.CDispatch(...), 
                  Path = 'D:\\Data\\Prosjekter\\Scripts\\WIP', 
                  Size = 0, 
                  Type = 'File folder', 
                  _builtMethods_ = {...}, 
                  _enum_ = None, 
                  _lazydata_ = (...), 
                  _mapCachedItems_ = {...}, 
                  _oleobj_ = PyIDispatch(...), 
                  _olerepr_ = win32com.client.build.LazyDispatchItem(...), 
                  _unicode_to_string_ = None, 
                  _username_ = '<unknown>'
                ), 
              ShowWebViewBarricade = False, 
              Title = 'WIP', 
              _builtMethods_ = {
                  'CopyHere': win32com.client.dynamic.function(...), 
                  'DismissedWebViewBarricade': win32com.client.dynamic.function(...), 
                  'GetDetailsOf': win32com.client.dynamic.function(...), 
                  'Items': win32com.client.dynamic.function(...), 
                  'MoveHere': win32com.client.dynamic.function(...), 
                  'NewFolder': win32com.client.dynamic.function(...), 
                  'ParseName': win32com.client.dynamic.function(...), 
                  'Synchronize': win32com.client.dynamic.function(...)
                }, 
              _enum_ = None, 
              _lazydata_ = (PyITypeInfo(...), PyITypeComp(...)), 
              _mapCachedItems_ = {}, 
              _oleobj_ = PyIDispatch(), 
              _olerepr_ = win32com.client.build.LazyDispatchItem(
                  bIsDispatch = 0, 
                  bIsSink = 0, 
                  bWritten = 0, 
                  clsid = None, 
                  co_class = None, 
                  defaultDispatchName = 'Title', 
                  doc = None, 
                  hidden = 0, 
                  mapFuncs = {...}, 
                  propMap = {...}, 
                  propMapGet = {...}, 
                  propMapPut = {...}, 
                  python_name = None, 
                  typename = 'LazyDispatchItem'
                ), 
              _unicode_to_string_ = None, 
              _username_ = '<unknown>'
            ), 
          Self = win32com.client.CDispatch(
              Application = win32com.client.CDispatch(
                  Application = win32com.client.CDispatch(...), 
                  Parent = win32com.client.CDispatch(...), 
                  _builtMethods_ = {...}, 
                  _enum_ = None, 
                  _lazydata_ = (...), 
                  _mapCachedItems_ = {...}, 
                  _oleobj_ = PyIDispatch(...), 
                  _olerepr_ = win32com.client.build.LazyDispatchItem(...), 
                  _unicode_to_string_ = None, 
                  _username_ = '<unknown>'
                ), 
              GetFolder = win32com.client.CDispatch(
                  Application = win32com.client.CDispatch(...), 
                  HaveToShowWebViewBarricade = False, 
                  OfflineStatus = <Attribute error: com_error>, 
                  Parent = <Attribute error: com_error>, 
                  ParentFolder = win32com.client.CDispatch(...), 
                  Self = win32com.client.CDispatch(...), 
                  ShowWebViewBarricade = False, 
                  Title = 'SaveWindowLayout', 
                  _builtMethods_ = {...}, 
                  _enum_ = None, 
                  _lazydata_ = (...), 
                  _mapCachedItems_ = {...}, 
                  _oleobj_ = PyIDispatch(...), 
                  _olerepr_ = win32com.client.build.LazyDispatchItem(...), 
                  _unicode_to_string_ = None, 
                  _username_ = '<unknown>'
                ), 
              GetLink = <Attribute error: com_error>, 
              IsBrowsable = False, 
              IsFileSystem = True, 
              IsFolder = True, 
              IsLink = False, 
              ModifyDate = datetime(
                  day = 11, 
                  fold = 0, 
                  hour = 11, 
                  max = datetime(...), 
                  microsecond = 0, 
                  min = datetime(...), 
                  minute = 11, 
                  month = 1, 
                  resolution = timedelta(...), 
                  second = 14, 
                  tzinfo = win32timezone.TimeZoneInfo(...), 
                  year = 2023
                ), 
              Name = 'SaveWindowLayout', 
              Parent = win32com.client.CDispatch(
                  Application = win32com.client.CDispatch(...), 
                  HaveToShowWebViewBarricade = False, 
                  OfflineStatus = <Attribute error: com_error>, 
                  Parent = <Attribute error: com_error>, 
                  ParentFolder = win32com.client.CDispatch(...), 
                  Self = win32com.client.CDispatch(...), 
                  ShowWebViewBarricade = False, 
                  Title = 'WIP', 
                  _builtMethods_ = {...}, 
                  _enum_ = None, 
                  _lazydata_ = (...), 
                  _mapCachedItems_ = {...}, 
                  _oleobj_ = PyIDispatch(...), 
                  _olerepr_ = win32com.client.build.LazyDispatchItem(...), 
                  _unicode_to_string_ = None, 
                  _username_ = '<unknown>'
                ), 
              Path = 'D:\\Data\\Prosjekter\\Scripts\\WIP\\SaveWindowLayout', 
              Size = 0, 
              Type = 'File folder', 
              _builtMethods_ = {
                  'ExtendedProperty': win32com.client.dynamic.function(...), 
                  'InvokeVerb': win32com.client.dynamic.function(...), 
                  'InvokeVerbEx': win32com.client.dynamic.function(...), 
                  'Verbs': win32com.client.dynamic.function(...)
                }, 
              _enum_ = None, 
              _lazydata_ = (PyITypeInfo(...), PyITypeComp(...)), 
              _mapCachedItems_ = {}, 
              _oleobj_ = PyIDispatch(), 
              _olerepr_ = win32com.client.build.LazyDispatchItem(
                  bIsDispatch = 0, 
                  bIsSink = 0, 
                  bWritten = 0, 
                  clsid = None, 
                  co_class = None, 
                  defaultDispatchName = 'Name', 
                  doc = None, 
                  hidden = 0, 
                  mapFuncs = {...}, 
                  propMap = {...}, 
                  propMapGet = {...}, 
                  propMapPut = {...}, 
                  python_name = None, 
                  typename = 'LazyDispatchItem'
                ), 
              _unicode_to_string_ = None, 
              _username_ = '<unknown>'
            ), 
          ShowWebViewBarricade = False, 
          Title = 'SaveWindowLayout', 
          _builtMethods_ = {
              'CopyHere': win32com.client.dynamic.function(), 
              'DismissedWebViewBarricade': win32com.client.dynamic.function(), 
              'GetDetailsOf': win32com.client.dynamic.function(), 
              'Items': win32com.client.dynamic.function(), 
              'MoveHere': win32com.client.dynamic.function(), 
              'NewFolder': win32com.client.dynamic.function(), 
              'ParseName': win32com.client.dynamic.function(), 
              'Synchronize': win32com.client.dynamic.function()
            }, 
          _enum_ = None, 
          _lazydata_ = (PyITypeInfo(), PyITypeComp()), 
          _mapCachedItems_ = {}, 
          _oleobj_ = PyIDispatch(), 
          _olerepr_ = win32com.client.build.LazyDispatchItem(
              bIsDispatch = 0, 
              bIsSink = 0, 
              bWritten = 0, 
              clsid = None, 
              co_class = None, 
              defaultDispatchName = 'Title', 
              doc = None, 
              hidden = 0, 
              mapFuncs = {
                  'CopyHere': win32com.client.build.MapEntry(...), 
                  'DismissedWebViewBarricade': win32com.client.build.MapEntry(...), 
                  'GetDetailsOf': win32com.client.build.MapEntry(...), 
                  'Items': win32com.client.build.MapEntry(...), 
                  'MoveHere': win32com.client.build.MapEntry(...), 
                  'NewFolder': win32com.client.build.MapEntry(...), 
                  'ParseName': win32com.client.build.MapEntry(...), 
                  'Synchronize': win32com.client.build.MapEntry(...)
                }, 
              propMap = {}, 
              propMapGet = {
                  'Application': win32com.client.build.MapEntry(...), 
                  'HaveToShowWebViewBarricade': win32com.client.build.MapEntry(...), 
                  'OfflineStatus': win32com.client.build.MapEntry(...), 
                  'Parent': win32com.client.build.MapEntry(...), 
                  'ParentFolder': win32com.client.build.MapEntry(...), 
                  'Self': win32com.client.build.MapEntry(...), 
                  'ShowWebViewBarricade': win32com.client.build.MapEntry(...), 
                  'Title': win32com.client.build.MapEntry(...)
                }, 
              propMapPut = {'ShowWebViewBarricade': win32com.client.build.MapEntry(...)}, 
              python_name = None, 
              typename = 'LazyDispatchItem'
            ), 
          _unicode_to_string_ = None, 
          _username_ = '<unknown>'
        ), 
      FolderFlags = 1092616192, 
      GroupBy = 'System.Null', 
      IconSize = 16, 
      Parent = <Attribute error: com_error>, 
      Script = <Attribute error: com_error>, 
      SortColumns = 'prop:-System.DateModified;', 
      ViewOptions = 170, 
      _builtMethods_ = {
          'FilterView': win32com.client.dynamic.function(), 
          'PopupItemMenu': win32com.client.dynamic.function(), 
          'SelectItem': win32com.client.dynamic.function(), 
          'SelectItemRelative': win32com.client.dynamic.function(), 
          'SelectedItems': win32com.client.dynamic.function()
        }, 
      _enum_ = None, 
      _lazydata_ = (PyITypeInfo(), PyITypeComp()), 
      _mapCachedItems_ = {}, 
      _oleobj_ = PyIDispatch(), 
      _olerepr_ = win32com.client.build.LazyDispatchItem(
          bIsDispatch = 0, 
          bIsSink = 0, 
          bWritten = 0, 
          clsid = None, 
          co_class = None, 
          defaultDispatchName = None, 
          doc = None, 
          hidden = 0, 
          mapFuncs = {
              'FilterView': win32com.client.build.MapEntry(
                  desc = None, 
                  dispid = 1610874888, 
                  doc = None, 
                  hidden = 0, 
                  names = (...), 
                  resultCLSID = PyIID(...), 
                  resultDocumentation = None, 
                  wasProperty = 0
                ), 
              'PopupItemMenu': win32com.client.build.MapEntry(
                  desc = None, 
                  dispid = 1610743814, 
                  doc = None, 
                  hidden = 0, 
                  names = (...), 
                  resultCLSID = PyIID(...), 
                  resultDocumentation = None, 
                  wasProperty = 0
                ), 
              'SelectItem': win32com.client.build.MapEntry(
                  desc = None, 
                  dispid = 1610743813, 
                  doc = None, 
                  hidden = 0, 
                  names = (...), 
                  resultCLSID = PyIID(...), 
                  resultDocumentation = None, 
                  wasProperty = 0
                ), 
              'SelectItemRelative': win32com.client.build.MapEntry(
                  desc = None, 
                  dispid = 1610809346, 
                  doc = None, 
                  hidden = 0, 
                  names = (...), 
                  resultCLSID = PyIID(...), 
                  resultDocumentation = None, 
                  wasProperty = 0
                ), 
              'SelectedItems': win32com.client.build.MapEntry(
                  desc = None, 
                  dispid = 1610743811, 
                  doc = None, 
                  hidden = 0, 
                  names = (...), 
                  resultCLSID = PyIID(...), 
                  resultDocumentation = None, 
                  wasProperty = 0
                )
            }, 
          propMap = {}, 
          propMapGet = {
              'Application': win32com.client.build.MapEntry(
                  desc = PyFUNCDESC(...), 
                  dispid = 1610743808, 
                  doc = None, 
                  hidden = False, 
                  names = (...), 
                  resultCLSID = None, 
                  resultDocumentation = None, 
                  wasProperty = 0
                ), 
              'CurrentViewMode': win32com.client.build.MapEntry(
                  desc = PyFUNCDESC(...), 
                  dispid = 1610809344, 
                  doc = None, 
                  hidden = False, 
                  names = (...), 
                  resultCLSID = None, 
                  resultDocumentation = None, 
                  wasProperty = 0
                ), 
              'FocusedItem': win32com.client.build.MapEntry(
                  desc = PyFUNCDESC(...), 
                  dispid = 1610743812, 
                  doc = None, 
                  hidden = False, 
                  names = (...), 
                  resultCLSID = None, 
                  resultDocumentation = None, 
                  wasProperty = 0
                ), 
              'Folder': win32com.client.build.MapEntry(
                  desc = PyFUNCDESC(...), 
                  dispid = 1610743810, 
                  doc = None, 
                  hidden = False, 
                  names = (...), 
                  resultCLSID = None, 
                  resultDocumentation = None, 
                  wasProperty = 0
                ), 
              'FolderFlags': win32com.client.build.MapEntry(
                  desc = PyFUNCDESC(...), 
                  dispid = 1610874882, 
                  doc = None, 
                  hidden = False, 
                  names = (...), 
                  resultCLSID = None, 
                  resultDocumentation = None, 
                  wasProperty = 0
                ), 
              'GroupBy': win32com.client.build.MapEntry(
                  desc = PyFUNCDESC(...), 
                  dispid = 1610874880, 
                  doc = None, 
                  hidden = False, 
                  names = (...), 
                  resultCLSID = None, 
                  resultDocumentation = None, 
                  wasProperty = 0
                ), 
              'IconSize': win32com.client.build.MapEntry(
                  desc = PyFUNCDESC(...), 
                  dispid = 1610874886, 
                  doc = None, 
                  hidden = False, 
                  names = (...), 
                  resultCLSID = None, 
                  resultDocumentation = None, 
                  wasProperty = 0
                ), 
              'Parent': win32com.client.build.MapEntry(
                  desc = PyFUNCDESC(...), 
                  dispid = 1610743809, 
                  doc = None, 
                  hidden = False, 
                  names = (...), 
                  resultCLSID = None, 
                  resultDocumentation = None, 
                  wasProperty = 0
                ), 
              'Script': win32com.client.build.MapEntry(
                  desc = PyFUNCDESC(...), 
                  dispid = 1610743815, 
                  doc = None, 
                  hidden = False, 
                  names = (...), 
                  resultCLSID = None, 
                  resultDocumentation = None, 
                  wasProperty = 0
                ), 
              'SortColumns': win32com.client.build.MapEntry(
                  desc = PyFUNCDESC(...), 
                  dispid = 1610874884, 
                  doc = None, 
                  hidden = False, 
                  names = (...), 
                  resultCLSID = None, 
                  resultDocumentation = None, 
                  wasProperty = 0
                ), 
              'ViewOptions': win32com.client.build.MapEntry(
                  desc = PyFUNCDESC(...), 
                  dispid = 1610743816, 
                  doc = None, 
                  hidden = False, 
                  names = (...), 
                  resultCLSID = None, 
                  resultDocumentation = None, 
                  wasProperty = 0
                )
            }, 
          propMapPut = {
              'CurrentViewMode': win32com.client.build.MapEntry(
                  desc = PyFUNCDESC(...), 
                  dispid = 1610809344, 
                  doc = None, 
                  hidden = False, 
                  names = (...), 
                  resultCLSID = None, 
                  resultDocumentation = None, 
                  wasProperty = 0
                ), 
              'FolderFlags': win32com.client.build.MapEntry(
                  desc = PyFUNCDESC(...), 
                  dispid = 1610874882, 
                  doc = None, 
                  hidden = False, 
                  names = (...), 
                  resultCLSID = None, 
                  resultDocumentation = None, 
                  wasProperty = 0
                ), 
              'GroupBy': win32com.client.build.MapEntry(
                  desc = PyFUNCDESC(...), 
                  dispid = 1610874880, 
                  doc = None, 
                  hidden = False, 
                  names = (...), 
                  resultCLSID = None, 
                  resultDocumentation = None, 
                  wasProperty = 0
                ), 
              'IconSize': win32com.client.build.MapEntry(
                  desc = PyFUNCDESC(...), 
                  dispid = 1610874886, 
                  doc = None, 
                  hidden = False, 
                  names = (...), 
                  resultCLSID = None, 
                  resultDocumentation = None, 
                  wasProperty = 0
                ), 
              'SortColumns': win32com.client.build.MapEntry(
                  desc = PyFUNCDESC(...), 
                  dispid = 1610874884, 
                  doc = None, 
                  hidden = False, 
                  names = (...), 
                  resultCLSID = None, 
                  resultDocumentation = None, 
                  wasProperty = 0
                )
            }, 
          python_name = None, 
          typename = 'LazyDispatchItem'
        ), 
      _unicode_to_string_ = None, 
      _username_ = 'Document'
    ), 
  FullName = 'C:\\Windows\\explorer.exe', 
  FullScreen = False, 
  HWND = 6163636, 
  Height = 443, 
  Left = 1920, 
  LocationName = 'SaveWindowLayout', 
  LocationURL = 'file:///D:/Data/Prosjekte...pts/WIP/SaveWindowLayout', 
  MenuBar = False, 
  Name = 'File Explorer', 
  Offline = False, 
  Parent = win32com.gen_py.EAB22AC0-30C1-11CF-A7EB-0000C05BAE0Bx0x1x1.IWebBrowser2.IWebBrowser2(
      AddressBar = True, 
      Application = win32com.gen_py.EAB22AC0-30C1-11CF-A7EB-0000C05BAE0Bx0x1x1.IWebBrowser2.IWebBrowser2(
          AddressBar = True, 
          Application = win32com.gen_py.EAB22AC0-30C1-11CF-A7EB-0000C05BAE0Bx0x1x1.IWebBrowser2.IWebBrowser2(
              AddressBar = True, 
              Application = win32com.gen_py.EAB22AC0-30C1-11CF-A7EB-0000C05BAE0Bx0x1x1.IWebBrowser2.IWebBrowser2(
                  AddressBar = True, 
                  Application = win32com.gen_py.EAB22AC0-30C1-11CF-A7EB-0000C05BAE0Bx0x1x1.IWebBrowser2.IWebBrowser2(...), 
                  Busy = False, 
                  CLSID = PyIID(...), 
                  Container = None, 
                  Document = win32com.client.CDispatch(...), 
                  FullName = 'C:\\Windows\\explorer.exe', 
                  FullScreen = False, 
                  HWND = 6163636, 
                  Height = 443, 
                  Left = 1920, 
                  LocationName = 'SaveWindowLayout', 
                  LocationURL = 'file:///D:/Data/Prosjekte...pts/WIP/SaveWindowLayout', 
                  MenuBar = False, 
                  Name = 'File Explorer', 
                  Offline = False, 
                  Parent = win32com.gen_py.EAB22AC0-30C1-11CF-A7EB-0000C05BAE0Bx0x1x1.IWebBrowser2.IWebBrowser2(...), 
                  Path = 'C:\\Windows\\', 
                  ReadyState = 4, 
                  RegisterAsBrowser = False, 
                  RegisterAsDropTarget = True, 
                  Resizable = True, 
                  Silent = False, 
                  StatusBar = False, 
                  StatusText = '', 
                  TheaterMode = False, 
                  ToolBar = 1, 
                  Top = 0, 
                  TopLevelContainer = True, 
                  Type = <Attribute error: com_error>, 
                  Visible = True, 
                  Width = 1920, 
                  _oleobj_ = PyIDispatch(...), 
                  _prop_map_get_ = {...}, 
                  _prop_map_put_ = {...}, 
                  coclass_clsid = PyIID(...)
                ), 
              Busy = False, 
              CLSID = PyIID(), 
              Container = None, 
              Document = win32com.client.CDispatch(
                  Application = win32com.client.CDispatch(...), 
                  CurrentViewMode = 4, 
                  FocusedItem = win32com.client.CDispatch(...), 
                  Folder = win32com.client.CDispatch(...), 
                  FolderFlags = 1092616192, 
                  GroupBy = 'System.Null', 
                  IconSize = 16, 
                  Parent = <Attribute error: com_error>, 
                  Script = <Attribute error: com_error>, 
                  SortColumns = 'prop:-System.DateModified;', 
                  ViewOptions = 170, 
                  _builtMethods_ = {...}, 
                  _enum_ = None, 
                  _lazydata_ = (...), 
                  _mapCachedItems_ = {...}, 
                  _oleobj_ = PyIDispatch(...), 
                  _olerepr_ = win32com.client.build.LazyDispatchItem(...), 
                  _unicode_to_string_ = None, 
                  _username_ = 'Document'
                ), 
              FullName = 'C:\\Windows\\explorer.exe', 
              FullScreen = False, 
              HWND = 6163636, 
              Height = 443, 
              Left = 1920, 
              LocationName = 'SaveWindowLayout', 
              LocationURL = 'file:///D:/Data/Prosjekte...pts/WIP/SaveWindowLayout', 
              MenuBar = False, 
              Name = 'File Explorer', 
              Offline = False, 
              Parent = win32com.gen_py.EAB22AC0-30C1-11CF-A7EB-0000C05BAE0Bx0x1x1.IWebBrowser2.IWebBrowser2(
                  AddressBar = True, 
                  Application = win32com.gen_py.EAB22AC0-30C1-11CF-A7EB-0000C05BAE0Bx0x1x1.IWebBrowser2.IWebBrowser2(...), 
                  Busy = False, 
                  CLSID = PyIID(...), 
                  Container = None, 
                  Document = win32com.client.CDispatch(...), 
                  FullName = 'C:\\Windows\\explorer.exe', 
                  FullScreen = False, 
                  HWND = 6163636, 
                  Height = 443, 
                  Left = 1920, 
                  LocationName = 'SaveWindowLayout', 
                  LocationURL = 'file:///D:/Data/Prosjekte...pts/WIP/SaveWindowLayout', 
                  MenuBar = False, 
                  Name = 'File Explorer', 
                  Offline = False, 
                  Parent = win32com.gen_py.EAB22AC0-30C1-11CF-A7EB-0000C05BAE0Bx0x1x1.IWebBrowser2.IWebBrowser2(...), 
                  Path = 'C:\\Windows\\', 
                  ReadyState = 4, 
                  RegisterAsBrowser = False, 
                  RegisterAsDropTarget = True, 
                  Resizable = True, 
                  Silent = False, 
                  StatusBar = False, 
                  StatusText = '', 
                  TheaterMode = False, 
                  ToolBar = 1, 
                  Top = 0, 
                  TopLevelContainer = True, 
                  Type = <Attribute error: com_error>, 
                  Visible = True, 
                  Width = 1920, 
                  _oleobj_ = PyIDispatch(...), 
                  _prop_map_get_ = {...}, 
                  _prop_map_put_ = {...}, 
                  coclass_clsid = PyIID(...)
                ), 
              Path = 'C:\\Windows\\', 
              ReadyState = 4, 
              RegisterAsBrowser = False, 
              RegisterAsDropTarget = True, 
              Resizable = True, 
              Silent = False, 
              StatusBar = False, 
              StatusText = '', 
              TheaterMode = False, 
              ToolBar = 1, 
              Top = 0, 
              TopLevelContainer = True, 
              Type = <Attribute error: com_error>, 
              Visible = True, 
              Width = 1920, 
              _oleobj_ = PyIDispatch(), 
              _prop_map_get_ = {
                  'AddressBar': (...), 
                  'Application': (...), 
                  'Busy': (...), 
                  'Container': (...), 
                  'Document': (...), 
                  'FullName': (...), 
                  'FullScreen': (...), 
                  'HWND': (...), 
                  'Height': (...), 
                  'Left': (...), 
                  'LocationName': (...), 
                  'LocationURL': (...), 
                  'MenuBar': (...), 
                  'Name': (...), 
                  'Offline': (...), 
                  'Parent': (...), 
                  'Path': (...), 
                  'ReadyState': (...), 
                  'RegisterAsBrowser': (...), 
                  'RegisterAsDropTarget': (...), 
                  'Resizable': (...), 
                  'Silent': (...), 
                  'StatusBar': (...), 
                  'StatusText': (...), 
                  'TheaterMode': (...), 
                  'ToolBar': (...), 
                  'Top': (...), 
                  'TopLevelContainer': (...), 
                  'Type': (...), 
                  'Visible': (...), 
                  'Width': (...)
                }, 
              _prop_map_put_ = {
                  'AddressBar': (...), 
                  'FullScreen': (...), 
                  'Height': (...), 
                  'Left': (...), 
                  'MenuBar': (...), 
                  'Offline': (...), 
                  'RegisterAsBrowser': (...), 
                  'RegisterAsDropTarget': (...), 
                  'Resizable': (...), 
                  'Silent': (...), 
                  'StatusBar': (...), 
                  'StatusText': (...), 
                  'TheaterMode': (...), 
                  'ToolBar': (...), 
                  'Top': (...), 
                  'Visible': (...), 
                  'Width': (...)
                }, 
              coclass_clsid = PyIID()
            ), 
          Busy = False, 
          CLSID = PyIID(), 
          Container = None, 
          Document = win32com.client.CDispatch(
              Application = win32com.client.CDispatch(
                  Application = win32com.client.CDispatch(...), 
                  Parent = win32com.client.CDispatch(...), 
                  _builtMethods_ = {...}, 
                  _enum_ = None, 
                  _lazydata_ = (...), 
                  _mapCachedItems_ = {...}, 
                  _oleobj_ = PyIDispatch(...), 
                  _olerepr_ = win32com.client.build.LazyDispatchItem(...), 
                  _unicode_to_string_ = None, 
                  _username_ = '<unknown>'
                ), 
              CurrentViewMode = 4, 
              FocusedItem = win32com.client.CDispatch(
                  Application = win32com.client.CDispatch(...), 
                  GetFolder = <Attribute error: com_error>, 
                  GetLink = <Attribute error: com_error>, 
                  IsBrowsable = False, 
                  IsFileSystem = True, 
                  IsFolder = False, 
                  IsLink = False, 
                  ModifyDate = datetime(...), 
                  Name = '-OPEN-VENV-PROMPT.bat', 
                  Parent = win32com.client.CDispatch(...), 
                  Path = 'D:\\Data\\Prosjekter\\Script...ut\\-OPEN-VENV-PROMPT.bat', 
                  Size = 138, 
                  Type = 'Windows Batch File', 
                  _builtMethods_ = {...}, 
                  _enum_ = None, 
                  _lazydata_ = (...), 
                  _mapCachedItems_ = {...}, 
                  _oleobj_ = PyIDispatch(...), 
                  _olerepr_ = win32com.client.build.LazyDispatchItem(...), 
                  _unicode_to_string_ = None, 
                  _username_ = '<unknown>'
                ), 
              Folder = win32com.client.CDispatch(
                  Application = win32com.client.CDispatch(...), 
                  HaveToShowWebViewBarricade = False, 
                  OfflineStatus = <Attribute error: com_error>, 
                  Parent = <Attribute error: com_error>, 
                  ParentFolder = win32com.client.CDispatch(...), 
                  Self = win32com.client.CDispatch(...), 
                  ShowWebViewBarricade = False, 
                  Title = 'SaveWindowLayout', 
                  _builtMethods_ = {...}, 
                  _enum_ = None, 
                  _lazydata_ = (...), 
                  _mapCachedItems_ = {...}, 
                  _oleobj_ = PyIDispatch(...), 
                  _olerepr_ = win32com.client.build.LazyDispatchItem(...), 
                  _unicode_to_string_ = None, 
                  _username_ = '<unknown>'
                ), 
              FolderFlags = 1092616192, 
              GroupBy = 'System.Null', 
              IconSize = 16, 
              Parent = <Attribute error: com_error>, 
              Script = <Attribute error: com_error>, 
              SortColumns = 'prop:-System.DateModified;', 
              ViewOptions = 170, 
              _builtMethods_ = {
                  'FilterView': win32com.client.dynamic.function(...), 
                  'PopupItemMenu': win32com.client.dynamic.function(...), 
                  'SelectItem': win32com.client.dynamic.function(...), 
                  'SelectItemRelative': win32com.client.dynamic.function(...), 
                  'SelectedItems': win32com.client.dynamic.function(...)
                }, 
              _enum_ = None, 
              _lazydata_ = (PyITypeInfo(...), PyITypeComp(...)), 
              _mapCachedItems_ = {}, 
              _oleobj_ = PyIDispatch(), 
              _olerepr_ = win32com.client.build.LazyDispatchItem(
                  bIsDispatch = 0, 
                  bIsSink = 0, 
                  bWritten = 0, 
                  clsid = None, 
                  co_class = None, 
                  defaultDispatchName = None, 
                  doc = None, 
                  hidden = 0, 
                  mapFuncs = {...}, 
                  propMap = {...}, 
                  propMapGet = {...}, 
                  propMapPut = {...}, 
                  python_name = None, 
                  typename = 'LazyDispatchItem'
                ), 
              _unicode_to_string_ = None, 
              _username_ = 'Document'
            ), 
          FullName = 'C:\\Windows\\explorer.exe', 
          FullScreen = False, 
          HWND = 6163636, 
          Height = 443, 
          Left = 1920, 
          LocationName = 'SaveWindowLayout', 
          LocationURL = 'file:///D:/Data/Prosjekte...pts/WIP/SaveWindowLayout', 
          MenuBar = False, 
          Name = 'File Explorer', 
          Offline = False, 
          Parent = win32com.gen_py.EAB22AC0-30C1-11CF-A7EB-0000C05BAE0Bx0x1x1.IWebBrowser2.IWebBrowser2(
              AddressBar = True, 
              Application = win32com.gen_py.EAB22AC0-30C1-11CF-A7EB-0000C05BAE0Bx0x1x1.IWebBrowser2.IWebBrowser2(
                  AddressBar = True, 
                  Application = win32com.gen_py.EAB22AC0-30C1-11CF-A7EB-0000C05BAE0Bx0x1x1.IWebBrowser2.IWebBrowser2(...), 
                  Busy = False, 
                  CLSID = PyIID(...), 
                  Container = None, 
                  Document = win32com.client.CDispatch(...), 
                  FullName = 'C:\\Windows\\explorer.exe', 
                  FullScreen = False, 
                  HWND = 6163636, 
                  Height = 443, 
                  Left = 1920, 
                  LocationName = 'SaveWindowLayout', 
                  LocationURL = 'file:///D:/Data/Prosjekte...pts/WIP/SaveWindowLayout', 
                  MenuBar = False, 
                  Name = 'File Explorer', 
                  Offline = False, 
                  Parent = win32com.gen_py.EAB22AC0-30C1-11CF-A7EB-0000C05BAE0Bx0x1x1.IWebBrowser2.IWebBrowser2(...), 
                  Path = 'C:\\Windows\\', 
                  ReadyState = 4, 
                  RegisterAsBrowser = False, 
                  RegisterAsDropTarget = True, 
                  Resizable = True, 
                  Silent = False, 
                  StatusBar = False, 
                  StatusText = '', 
                  TheaterMode = False, 
                  ToolBar = 1, 
                  Top = 0, 
                  TopLevelContainer = True, 
                  Type = <Attribute error: com_error>, 
                  Visible = True, 
                  Width = 1920, 
                  _oleobj_ = PyIDispatch(...), 
                  _prop_map_get_ = {...}, 
                  _prop_map_put_ = {...}, 
                  coclass_clsid = PyIID(...)
                ), 
              Busy = False, 
              CLSID = PyIID(), 
              Container = None, 
              Document = win32com.client.CDispatch(
                  Application = win32com.client.CDispatch(...), 
                  CurrentViewMode = 4, 
                  FocusedItem = win32com.client.CDispatch(...), 
                  Folder = win32com.client.CDispatch(...), 
                  FolderFlags = 1092616192, 
                  GroupBy = 'System.Null', 
                  IconSize = 16, 
                  Parent = <Attribute error: com_error>, 
                  Script = <Attribute error: com_error>, 
                  SortColumns = 'prop:-System.DateModified;', 
                  ViewOptions = 170, 
                  _builtMethods_ = {...}, 
                  _enum_ = None, 
                  _lazydata_ = (...), 
                  _mapCachedItems_ = {...}, 
                  _oleobj_ = PyIDispatch(...), 
                  _olerepr_ = win32com.client.build.LazyDispatchItem(...), 
                  _unicode_to_string_ = None, 
                  _username_ = 'Document'
                ), 
              FullName = 'C:\\Windows\\explorer.exe', 
              FullScreen = False, 
              HWND = 6163636, 
              Height = 443, 
              Left = 1920, 
              LocationName = 'SaveWindowLayout', 
              LocationURL = 'file:///D:/Data/Prosjekte...pts/WIP/SaveWindowLayout', 
              MenuBar = False, 
              Name = 'File Explorer', 
              Offline = False, 
              Parent = win32com.gen_py.EAB22AC0-30C1-11CF-A7EB-0000C05BAE0Bx0x1x1.IWebBrowser2.IWebBrowser2(
                  AddressBar = True, 
                  Application = win32com.gen_py.EAB22AC0-30C1-11CF-A7EB-0000C05BAE0Bx0x1x1.IWebBrowser2.IWebBrowser2(...), 
                  Busy = False, 
                  CLSID = PyIID(...), 
                  Container = None, 
                  Document = win32com.client.CDispatch(...), 
                  FullName = 'C:\\Windows\\explorer.exe', 
                  FullScreen = False, 
                  HWND = 6163636, 
                  Height = 443, 
                  Left = 1920, 
                  LocationName = 'SaveWindowLayout', 
                  LocationURL = 'file:///D:/Data/Prosjekte...pts/WIP/SaveWindowLayout', 
                  MenuBar = False, 
                  Name = 'File Explorer', 
                  Offline = False, 
                  Parent = win32com.gen_py.EAB22AC0-30C1-11CF-A7EB-0000C05BAE0Bx0x1x1.IWebBrowser2.IWebBrowser2(...), 
                  Path = 'C:\\Windows\\', 
                  ReadyState = 4, 
                  RegisterAsBrowser = False, 
                  RegisterAsDropTarget = True, 
                  Resizable = True, 
                  Silent = False, 
                  StatusBar = False, 
                  StatusText = '', 
                  TheaterMode = False, 
                  ToolBar = 1, 
                  Top = 0, 
                  TopLevelContainer = True, 
                  Type = <Attribute error: com_error>, 
                  Visible = True, 
                  Width = 1920, 
                  _oleobj_ = PyIDispatch(...), 
                  _prop_map_get_ = {...}, 
                  _prop_map_put_ = {...}, 
                  coclass_clsid = PyIID(...)
                ), 
              Path = 'C:\\Windows\\', 
              ReadyState = 4, 
              RegisterAsBrowser = False, 
              RegisterAsDropTarget = True, 
              Resizable = True, 
              Silent = False, 
              StatusBar = False, 
              StatusText = '', 
              TheaterMode = False, 
              ToolBar = 1, 
              Top = 0, 
              TopLevelContainer = True, 
              Type = <Attribute error: com_error>, 
              Visible = True, 
              Width = 1920, 
              _oleobj_ = PyIDispatch(), 
              _prop_map_get_ = {
                  'AddressBar': (...), 
                  'Application': (...), 
                  'Busy': (...), 
                  'Container': (...), 
                  'Document': (...), 
                  'FullName': (...), 
                  'FullScreen': (...), 
                  'HWND': (...), 
                  'Height': (...), 
                  'Left': (...), 
                  'LocationName': (...), 
                  'LocationURL': (...), 
                  'MenuBar': (...), 
                  'Name': (...), 
                  'Offline': (...), 
                  'Parent': (...), 
                  'Path': (...), 
                  'ReadyState': (...), 
                  'RegisterAsBrowser': (...), 
                  'RegisterAsDropTarget': (...), 
                  'Resizable': (...), 
                  'Silent': (...), 
                  'StatusBar': (...), 
                  'StatusText': (...), 
                  'TheaterMode': (...), 
                  'ToolBar': (...), 
                  'Top': (...), 
                  'TopLevelContainer': (...), 
                  'Type': (...), 
                  'Visible': (...), 
                  'Width': (...)
                }, 
              _prop_map_put_ = {
                  'AddressBar': (...), 
                  'FullScreen': (...), 
                  'Height': (...), 
                  'Left': (...), 
                  'MenuBar': (...), 
                  'Offline': (...), 
                  'RegisterAsBrowser': (...), 
                  'RegisterAsDropTarget': (...), 
                  'Resizable': (...), 
                  'Silent': (...), 
                  'StatusBar': (...), 
                  'StatusText': (...), 
                  'TheaterMode': (...), 
                  'ToolBar': (...), 
                  'Top': (...), 
                  'Visible': (...), 
                  'Width': (...)
                }, 
              coclass_clsid = PyIID()
            ), 
          Path = 'C:\\Windows\\', 
          ReadyState = 4, 
          RegisterAsBrowser = False, 
          RegisterAsDropTarget = True, 
          Resizable = True, 
          Silent = False, 
          StatusBar = False, 
          StatusText = '', 
          TheaterMode = False, 
          ToolBar = 1, 
          Top = 0, 
          TopLevelContainer = True, 
          Type = <Attribute error: com_error>, 
          Visible = True, 
          Width = 1920, 
          _oleobj_ = PyIDispatch(), 
          _prop_map_get_ = {
              'AddressBar': (555, 2, (...), (...), 'AddressBar', None), 
              'Application': (200, 2, (...), (...), 'Application', None), 
              'Busy': (212, 2, (...), (...), 'Busy', None), 
              'Container': (202, 2, (...), (...), 'Container', None), 
              'Document': (203, 2, (...), (...), 'Document', None), 
              'FullName': (400, 2, (...), (...), 'FullName', None), 
              'FullScreen': (407, 2, (...), (...), 'FullScreen', None), 
              'HWND': (-515, 2, (...), (...), 'HWND', None), 
              'Height': (209, 2, (...), (...), 'Height', None), 
              'Left': (206, 2, (...), (...), 'Left', None), 
              'LocationName': (210, 2, (...), (...), 'LocationName', None), 
              'LocationURL': (211, 2, (...), (...), 'LocationURL', None), 
              'MenuBar': (406, 2, (...), (...), 'MenuBar', None), 
              'Name': (0, 2, (...), (...), 'Name', None), 
              'Offline': (550, 2, (...), (...), 'Offline', None), 
              'Parent': (201, 2, (...), (...), 'Parent', None), 
              'Path': (401, 2, (...), (...), 'Path', None), 
              'ReadyState': (-525, 2, (...), (...), 'ReadyState', None), 
              'RegisterAsBrowser': (552, 2, (...), (...), 'RegisterAsBrowser', None), 
              'RegisterAsDropTarget': (553, 2, (...), (...), 'RegisterAsDropTarget', None), 
              'Resizable': (556, 2, (...), (...), 'Resizable', None), 
              'Silent': (551, 2, (...), (...), 'Silent', None), 
              'StatusBar': (403, 2, (...), (...), 'StatusBar', None), 
              'StatusText': (404, 2, (...), (...), 'StatusText', None), 
              'TheaterMode': (554, 2, (...), (...), 'TheaterMode', None), 
              'ToolBar': (405, 2, (...), (...), 'ToolBar', None), 
              'Top': (207, 2, (...), (...), 'Top', None), 
              'TopLevelContainer': (204, 2, (...), (...), 'TopLevelContainer', None), 
              'Type': (205, 2, (...), (...), 'Type', None), 
              'Visible': (402, 2, (...), (...), 'Visible', None), 
              'Width': (208, 2, (...), (...), 'Width', None)
            }, 
          _prop_map_put_ = {
              'AddressBar': ((...), (...)), 
              'FullScreen': ((...), (...)), 
              'Height': ((...), (...)), 
              'Left': ((...), (...)), 
              'MenuBar': ((...), (...)), 
              'Offline': ((...), (...)), 
              'RegisterAsBrowser': ((...), (...)), 
              'RegisterAsDropTarget': ((...), (...)), 
              'Resizable': ((...), (...)), 
              'Silent': ((...), (...)), 
              'StatusBar': ((...), (...)), 
              'StatusText': ((...), (...)), 
              'TheaterMode': ((...), (...)), 
              'ToolBar': ((...), (...)), 
              'Top': ((...), (...)), 
              'Visible': ((...), (...)), 
              'Width': ((...), (...))
            }, 
          coclass_clsid = PyIID()
        ), 
      Busy = False, 
      CLSID = PyIID(), 
      Container = None, 
      Document = win32com.client.CDispatch(
          Application = win32com.client.CDispatch(
              Application = win32com.client.CDispatch(
                  Application = win32com.client.CDispatch(...), 
                  Parent = win32com.client.CDispatch(...), 
                  _builtMethods_ = {...}, 
                  _enum_ = None, 
                  _lazydata_ = (...), 
                  _mapCachedItems_ = {...}, 
                  _oleobj_ = PyIDispatch(...), 
                  _olerepr_ = win32com.client.build.LazyDispatchItem(...), 
                  _unicode_to_string_ = None, 
                  _username_ = '<unknown>'
                ), 
              Parent = win32com.client.CDispatch(
                  Application = win32com.client.CDispatch(...), 
                  Parent = win32com.client.CDispatch(...), 
                  _builtMethods_ = {...}, 
                  _enum_ = None, 
                  _lazydata_ = (...), 
                  _mapCachedItems_ = {...}, 
                  _oleobj_ = PyIDispatch(...), 
                  _olerepr_ = win32com.client.build.LazyDispatchItem(...), 
                  _unicode_to_string_ = None, 
                  _username_ = '<unknown>'
                ), 
              _builtMethods_ = {
                  'AddToRecent': win32com.client.dynamic.function(...), 
                  'BrowseForFolder': win32com.client.dynamic.function(...), 
                  'CanStartStopService': win32com.client.dynamic.function(...), 
                  'CascadeWindows': win32com.client.dynamic.function(...), 
                  'ControlPanelItem': win32com.client.dynamic.function(...), 
                  'EjectPC': win32com.client.dynamic.function(...), 
                  'Explore': win32com.client.dynamic.function(...), 
                  'ExplorerPolicy': win32com.client.dynamic.function(...), 
                  'FileRun': win32com.client.dynamic.function(...), 
                  'FindComputer': win32com.client.dynamic.function(...), 
                  'FindFiles': win32com.client.dynamic.function(...), 
                  'FindPrinter': win32com.client.dynamic.function(...), 
                  'GetSetting': win32com.client.dynamic.function(...), 
                  'GetSystemInformation': win32com.client.dynamic.function(...), 
                  'Help': win32com.client.dynamic.function(...), 
                  'IsRestricted': win32com.client.dynamic.function(...), 
                  'IsServiceRunning': win32com.client.dynamic.function(...), 
                  'MinimizeAll': win32com.client.dynamic.function(...), 
                  'NameSpace': win32com.client.dynamic.function(...), 
                  'Open': win32com.client.dynamic.function(...), 
                  'RefreshMenu': win32com.client.dynamic.function(...), 
                  'SearchCommand': win32com.client.dynamic.function(...), 
                  'ServiceStart': win32com.client.dynamic.function(...), 
                  'ServiceStop': win32com.client.dynamic.function(...), 
                  'SetTime': win32com.client.dynamic.function(...), 
                  'ShellExecute': win32com.client.dynamic.function(...), 
                  'ShowBrowserBar': win32com.client.dynamic.function(...), 
                  'ShutdownWindows': win32com.client.dynamic.function(...), 
                  'Suspend': win32com.client.dynamic.function(...), 
                  'TileHorizontally': win32com.client.dynamic.function(...), 
                  'TileVertically': win32com.client.dynamic.function(...), 
                  'ToggleDesktop': win32com.client.dynamic.function(...), 
                  'TrayProperties': win32com.client.dynamic.function(...), 
                  'UndoMinimizeALL': win32com.client.dynamic.function(...), 
                  'WindowSwitcher': win32com.client.dynamic.function(...), 
                  'Windows': win32com.client.dynamic.function(...), 
                  'WindowsSecurity': win32com.client.dynamic.function(...)
                }, 
              _enum_ = None, 
              _lazydata_ = (PyITypeInfo(...), PyITypeComp(...)), 
              _mapCachedItems_ = {}, 
              _oleobj_ = PyIDispatch(), 
              _olerepr_ = win32com.client.build.LazyDispatchItem(
                  bIsDispatch = 0, 
                  bIsSink = 0, 
                  bWritten = 0, 
                  clsid = None, 
                  co_class = None, 
                  defaultDispatchName = None, 
                  doc = None, 
                  hidden = 0, 
                  mapFuncs = {...}, 
                  propMap = {...}, 
                  propMapGet = {...}, 
                  propMapPut = {...}, 
                  python_name = None, 
                  typename = 'LazyDispatchItem'
                ), 
              _unicode_to_string_ = None, 
              _username_ = '<unknown>'
            ), 
          CurrentViewMode = 4, 
          FocusedItem = win32com.client.CDispatch(
              Application = win32com.client.CDispatch(
                  Application = win32com.client.CDispatch(...), 
                  Parent = win32com.client.CDispatch(...), 
                  _builtMethods_ = {...}, 
                  _enum_ = None, 
                  _lazydata_ = (...), 
                  _mapCachedItems_ = {...}, 
                  _oleobj_ = PyIDispatch(...), 
                  _olerepr_ = win32com.client.build.LazyDispatchItem(...), 
                  _unicode_to_string_ = None, 
                  _username_ = '<unknown>'
                ), 
              GetFolder = <Attribute error: com_error>, 
              GetLink = <Attribute error: com_error>, 
              IsBrowsable = False, 
              IsFileSystem = True, 
              IsFolder = False, 
              IsLink = False, 
              ModifyDate = datetime(
                  day = 29, 
                  fold = 0, 
                  hour = 14, 
                  max = datetime(...), 
                  microsecond = 0, 
                  min = datetime(...), 
                  minute = 33, 
                  month = 12, 
                  resolution = timedelta(...), 
                  second = 0, 
                  tzinfo = win32timezone.TimeZoneInfo(...), 
                  year = 2022
                ), 
              Name = '-OPEN-VENV-PROMPT.bat', 
              Parent = win32com.client.CDispatch(
                  Application = win32com.client.CDispatch(...), 
                  HaveToShowWebViewBarricade = False, 
                  OfflineStatus = <Attribute error: com_error>, 
                  Parent = <Attribute error: com_error>, 
                  ParentFolder = win32com.client.CDispatch(...), 
                  Self = win32com.client.CDispatch(...), 
                  ShowWebViewBarricade = False, 
                  Title = 'SaveWindowLayout', 
                  _builtMethods_ = {...}, 
                  _enum_ = None, 
                  _lazydata_ = (...), 
                  _mapCachedItems_ = {...}, 
                  _oleobj_ = PyIDispatch(...), 
                  _olerepr_ = win32com.client.build.LazyDispatchItem(...), 
                  _unicode_to_string_ = None, 
                  _username_ = '<unknown>'
                ), 
              Path = 'D:\\Data\\Prosjekter\\Script...ut\\-OPEN-VENV-PROMPT.bat', 
              Size = 138, 
              Type = 'Windows Batch File', 
              _builtMethods_ = {
                  'ExtendedProperty': win32com.client.dynamic.function(...), 
                  'InvokeVerb': win32com.client.dynamic.function(...), 
                  'InvokeVerbEx': win32com.client.dynamic.function(...), 
                  'Verbs': win32com.client.dynamic.function(...)
                }, 
              _enum_ = None, 
              _lazydata_ = (PyITypeInfo(...), PyITypeComp(...)), 
              _mapCachedItems_ = {}, 
              _oleobj_ = PyIDispatch(), 
              _olerepr_ = win32com.client.build.LazyDispatchItem(
                  bIsDispatch = 0, 
                  bIsSink = 0, 
                  bWritten = 0, 
                  clsid = None, 
                  co_class = None, 
                  defaultDispatchName = 'Name', 
                  doc = None, 
                  hidden = 0, 
                  mapFuncs = {...}, 
                  propMap = {...}, 
                  propMapGet = {...}, 
                  propMapPut = {...}, 
                  python_name = None, 
                  typename = 'LazyDispatchItem'
                ), 
              _unicode_to_string_ = None, 
              _username_ = '<unknown>'
            ), 
          Folder = win32com.client.CDispatch(
              Application = win32com.client.CDispatch(
                  Application = win32com.client.CDispatch(...), 
                  Parent = win32com.client.CDispatch(...), 
                  _builtMethods_ = {...}, 
                  _enum_ = None, 
                  _lazydata_ = (...), 
                  _mapCachedItems_ = {...}, 
                  _oleobj_ = PyIDispatch(...), 
                  _olerepr_ = win32com.client.build.LazyDispatchItem(...), 
                  _unicode_to_string_ = None, 
                  _username_ = '<unknown>'
                ), 
              HaveToShowWebViewBarricade = False, 
              OfflineStatus = <Attribute error: com_error>, 
              Parent = <Attribute error: com_error>, 
              ParentFolder = win32com.client.CDispatch(
                  Application = win32com.client.CDispatch(...), 
                  HaveToShowWebViewBarricade = False, 
                  OfflineStatus = <Attribute error: com_error>, 
                  Parent = <Attribute error: com_error>, 
                  ParentFolder = win32com.client.CDispatch(...), 
                  Self = win32com.client.CDispatch(...), 
                  ShowWebViewBarricade = False, 
                  Title = 'WIP', 
                  _builtMethods_ = {...}, 
                  _enum_ = None, 
                  _lazydata_ = (...), 
                  _mapCachedItems_ = {...}, 
                  _oleobj_ = PyIDispatch(...), 
                  _olerepr_ = win32com.client.build.LazyDispatchItem(...), 
                  _unicode_to_string_ = None, 
                  _username_ = '<unknown>'
                ), 
              Self = win32com.client.CDispatch(
                  Application = win32com.client.CDispatch(...), 
                  GetFolder = win32com.client.CDispatch(...), 
                  GetLink = <Attribute error: com_error>, 
                  IsBrowsable = False, 
                  IsFileSystem = True, 
                  IsFolder = True, 
                  IsLink = False, 
                  ModifyDate = datetime(...), 
                  Name = 'SaveWindowLayout', 
                  Parent = win32com.client.CDispatch(...), 
                  Path = 'D:\\Data\\Prosjekter\\Scripts\\WIP\\SaveWindowLayout', 
                  Size = 0, 
                  Type = 'File folder', 
                  _builtMethods_ = {...}, 
                  _enum_ = None, 
                  _lazydata_ = (...), 
                  _mapCachedItems_ = {...}, 
                  _oleobj_ = PyIDispatch(...), 
                  _olerepr_ = win32com.client.build.LazyDispatchItem(...), 
                  _unicode_to_string_ = None, 
                  _username_ = '<unknown>'
                ), 
              ShowWebViewBarricade = False, 
              Title = 'SaveWindowLayout', 
              _builtMethods_ = {
                  'CopyHere': win32com.client.dynamic.function(...), 
                  'DismissedWebViewBarricade': win32com.client.dynamic.function(...), 
                  'GetDetailsOf': win32com.client.dynamic.function(...), 
                  'Items': win32com.client.dynamic.function(...), 
                  'MoveHere': win32com.client.dynamic.function(...), 
                  'NewFolder': win32com.client.dynamic.function(...), 
                  'ParseName': win32com.client.dynamic.function(...), 
                  'Synchronize': win32com.client.dynamic.function(...)
                }, 
              _enum_ = None, 
              _lazydata_ = (PyITypeInfo(...), PyITypeComp(...)), 
              _mapCachedItems_ = {}, 
              _oleobj_ = PyIDispatch(), 
              _olerepr_ = win32com.client.build.LazyDispatchItem(
                  bIsDispatch = 0, 
                  bIsSink = 0, 
                  bWritten = 0, 
                  clsid = None, 
                  co_class = None, 
                  defaultDispatchName = 'Title', 
                  doc = None, 
                  hidden = 0, 
                  mapFuncs = {...}, 
                  propMap = {...}, 
                  propMapGet = {...}, 
                  propMapPut = {...}, 
                  python_name = None, 
                  typename = 'LazyDispatchItem'
                ), 
              _unicode_to_string_ = None, 
              _username_ = '<unknown>'
            ), 
          FolderFlags = 1092616192, 
          GroupBy = 'System.Null', 
          IconSize = 16, 
          Parent = <Attribute error: com_error>, 
          Script = <Attribute error: com_error>, 
          SortColumns = 'prop:-System.DateModified;', 
          ViewOptions = 170, 
          _builtMethods_ = {
              'FilterView': win32com.client.dynamic.function(), 
              'PopupItemMenu': win32com.client.dynamic.function(), 
              'SelectItem': win32com.client.dynamic.function(), 
              'SelectItemRelative': win32com.client.dynamic.function(), 
              'SelectedItems': win32com.client.dynamic.function()
            }, 
          _enum_ = None, 
          _lazydata_ = (PyITypeInfo(), PyITypeComp()), 
          _mapCachedItems_ = {}, 
          _oleobj_ = PyIDispatch(), 
          _olerepr_ = win32com.client.build.LazyDispatchItem(
              bIsDispatch = 0, 
              bIsSink = 0, 
              bWritten = 0, 
              clsid = None, 
              co_class = None, 
              defaultDispatchName = None, 
              doc = None, 
              hidden = 0, 
              mapFuncs = {
                  'FilterView': win32com.client.build.MapEntry(...), 
                  'PopupItemMenu': win32com.client.build.MapEntry(...), 
                  'SelectItem': win32com.client.build.MapEntry(...), 
                  'SelectItemRelative': win32com.client.build.MapEntry(...), 
                  'SelectedItems': win32com.client.build.MapEntry(...)
                }, 
              propMap = {}, 
              propMapGet = {
                  'Application': win32com.client.build.MapEntry(...), 
                  'CurrentViewMode': win32com.client.build.MapEntry(...), 
                  'FocusedItem': win32com.client.build.MapEntry(...), 
                  'Folder': win32com.client.build.MapEntry(...), 
                  'FolderFlags': win32com.client.build.MapEntry(...), 
                  'GroupBy': win32com.client.build.MapEntry(...), 
                  'IconSize': win32com.client.build.MapEntry(...), 
                  'Parent': win32com.client.build.MapEntry(...), 
                  'Script': win32com.client.build.MapEntry(...), 
                  'SortColumns': win32com.client.build.MapEntry(...), 
                  'ViewOptions': win32com.client.build.MapEntry(...)
                }, 
              propMapPut = {
                  'CurrentViewMode': win32com.client.build.MapEntry(...), 
                  'FolderFlags': win32com.client.build.MapEntry(...), 
                  'GroupBy': win32com.client.build.MapEntry(...), 
                  'IconSize': win32com.client.build.MapEntry(...), 
                  'SortColumns': win32com.client.build.MapEntry(...)
                }, 
              python_name = None, 
              typename = 'LazyDispatchItem'
            ), 
          _unicode_to_string_ = None, 
          _username_ = 'Document'
        ), 
      FullName = 'C:\\Windows\\explorer.exe', 
      FullScreen = False, 
      HWND = 6163636, 
      Height = 443, 
      Left = 1920, 
      LocationName = 'SaveWindowLayout', 
      LocationURL = 'file:///D:/Data/Prosjekte...pts/WIP/SaveWindowLayout', 
      MenuBar = False, 
      Name = 'File Explorer', 
      Offline = False, 
      Parent = win32com.gen_py.EAB22AC0-30C1-11CF-A7EB-0000C05BAE0Bx0x1x1.IWebBrowser2.IWebBrowser2(
          AddressBar = True, 
          Application = win32com.gen_py.EAB22AC0-30C1-11CF-A7EB-0000C05BAE0Bx0x1x1.IWebBrowser2.IWebBrowser2(
              AddressBar = True, 
              Application = win32com.gen_py.EAB22AC0-30C1-11CF-A7EB-0000C05BAE0Bx0x1x1.IWebBrowser2.IWebBrowser2(
                  AddressBar = True, 
                  Application = win32com.gen_py.EAB22AC0-30C1-11CF-A7EB-0000C05BAE0Bx0x1x1.IWebBrowser2.IWebBrowser2(...), 
                  Busy = False, 
                  CLSID = PyIID(...), 
                  Container = None, 
                  Document = win32com.client.CDispatch(...), 
                  FullName = 'C:\\Windows\\explorer.exe', 
                  FullScreen = False, 
                  HWND = 6163636, 
                  Height = 443, 
                  Left = 1920, 
                  LocationName = 'SaveWindowLayout', 
                  LocationURL = 'file:///D:/Data/Prosjekte...pts/WIP/SaveWindowLayout', 
                  MenuBar = False, 
                  Name = 'File Explorer', 
                  Offline = False, 
                  Parent = win32com.gen_py.EAB22AC0-30C1-11CF-A7EB-0000C05BAE0Bx0x1x1.IWebBrowser2.IWebBrowser2(...), 
                  Path = 'C:\\Windows\\', 
                  ReadyState = 4, 
                  RegisterAsBrowser = False, 
                  RegisterAsDropTarget = True, 
                  Resizable = True, 
                  Silent = False, 
                  StatusBar = False, 
                  StatusText = '', 
                  TheaterMode = False, 
                  ToolBar = 1, 
                  Top = 0, 
                  TopLevelContainer = True, 
                  Type = <Attribute error: com_error>, 
                  Visible = True, 
                  Width = 1920, 
                  _oleobj_ = PyIDispatch(...), 
                  _prop_map_get_ = {...}, 
                  _prop_map_put_ = {...}, 
                  coclass_clsid = PyIID(...)
                ), 
              Busy = False, 
              CLSID = PyIID(), 
              Container = None, 
              Document = win32com.client.CDispatch(
                  Application = win32com.client.CDispatch(...), 
                  CurrentViewMode = 4, 
                  FocusedItem = win32com.client.CDispatch(...), 
                  Folder = win32com.client.CDispatch(...), 
                  FolderFlags = 1092616192, 
                  GroupBy = 'System.Null', 
                  IconSize = 16, 
                  Parent = <Attribute error: com_error>, 
                  Script = <Attribute error: com_error>, 
                  SortColumns = 'prop:-System.DateModified;', 
                  ViewOptions = 170, 
                  _builtMethods_ = {...}, 
                  _enum_ = None, 
                  _lazydata_ = (...), 
                  _mapCachedItems_ = {...}, 
                  _oleobj_ = PyIDispatch(...), 
                  _olerepr_ = win32com.client.build.LazyDispatchItem(...), 
                  _unicode_to_string_ = None, 
                  _username_ = 'Document'
                ), 
              FullName = 'C:\\Windows\\explorer.exe', 
              FullScreen = False, 
              HWND = 6163636, 
              Height = 443, 
              Left = 1920, 
              LocationName = 'SaveWindowLayout', 
              LocationURL = 'file:///D:/Data/Prosjekte...pts/WIP/SaveWindowLayout', 
              MenuBar = False, 
              Name = 'File Explorer', 
              Offline = False, 
              Parent = win32com.gen_py.EAB22AC0-30C1-11CF-A7EB-0000C05BAE0Bx0x1x1.IWebBrowser2.IWebBrowser2(
                  AddressBar = True, 
                  Application = win32com.gen_py.EAB22AC0-30C1-11CF-A7EB-0000C05BAE0Bx0x1x1.IWebBrowser2.IWebBrowser2(...), 
                  Busy = False, 
                  CLSID = PyIID(...), 
                  Container = None, 
                  Document = win32com.client.CDispatch(...), 
                  FullName = 'C:\\Windows\\explorer.exe', 
                  FullScreen = False, 
                  HWND = 6163636, 
                  Height = 443, 
                  Left = 1920, 
                  LocationName = 'SaveWindowLayout', 
                  LocationURL = 'file:///D:/Data/Prosjekte...pts/WIP/SaveWindowLayout', 
                  MenuBar = False, 
                  Name = 'File Explorer', 
                  Offline = False, 
                  Parent = win32com.gen_py.EAB22AC0-30C1-11CF-A7EB-0000C05BAE0Bx0x1x1.IWebBrowser2.IWebBrowser2(...), 
                  Path = 'C:\\Windows\\', 
                  ReadyState = 4, 
                  RegisterAsBrowser = False, 
                  RegisterAsDropTarget = True, 
                  Resizable = True, 
                  Silent = False, 
                  StatusBar = False, 
                  StatusText = '', 
                  TheaterMode = False, 
                  ToolBar = 1, 
                  Top = 0, 
                  TopLevelContainer = True, 
                  Type = <Attribute error: com_error>, 
                  Visible = True, 
                  Width = 1920, 
                  _oleobj_ = PyIDispatch(...), 
                  _prop_map_get_ = {...}, 
                  _prop_map_put_ = {...}, 
                  coclass_clsid = PyIID(...)
                ), 
              Path = 'C:\\Windows\\', 
              ReadyState = 4, 
              RegisterAsBrowser = False, 
              RegisterAsDropTarget = True, 
              Resizable = True, 
              Silent = False, 
              StatusBar = False, 
              StatusText = '', 
              TheaterMode = False, 
              ToolBar = 1, 
              Top = 0, 
              TopLevelContainer = True, 
              Type = <Attribute error: com_error>, 
              Visible = True, 
              Width = 1920, 
              _oleobj_ = PyIDispatch(), 
              _prop_map_get_ = {
                  'AddressBar': (...), 
                  'Application': (...), 
                  'Busy': (...), 
                  'Container': (...), 
                  'Document': (...), 
                  'FullName': (...), 
                  'FullScreen': (...), 
                  'HWND': (...), 
                  'Height': (...), 
                  'Left': (...), 
                  'LocationName': (...), 
                  'LocationURL': (...), 
                  'MenuBar': (...), 
                  'Name': (...), 
                  'Offline': (...), 
                  'Parent': (...), 
                  'Path': (...), 
                  'ReadyState': (...), 
                  'RegisterAsBrowser': (...), 
                  'RegisterAsDropTarget': (...), 
                  'Resizable': (...), 
                  'Silent': (...), 
                  'StatusBar': (...), 
                  'StatusText': (...), 
                  'TheaterMode': (...), 
                  'ToolBar': (...), 
                  'Top': (...), 
                  'TopLevelContainer': (...), 
                  'Type': (...), 
                  'Visible': (...), 
                  'Width': (...)
                }, 
              _prop_map_put_ = {
                  'AddressBar': (...), 
                  'FullScreen': (...), 
                  'Height': (...), 
                  'Left': (...), 
                  'MenuBar': (...), 
                  'Offline': (...), 
                  'RegisterAsBrowser': (...), 
                  'RegisterAsDropTarget': (...), 
                  'Resizable': (...), 
                  'Silent': (...), 
                  'StatusBar': (...), 
                  'StatusText': (...), 
                  'TheaterMode': (...), 
                  'ToolBar': (...), 
                  'Top': (...), 
                  'Visible': (...), 
                  'Width': (...)
                }, 
              coclass_clsid = PyIID()
            ), 
          Busy = False, 
          CLSID = PyIID(), 
          Container = None, 
          Document = win32com.client.CDispatch(
              Application = win32com.client.CDispatch(
                  Application = win32com.client.CDispatch(...), 
                  Parent = win32com.client.CDispatch(...), 
                  _builtMethods_ = {...}, 
                  _enum_ = None, 
                  _lazydata_ = (...), 
                  _mapCachedItems_ = {...}, 
                  _oleobj_ = PyIDispatch(...), 
                  _olerepr_ = win32com.client.build.LazyDispatchItem(...), 
                  _unicode_to_string_ = None, 
                  _username_ = '<unknown>'
                ), 
              CurrentViewMode = 4, 
              FocusedItem = win32com.client.CDispatch(
                  Application = win32com.client.CDispatch(...), 
                  GetFolder = <Attribute error: com_error>, 
                  GetLink = <Attribute error: com_error>, 
                  IsBrowsable = False, 
                  IsFileSystem = True, 
                  IsFolder = False, 
                  IsLink = False, 
                  ModifyDate = datetime(...), 
                  Name = '-OPEN-VENV-PROMPT.bat', 
                  Parent = win32com.client.CDispatch(...), 
                  Path = 'D:\\Data\\Prosjekter\\Script...ut\\-OPEN-VENV-PROMPT.bat', 
                  Size = 138, 
                  Type = 'Windows Batch File', 
                  _builtMethods_ = {...}, 
                  _enum_ = None, 
                  _lazydata_ = (...), 
                  _mapCachedItems_ = {...}, 
                  _oleobj_ = PyIDispatch(...), 
                  _olerepr_ = win32com.client.build.LazyDispatchItem(...), 
                  _unicode_to_string_ = None, 
                  _username_ = '<unknown>'
                ), 
              Folder = win32com.client.CDispatch(
                  Application = win32com.client.CDispatch(...), 
                  HaveToShowWebViewBarricade = False, 
                  OfflineStatus = <Attribute error: com_error>, 
                  Parent = <Attribute error: com_error>, 
                  ParentFolder = win32com.client.CDispatch(...), 
                  Self = win32com.client.CDispatch(...), 
                  ShowWebViewBarricade = False, 
                  Title = 'SaveWindowLayout', 
                  _builtMethods_ = {...}, 
                  _enum_ = None, 
                  _lazydata_ = (...), 
                  _mapCachedItems_ = {...}, 
                  _oleobj_ = PyIDispatch(...), 
                  _olerepr_ = win32com.client.build.LazyDispatchItem(...), 
                  _unicode_to_string_ = None, 
                  _username_ = '<unknown>'
                ), 
              FolderFlags = 1092616192, 
              GroupBy = 'System.Null', 
              IconSize = 16, 
              Parent = <Attribute error: com_error>, 
              Script = <Attribute error: com_error>, 
              SortColumns = 'prop:-System.DateModified;', 
              ViewOptions = 170, 
              _builtMethods_ = {
                  'FilterView': win32com.client.dynamic.function(...), 
                  'PopupItemMenu': win32com.client.dynamic.function(...), 
                  'SelectItem': win32com.client.dynamic.function(...), 
                  'SelectItemRelative': win32com.client.dynamic.function(...), 
                  'SelectedItems': win32com.client.dynamic.function(...)
                }, 
              _enum_ = None, 
              _lazydata_ = (PyITypeInfo(...), PyITypeComp(...)), 
              _mapCachedItems_ = {}, 
              _oleobj_ = PyIDispatch(), 
              _olerepr_ = win32com.client.build.LazyDispatchItem(
                  bIsDispatch = 0, 
                  bIsSink = 0, 
                  bWritten = 0, 
                  clsid = None, 
                  co_class = None, 
                  defaultDispatchName = None, 
                  doc = None, 
                  hidden = 0, 
                  mapFuncs = {...}, 
                  propMap = {...}, 
                  propMapGet = {...}, 
                  propMapPut = {...}, 
                  python_name = None, 
                  typename = 'LazyDispatchItem'
                ), 
              _unicode_to_string_ = None, 
              _username_ = 'Document'
            ), 
          FullName = 'C:\\Windows\\explorer.exe', 
          FullScreen = False, 
          HWND = 6163636, 
          Height = 443, 
          Left = 1920, 
          LocationName = 'SaveWindowLayout', 
          LocationURL = 'file:///D:/Data/Prosjekte...pts/WIP/SaveWindowLayout', 
          MenuBar = False, 
          Name = 'File Explorer', 
          Offline = False, 
          Parent = win32com.gen_py.EAB22AC0-30C1-11CF-A7EB-0000C05BAE0Bx0x1x1.IWebBrowser2.IWebBrowser2(
              AddressBar = True, 
              Application = win32com.gen_py.EAB22AC0-30C1-11CF-A7EB-0000C05BAE0Bx0x1x1.IWebBrowser2.IWebBrowser2(
                  AddressBar = True, 
                  Application = win32com.gen_py.EAB22AC0-30C1-11CF-A7EB-0000C05BAE0Bx0x1x1.IWebBrowser2.IWebBrowser2(...), 
                  Busy = False, 
                  CLSID = PyIID(...), 
                  Container = None, 
                  Document = win32com.client.CDispatch(...), 
                  FullName = 'C:\\Windows\\explorer.exe', 
                  FullScreen = False, 
                  HWND = 6163636, 
                  Height = 443, 
                  Left = 1920, 
                  LocationName = 'SaveWindowLayout', 
                  LocationURL = 'file:///D:/Data/Prosjekte...pts/WIP/SaveWindowLayout', 
                  MenuBar = False, 
                  Name = 'File Explorer', 
                  Offline = False, 
                  Parent = win32com.gen_py.EAB22AC0-30C1-11CF-A7EB-0000C05BAE0Bx0x1x1.IWebBrowser2.IWebBrowser2(...), 
                  Path = 'C:\\Windows\\', 
                  ReadyState = 4, 
                  RegisterAsBrowser = False, 
                  RegisterAsDropTarget = True, 
                  Resizable = True, 
                  Silent = False, 
                  StatusBar = False, 
                  StatusText = '', 
                  TheaterMode = False, 
                  ToolBar = 1, 
                  Top = 0, 
                  TopLevelContainer = True, 
                  Type = <Attribute error: com_error>, 
                  Visible = True, 
                  Width = 1920, 
                  _oleobj_ = PyIDispatch(...), 
                  _prop_map_get_ = {...}, 
                  _prop_map_put_ = {...}, 
                  coclass_clsid = PyIID(...)
                ), 
              Busy = False, 
              CLSID = PyIID(), 
              Container = None, 
              Document = win32com.client.CDispatch(
                  Application = win32com.client.CDispatch(...), 
                  CurrentViewMode = 4, 
                  FocusedItem = win32com.client.CDispatch(...), 
                  Folder = win32com.client.CDispatch(...), 
                  FolderFlags = 1092616192, 
                  GroupBy = 'System.Null', 
                  IconSize = 16, 
                  Parent = <Attribute error: com_error>, 
                  Script = <Attribute error: com_error>, 
                  SortColumns = 'prop:-System.DateModified;', 
                  ViewOptions = 170, 
                  _builtMethods_ = {...}, 
                  _enum_ = None, 
                  _lazydata_ = (...), 
                  _mapCachedItems_ = {...}, 
                  _oleobj_ = PyIDispatch(...), 
                  _olerepr_ = win32com.client.build.LazyDispatchItem(...), 
                  _unicode_to_string_ = None, 
                  _username_ = 'Document'
                ), 
              FullName = 'C:\\Windows\\explorer.exe', 
              FullScreen = False, 
              HWND = 6163636, 
              Height = 443, 
              Left = 1920, 
              LocationName = 'SaveWindowLayout', 
              LocationURL = 'file:///D:/Data/Prosjekte...pts/WIP/SaveWindowLayout', 
              MenuBar = False, 
              Name = 'File Explorer', 
              Offline = False, 
              Parent = win32com.gen_py.EAB22AC0-30C1-11CF-A7EB-0000C05BAE0Bx0x1x1.IWebBrowser2.IWebBrowser2(
                  AddressBar = True, 
                  Application = win32com.gen_py.EAB22AC0-30C1-11CF-A7EB-0000C05BAE0Bx0x1x1.IWebBrowser2.IWebBrowser2(...), 
                  Busy = False, 
                  CLSID = PyIID(...), 
                  Container = None, 
                  Document = win32com.client.CDispatch(...), 
                  FullName = 'C:\\Windows\\explorer.exe', 
                  FullScreen = False, 
                  HWND = 6163636, 
                  Height = 443, 
                  Left = 1920, 
                  LocationName = 'SaveWindowLayout', 
                  LocationURL = 'file:///D:/Data/Prosjekte...pts/WIP/SaveWindowLayout', 
                  MenuBar = False, 
                  Name = 'File Explorer', 
                  Offline = False, 
                  Parent = win32com.gen_py.EAB22AC0-30C1-11CF-A7EB-0000C05BAE0Bx0x1x1.IWebBrowser2.IWebBrowser2(...), 
                  Path = 'C:\\Windows\\', 
                  ReadyState = 4, 
                  RegisterAsBrowser = False, 
                  RegisterAsDropTarget = True, 
                  Resizable = True, 
                  Silent = False, 
                  StatusBar = False, 
                  StatusText = '', 
                  TheaterMode = False, 
                  ToolBar = 1, 
                  Top = 0, 
                  TopLevelContainer = True, 
                  Type = <Attribute error: com_error>, 
                  Visible = True, 
                  Width = 1920, 
                  _oleobj_ = PyIDispatch(...), 
                  _prop_map_get_ = {...}, 
                  _prop_map_put_ = {...}, 
                  coclass_clsid = PyIID(...)
                ), 
              Path = 'C:\\Windows\\', 
              ReadyState = 4, 
              RegisterAsBrowser = False, 
              RegisterAsDropTarget = True, 
              Resizable = True, 
              Silent = False, 
              StatusBar = False, 
              StatusText = '', 
              TheaterMode = False, 
              ToolBar = 1, 
              Top = 0, 
              TopLevelContainer = True, 
              Type = <Attribute error: com_error>, 
              Visible = True, 
              Width = 1920, 
              _oleobj_ = PyIDispatch(), 
              _prop_map_get_ = {
                  'AddressBar': (...), 
                  'Application': (...), 
                  'Busy': (...), 
                  'Container': (...), 
                  'Document': (...), 
                  'FullName': (...), 
                  'FullScreen': (...), 
                  'HWND': (...), 
                  'Height': (...), 
                  'Left': (...), 
                  'LocationName': (...), 
                  'LocationURL': (...), 
                  'MenuBar': (...), 
                  'Name': (...), 
                  'Offline': (...), 
                  'Parent': (...), 
                  'Path': (...), 
                  'ReadyState': (...), 
                  'RegisterAsBrowser': (...), 
                  'RegisterAsDropTarget': (...), 
                  'Resizable': (...), 
                  'Silent': (...), 
                  'StatusBar': (...), 
                  'StatusText': (...), 
                  'TheaterMode': (...), 
                  'ToolBar': (...), 
                  'Top': (...), 
                  'TopLevelContainer': (...), 
                  'Type': (...), 
                  'Visible': (...), 
                  'Width': (...)
                }, 
              _prop_map_put_ = {
                  'AddressBar': (...), 
                  'FullScreen': (...), 
                  'Height': (...), 
                  'Left': (...), 
                  'MenuBar': (...), 
                  'Offline': (...), 
                  'RegisterAsBrowser': (...), 
                  'RegisterAsDropTarget': (...), 
                  'Resizable': (...), 
                  'Silent': (...), 
                  'StatusBar': (...), 
                  'StatusText': (...), 
                  'TheaterMode': (...), 
                  'ToolBar': (...), 
                  'Top': (...), 
                  'Visible': (...), 
                  'Width': (...)
                }, 
              coclass_clsid = PyIID()
            ), 
          Path = 'C:\\Windows\\', 
          ReadyState = 4, 
          RegisterAsBrowser = False, 
          RegisterAsDropTarget = True, 
          Resizable = True, 
          Silent = False, 
          StatusBar = False, 
          StatusText = '', 
          TheaterMode = False, 
          ToolBar = 1, 
          Top = 0, 
          TopLevelContainer = True, 
          Type = <Attribute error: com_error>, 
          Visible = True, 
          Width = 1920, 
          _oleobj_ = PyIDispatch(), 
          _prop_map_get_ = {
              'AddressBar': (555, 2, (...), (...), 'AddressBar', None), 
              'Application': (200, 2, (...), (...), 'Application', None), 
              'Busy': (212, 2, (...), (...), 'Busy', None), 
              'Container': (202, 2, (...), (...), 'Container', None), 
              'Document': (203, 2, (...), (...), 'Document', None), 
              'FullName': (400, 2, (...), (...), 'FullName', None), 
              'FullScreen': (407, 2, (...), (...), 'FullScreen', None), 
              'HWND': (-515, 2, (...), (...), 'HWND', None), 
              'Height': (209, 2, (...), (...), 'Height', None), 
              'Left': (206, 2, (...), (...), 'Left', None), 
              'LocationName': (210, 2, (...), (...), 'LocationName', None), 
              'LocationURL': (211, 2, (...), (...), 'LocationURL', None), 
              'MenuBar': (406, 2, (...), (...), 'MenuBar', None), 
              'Name': (0, 2, (...), (...), 'Name', None), 
              'Offline': (550, 2, (...), (...), 'Offline', None), 
              'Parent': (201, 2, (...), (...), 'Parent', None), 
              'Path': (401, 2, (...), (...), 'Path', None), 
              'ReadyState': (-525, 2, (...), (...), 'ReadyState', None), 
              'RegisterAsBrowser': (552, 2, (...), (...), 'RegisterAsBrowser', None), 
              'RegisterAsDropTarget': (553, 2, (...), (...), 'RegisterAsDropTarget', None), 
              'Resizable': (556, 2, (...), (...), 'Resizable', None), 
              'Silent': (551, 2, (...), (...), 'Silent', None), 
              'StatusBar': (403, 2, (...), (...), 'StatusBar', None), 
              'StatusText': (404, 2, (...), (...), 'StatusText', None), 
              'TheaterMode': (554, 2, (...), (...), 'TheaterMode', None), 
              'ToolBar': (405, 2, (...), (...), 'ToolBar', None), 
              'Top': (207, 2, (...), (...), 'Top', None), 
              'TopLevelContainer': (204, 2, (...), (...), 'TopLevelContainer', None), 
              'Type': (205, 2, (...), (...), 'Type', None), 
              'Visible': (402, 2, (...), (...), 'Visible', None), 
              'Width': (208, 2, (...), (...), 'Width', None)
            }, 
          _prop_map_put_ = {
              'AddressBar': ((...), (...)), 
              'FullScreen': ((...), (...)), 
              'Height': ((...), (...)), 
              'Left': ((...), (...)), 
              'MenuBar': ((...), (...)), 
              'Offline': ((...), (...)), 
              'RegisterAsBrowser': ((...), (...)), 
              'RegisterAsDropTarget': ((...), (...)), 
              'Resizable': ((...), (...)), 
              'Silent': ((...), (...)), 
              'StatusBar': ((...), (...)), 
              'StatusText': ((...), (...)), 
              'TheaterMode': ((...), (...)), 
              'ToolBar': ((...), (...)), 
              'Top': ((...), (...)), 
              'Visible': ((...), (...)), 
              'Width': ((...), (...))
            }, 
          coclass_clsid = PyIID()
        ), 
      Path = 'C:\\Windows\\', 
      ReadyState = 4, 
      RegisterAsBrowser = False, 
      RegisterAsDropTarget = True, 
      Resizable = True, 
      Silent = False, 
      StatusBar = False, 
      StatusText = '', 
      TheaterMode = False, 
      ToolBar = 1, 
      Top = 0, 
      TopLevelContainer = True, 
      Type = <Attribute error: com_error>, 
      Visible = True, 
      Width = 1920, 
      _oleobj_ = PyIDispatch(), 
      _prop_map_get_ = {
          'AddressBar': (555, 2, (11, 0), (), 'AddressBar', None), 
          'Application': (200, 2, (9, 0), (), 'Application', None), 
          'Busy': (212, 2, (11, 0), (), 'Busy', None), 
          'Container': (202, 2, (9, 0), (), 'Container', None), 
          'Document': (203, 2, (9, 0), (), 'Document', None), 
          'FullName': (400, 2, (8, 0), (), 'FullName', None), 
          'FullScreen': (407, 2, (11, 0), (), 'FullScreen', None), 
          'HWND': (-515, 2, (20, 0), (), 'HWND', None), 
          'Height': (209, 2, (3, 0), (), 'Height', None), 
          'Left': (206, 2, (3, 0), (), 'Left', None), 
          'LocationName': (210, 2, (8, 0), (), 'LocationName', None), 
          'LocationURL': (211, 2, (8, 0), (), 'LocationURL', None), 
          'MenuBar': (406, 2, (11, 0), (), 'MenuBar', None), 
          'Name': (0, 2, (8, 0), (), 'Name', None), 
          'Offline': (550, 2, (11, 0), (), 'Offline', None), 
          'Parent': (201, 2, (9, 0), (), 'Parent', None), 
          'Path': (401, 2, (8, 0), (), 'Path', None), 
          'ReadyState': (-525, 2, (3, 0), (), 'ReadyState', None), 
          'RegisterAsBrowser': (552, 2, (11, 0), (), 'RegisterAsBrowser', None), 
          'RegisterAsDropTarget': (553, 2, (11, 0), (), 'RegisterAsDropTarget', None), 
          'Resizable': (556, 2, (11, 0), (), 'Resizable', None), 
          'Silent': (551, 2, (11, 0), (), 'Silent', None), 
          'StatusBar': (403, 2, (11, 0), (), 'StatusBar', None), 
          'StatusText': (404, 2, (8, 0), (), 'StatusText', None), 
          'TheaterMode': (554, 2, (11, 0), (), 'TheaterMode', None), 
          'ToolBar': (405, 2, (3, 0), (), 'ToolBar', None), 
          'Top': (207, 2, (3, 0), (), 'Top', None), 
          'TopLevelContainer': (204, 2, (11, 0), (), 'TopLevelContainer', None), 
          'Type': (205, 2, (8, 0), (), 'Type', None), 
          'Visible': (402, 2, (11, 0), (), 'Visible', None), 
          'Width': (208, 2, (3, 0), (), 'Width', None)
        }, 
      _prop_map_put_ = {
          'AddressBar': ((555, 0, 4, 0), ()), 
          'FullScreen': ((407, 0, 4, 0), ()), 
          'Height': ((209, 0, 4, 0), ()), 
          'Left': ((206, 0, 4, 0), ()), 
          'MenuBar': ((406, 0, 4, 0), ()), 
          'Offline': ((550, 0, 4, 0), ()), 
          'RegisterAsBrowser': ((552, 0, 4, 0), ()), 
          'RegisterAsDropTarget': ((553, 0, 4, 0), ()), 
          'Resizable': ((556, 0, 4, 0), ()), 
          'Silent': ((551, 0, 4, 0), ()), 
          'StatusBar': ((403, 0, 4, 0), ()), 
          'StatusText': ((404, 0, 4, 0), ()), 
          'TheaterMode': ((554, 0, 4, 0), ()), 
          'ToolBar': ((405, 0, 4, 0), ()), 
          'Top': ((207, 0, 4, 0), ()), 
          'Visible': ((402, 0, 4, 0), ()), 
          'Width': ((208, 0, 4, 0), ())
        }, 
      coclass_clsid = PyIID()
    ), 
  Path = 'C:\\Windows\\', 
  ReadyState = 4, 
  RegisterAsBrowser = False, 
  RegisterAsDropTarget = True, 
  Resizable = True, 
  Silent = False, 
  StatusBar = False, 
  StatusText = '', 
  TheaterMode = False, 
  ToolBar = 1, 
  Top = 0, 
  TopLevelContainer = True, 
  Type = <Attribute error: com_error>, 
  Visible = True, 
  Width = 1920, 
  _oleobj_ = PyIDispatch(), 
  _prop_map_get_ = {
      'AddressBar': (555, 2, (11, 0), (), 'AddressBar', None), 
      'Application': (200, 2, (9, 0), (), 'Application', None), 
      'Busy': (212, 2, (11, 0), (), 'Busy', None), 
      'Container': (202, 2, (9, 0), (), 'Container', None), 
      'Document': (203, 2, (9, 0), (), 'Document', None), 
      'FullName': (400, 2, (8, 0), (), 'FullName', None), 
      'FullScreen': (407, 2, (11, 0), (), 'FullScreen', None), 
      'HWND': (-515, 2, (20, 0), (), 'HWND', None), 
      'Height': (209, 2, (3, 0), (), 'Height', None), 
      'Left': (206, 2, (3, 0), (), 'Left', None), 
      'LocationName': (210, 2, (8, 0), (), 'LocationName', None), 
      'LocationURL': (211, 2, (8, 0), (), 'LocationURL', None), 
      'MenuBar': (406, 2, (11, 0), (), 'MenuBar', None), 
      'Name': (0, 2, (8, 0), (), 'Name', None), 
      'Offline': (550, 2, (11, 0), (), 'Offline', None), 
      'Parent': (201, 2, (9, 0), (), 'Parent', None), 
      'Path': (401, 2, (8, 0), (), 'Path', None), 
      'ReadyState': (-525, 2, (3, 0), (), 'ReadyState', None), 
      'RegisterAsBrowser': (552, 2, (11, 0), (), 'RegisterAsBrowser', None), 
      'RegisterAsDropTarget': (553, 2, (11, 0), (), 'RegisterAsDropTarget', None), 
      'Resizable': (556, 2, (11, 0), (), 'Resizable', None), 
      'Silent': (551, 2, (11, 0), (), 'Silent', None), 
      'StatusBar': (403, 2, (11, 0), (), 'StatusBar', None), 
      'StatusText': (404, 2, (8, 0), (), 'StatusText', None), 
      'TheaterMode': (554, 2, (11, 0), (), 'TheaterMode', None), 
      'ToolBar': (405, 2, (3, 0), (), 'ToolBar', None), 
      'Top': (207, 2, (3, 0), (), 'Top', None), 
      'TopLevelContainer': (204, 2, (11, 0), (), 'TopLevelContainer', None), 
      'Type': (205, 2, (8, 0), (), 'Type', None), 
      'Visible': (402, 2, (11, 0), (), 'Visible', None), 
      'Width': (208, 2, (3, 0), (), 'Width', None)
    }, 
  _prop_map_put_ = {
      'AddressBar': ((555, 0, 4, 0), ()), 
      'FullScreen': ((407, 0, 4, 0), ()), 
      'Height': ((209, 0, 4, 0), ()), 
      'Left': ((206, 0, 4, 0), ()), 
      'MenuBar': ((406, 0, 4, 0), ()), 
      'Offline': ((550, 0, 4, 0), ()), 
      'RegisterAsBrowser': ((552, 0, 4, 0), ()), 
      'RegisterAsDropTarget': ((553, 0, 4, 0), ()), 
      'Resizable': ((556, 0, 4, 0), ()), 
      'Silent': ((551, 0, 4, 0), ()), 
      'StatusBar': ((403, 0, 4, 0), ()), 
      'StatusText': ((404, 0, 4, 0), ()), 
      'TheaterMode': ((554, 0, 4, 0), ()), 
      'ToolBar': ((405, 0, 4, 0), ()), 
      'Top': ((207, 0, 4, 0), ()), 
      'Visible': ((402, 0, 4, 0), ()), 
      'Width': ((208, 0, 4, 0), ())
    }, 
  coclass_clsid = PyIID()
)