# win32_window_monitor/main_usage_example.py
"""Usage example included in doc.

Demonstrates setup/listen/teardown for an HookEvent.
"""

from win32_window_monitor import *
from ctypes import wintypes


def on_event(win_event_hook_handle: HWINEVENTHOOK, event_id: int, hwnd: wintypes.HWND,
             id_object: wintypes.LONG, id_child: wintypes.LONG,
             event_thread_id: wintypes.DWORD,
             event_time_ms: wintypes.DWORD):
    # Called from the thread running the Windows message loop.
    # HookEvent provides a nice str, and unlike enum.Enum accepts any integer value.
    event_id = HookEvent(event_id)
    title = get_window_title(hwnd)
    process_id = get_hwnd_process_id(event_thread_id, hwnd)
    exe_path = get_process_filename(process_id) if process_id else '?'
    print(f'{event_time_ms} {event_id} P{process_id} {exe_path} {title}')


def main():
    # - init_com(): Initialize Windows COM (CoInitialize)
    # - post_quit_message_on_break_signal: Signal handlers to exit the
    # application when CTRL+C or CTRL+Break are pressed.
    with init_com(), post_quit_message_on_break_signal():
        # We must keep the event_hook_handle alive. Failure to do that may
        # cause a crash as the "trampoline" function generated by ctypes for
        # the event hook would be deleted.
        event_hook_handle = set_win_event_hook(on_event, HookEvent.SYSTEM_FOREGROUND)

        # Run the Windows message loop until the WM_QUIT message is received
        # (sent by signal handlers above). If you have a graphic UI (TkInter, Qt...), it is
        # likely that your application already has a Windows message loop that
        # should be used instead.
        run_message_loop()

        unhook_win_event(event_hook_handle)


if __name__ == '__main__':
    main()
