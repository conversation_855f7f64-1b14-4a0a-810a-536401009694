# Import modules
import os
import sys
import re
import time
import ctypes
import urllib.parse
from enum import Enum
# Import pywin32 modules
import win32com.client
import win32com.shell.shellcon as shellcon
import win32con
import win32gui
import win32process



# RETRIEVE ALL WINDOW HWND'S (IN THE Z-ORDER)
def get_all_window_hwnds():
    # - Get the top window (top of the z-order stack)
    # - Use while loop to ensure traversing through all windows
    #   - Append current hwnd
    #   - Go the the next window in the z-order
    # - Return result
    result_hwnds = []
    current_hwnd = win32gui.GetTopWindow(None)
    while current_hwnd:
        result_hwnds.append(current_hwnd)
        current_hwnd = win32gui.GetWindow(current_hwnd, win32con.GW_HWNDNEXT)
    return result_hwnds


# Creates a Windows Shell COM object instance and retrieve 'special folder' paths.
def initialize_shell_windows_instance():
    # Initializes a COM object instance of the 'Shell.Application' CLSID/ProgID
    CLSID_SHELL_WINDOWS = 'Shell.Application'
    shell_windows_instance = win32com.client.Dispatch(CLSID_SHELL_WINDOWS)
    shell_windows_instance_mapping = {window.HWND: window for window in shell_windows_instance.Windows()}

    # Retrieve special folders
    CSIDL_CONSTANTS = [getattr(shellcon, name) for name in dir(shellcon) if name.startswith("CSIDL_")]
    csidl_namespaces = [shell_windows_instance.Namespace(constant) for constant in CSIDL_CONSTANTS]
    valid_namespaces = [namespace for namespace in csidl_namespaces if hasattr(namespace, 'Application')]
    special_folders = [[namespace.Self.Name, namespace.Self.Path] for namespace in valid_namespaces]
    special_folders_mapping = {item[0]: item[1] for item in special_folders}

    # Retrieve all hwnd's
    windows_all_hwnds = []
    current_hwnd = win32gui.GetTopWindow(None)
    while current_hwnd:
        windows_all_hwnds.append(current_hwnd)
        current_hwnd = win32gui.GetWindow(current_hwnd, win32con.GW_HWNDNEXT)

    # Retrieve hwnd's that are part of shell windows
    shell_hwnds = []
    for hwnd in windows_all_hwnds:
        if hwnd in shell_windows_instance_mapping:
            shell_hwnds.append(shell_windows_instance_mapping[hwnd])

    print('any windows: %s' % len(windows_all_hwnds))
    print('shell windows: %s' % len(shell_hwnds))




# RETRIEVE BASIC WINDOW DATA (RETRIEVABLE BY HWND ON ALL WINDOWS)
def get_basic_window_data(hwnd):
    """
    Attributes:
        hwnd (int): Handle to the window. (example: 65740)
        title (str): Title of the window. (example: "This PC")
        class (str): Class name of the window. (example: "CabinetWClass")
        rect (tuple): Tuple containing the coordinates of the window.
        position (tuple): Tuple containing the x and y coordinates of the window.
        size (tuple): Tuple containing the width and height of the window.
        process (str): Path to the process associated with the window.
        path (str): Path of the window.
        type (str): Type of the window (e.g. special, normal, etc.)
    """
    """
    hwnd = Window title (type int : 'Window Handle' :
    """
    hwnd_title = win32gui.GetWindowText(hwnd)
    hwnd_class = win32gui.GetClassName(hwnd)
    hwnd_class = win32gui.GetClassName(hwnd)
     win32gui.GetWindowRect(self.hwnd)



    'hwnd',  # Window handle
    'z_index',  # Window stacking order
    'title',  # Window title
    'path',  # Window path
    'show_state',  # Window hidden/normal/minimized/maximized state
    'always_on_top',  # Window always-on-top status
    'position',  # Window position (x,y) from top-left corner of the screen
    'size',  # Window size (width, height)

all_hwnds = get_all_window_hwnds()
for hwnd in all_hwnds:
    if (win32gui.GetWindowText(hwnd) == "misc_utils"):
        print(dir(hwnd))
    # print(hwnd)
# # Retrieve the z-order of the retrieved items (using a while-loop to ensure traversing through all of the windows)
# currentItems_zIndex = []
# currWin_hwndHandle = win32gui.GetTopWindow(None)
# while (currWin_hwndHandle != 0):
#     if currWin_hwndHandle in currentItems_windowHandles: currentItems_zIndex.append(currWin_hwndHandle)
#     currWin_hwndHandle = win32gui.GetWindow(currWin_hwndHandle, win32con.GW_HWNDNEXT)

# # Retrieve the z-order of the retrieved items (using a while-loop to ensure traversing through all of the windows)
# currentItems_zIndex = []
# currWin_hwndHandle = win32gui.GetTopWindow(None)
# while (currWin_hwndHandle != 0):
#     if currWin_hwndHandle in currentItems_windowHandles: currentItems_zIndex.append(currWin_hwndHandle)
#     currWin_hwndHandle = win32gui.GetWindow(currWin_hwndHandle, win32con.GW_HWNDNEXT)

# def enum_callback(hwnd, _):
#     if win32gui.IsWindowVisible(hwnd):
#         open_windows = Window(hwnd, shell_instance_mapping, special_folders_mapping)
#         result_windows.append(open_windows)

# # Result
# result_windows = []
# # Enumerate all windows and pass them to the wm_enumerate_windows_callback method
# win32gui.EnumWindows(enum_callback, None)
# for x in result_windows:
#     print(x.title)
#     # print(dir(x))
#     # try:
#     # print(x.process)
#     # except:
#         # pass
#     # print(x.process)
