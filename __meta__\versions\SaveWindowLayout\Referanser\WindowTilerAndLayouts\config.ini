[global]
center_cursor = yes
left_margin = 0
right_margin = 0
top_margin = 0
bottom_margin = 0

[hotkey]
remove_window_from_master = alt+shift+l
add_window_to_master = alt+shift+h
focus_next_window = alt+j
focus_previous_window = alt+k
focus_primary_window = alt+return
shift_focused_window_down = alt+shift+j
shift_focused_window_up = alt+shift+k
shift_focused_window_to_primary = alt+shift+return
decrease_master_size = alt+h
increase_master_size = alt+l
close_focused_window = alt+shift+c
switch_to_group_1 = alt+1
switch_to_group_2 = alt+2
switch_to_group_3 = alt+3
switch_to_group_4 = alt+4
switch_to_group_5 = alt+5
switch_to_group_6 = alt+6
switch_to_group_7 = alt+7
switch_to_group_8 = alt+8
switch_to_group_9 = alt+9
send_to_group_1 = alt+shift+1
send_to_group_2 = alt+shift+2
send_to_group_3 = alt+shift+3
send_to_group_4 = alt+shift+4
send_to_group_5 = alt+shift+5
send_to_group_6 = alt+shift+6
send_to_group_7 = alt+shift+7
send_to_group_8 = alt+shift+8
send_to_group_9 = alt+shift+9
focus_next_monitor = alt+i
focus_previous_monitor = alt+u
shift_to_next_monitor = alt+shift+i
shift_to_previous_monitor = alt+shift+u
choose_next_layout = alt+space
toggle_focused_window_decoration = alt+shift+d
stop_pythonwindowstiler = alt+shift+delete
toggle_taskbar_visibility = alt+v
print_focused_window_classname = alt+s
tile_focused_window = alt+t
float_focused_window = alt+shift+t

[window]
float = progman;#32770
decorate = Chrome_WidgetWin_0;ConsoleWindowClass

