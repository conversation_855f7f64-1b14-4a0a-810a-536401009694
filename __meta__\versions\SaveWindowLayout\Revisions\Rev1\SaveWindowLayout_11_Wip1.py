# Import modules
import sys
import os
import time
import json
import subprocess
import win32api
import win32gui
import win32com.client
import win32con

# NOTE: THIS ONLY WORK FOR EXPLORER-WINDOWS
# Save/Load Explorer Windows (Paths/Locations/Sizes)


# subprocess.Popen(f'explorer.exe /n /e /min "D:/Dokumenter O.l/Documens/AndroidStudioProjects"')
# Retrieve any window-handles that have not already been processed
# start /min explorer "C:"

# hwnd = 1770862
# window = win32gui.GetWindow(1770862, win32con.GW_HWNDNEXT)
# print(window)
# print(type(window))
# print(dir(window))
# # print(window.HWND)
# # print(win32gui.GetWindowText(window.HWND))
# # print((window.LocationURL))
# time.sleep(1111)



# Constant: Specify path to the layout-file
layoutFile_FilePath = "WindowLayout.json"

# Constant: Specify name of the keys to use in the layout-file (for readability purposes) [Hwnd_ShowCmd: 1:Normal 2:Minimized 3:Maximized]
layoutFile_Keys = ['Hwnd_Handle', 'Hwnd_Title', 'Hwnd_OpenPath', 'Hwnd_Z-Index', 'Hwnd_ShowCmd', 'Hwnd_Pos', 'Hwnd_Size']

# If ApplyLayout: Specify whether or not to close windows that are not specified in layoutFile
closeUnspecifiedWindows = True

# If ApplyLayout: Specify whether or not to create specified windows if they are not already open
createWindowsThatDontExist = True



# # Specify the CLSID (Class Identifier) for COM-Object: ShellWindowsPermalink (File Explorer Windows)
# shellWindows_CLSID = '{9BA05972-F6A8-11CF-A442-00A0C90A8F39}'
# # Create a dynamic list of COM objects through the ShellWindows class (continually tracks open windows in the system)
# shellWindows_Instances = win32com.client.Dispatch('{9BA05972-F6A8-11CF-A442-00A0C90A8F39}')


# FUNCTION: RETRIEVE AND RETURN THE LAYOUT-DATA FOR (CURRENTLY OPEN) FILE-EXPLORER WINDOWS
def fnExplorerWindows_GetCurrentLayout():
    # Create a list to store the layout data of each window in
    currentLayout_HwndData = []
    # For each instance of currently open ShellWindows (File Explorer Window)
    for currInstance in win32com.client.Dispatch('{9BA05972-F6A8-11CF-A442-00A0C90A8F39}'):
        # currInstance: Retrieve data from the current window: ('Handle' | 'Title' | 'Path')
        currWin_Handle = currInstance.HWND
        currWin_Title  = win32gui.GetWindowText(currWin_Handle)
        currWin_Path   = (currInstance.LocationURL)
        # currWin_Path: Manual handling of windows that doesn't return a path
        currWin_Path = ('file:///' if (currWin_Title == 'This PC') else currWin_Path)
        currWin_Path = ('file:///%s/Desktop' % os.environ['USERPROFILE'] if (currWin_Title == 'Desktop') else currWin_Path)
        currWin_Path = ('shell:RecycleBinFolder' if (currWin_Title == 'Recycle Bin') else currWin_Path)
        currWin_Path = ('control' if (currWin_Title == 'All Control Panel Items') else currWin_Path)
        # currWin_Path: Update path and prefix with explorer-command (so that the path can be executed as a command)
        currWin_Path = currWin_Path.replace('\\','/')
        currWin_Path = (('explorer %s' % currWin_Path) if ('file:///' in currWin_Path) else currWin_Path)
        currWin_Path = (('explorer %s' % currWin_Path) if ('ftp://' in currWin_Path) else currWin_Path)
        # currInstance: Retrieve data from the current window: (WindowState:1/2/3 | Position:[X,Y] | Size:[X,Y])
        currWin_Placement   = list(win32gui.GetWindowPlacement(currWin_Handle))
        currWin_ShowCmd     = currWin_Placement[1]
        currWin_Coordinates = list(currWin_Placement[4])
        currWin_Pos  = ([currWin_Coordinates[0], currWin_Coordinates[1]])
        currWin_Size = ([currWin_Coordinates[2] - currWin_Pos[0], currWin_Coordinates[3] - currWin_Pos[1]])
        # Append current window-data to the result: ('Handle' | 'Title' | 'Path' | WindowState:1/2/3 | Position:[X,Y] | Size:[X,Y])
        currentLayout_HwndData.append([currWin_Handle, currWin_Title, currWin_Path, 0, currWin_ShowCmd, currWin_Pos, currWin_Size])

    # Collect the window-handles of retrieved items in a separate list (used as reference for retrieving their z-index)
    currentItems_windowHandles = [currItem[0] for currItem in currentLayout_HwndData]
    # Retrieve the z-order of the retrieved items (using a while-loop to ensure traversing through all of the windows)
    currentItems_zIndex = []
    currWin_hwndHandle = win32gui.GetTopWindow(None)
    while (currWin_hwndHandle != 0):
        if currWin_hwndHandle in currentItems_windowHandles: currentItems_zIndex.append(currWin_hwndHandle)
        currWin_hwndHandle = win32gui.GetWindow(currWin_hwndHandle, win32con.GW_HWNDNEXT)
    # Add the z-index to their corresponding item (by window-handle) in layout-data
    currentLayout_HwndData = [(currItem[:3] + [currentItems_zIndex.index(currItem[0])] + currItem[4:]) for currItem in currentLayout_HwndData]


    # Sort the layout-data by z-order and copy the data into a dictionary
    currentLayout_List = sorted(currentLayout_HwndData, key=lambda currItem: currItem[3])
    currentLayout_Dict = [{currKey:itemValue for currKey,itemValue in zip(layoutFile_Keys, currItem)} for currItem in currentLayout_List]
    # Return the result [Result[0]:(List), Result[1]:(Dictionary)]
    return [currentLayout_List, currentLayout_Dict]


# FUNCTION: LOAD AND APPLY LAYOUT (ON FILE-EXPLORER-WINDOWS) FROM SPECIFIED LAYOUTFILE
def fnExplorerWindows_ApplyLayout(importedLayout_List):
    # [COMPARE IMPORTED LAYOUT WITH CURRENT LAYOUT]
    # Retrieve the current layout (will be compared to imported layout in order to determine what actions to take on existing window-handles)
    currentLayout_List = (fnExplorerWindows_GetCurrentLayout())[0]
    # Initialize variables for dictionary/list comprehension (for efficiently consolidating data between current/imported layout)
    currentLayout_itemIndexLookup = {tuple(currItem[1:3]):currIndex for currIndex, currItem in enumerate(currentLayout_List)}
    importedLayout_itemIndexes = [tuple(currItem[1:3]) for currItem in importedLayout_List]
    # Compare current/imported layout and generate pairs of item-indexes based on whether their title/path match or not [[0]:CurrentLayout-Indexes, [1]:ImportLayout-Indexes]
    hwndMatched_IndexPairs = [[currentLayout_itemIndexLookup[currItem], currIndex] for currIndex, currItem in enumerate(importedLayout_itemIndexes) if currItem in currentLayout_itemIndexLookup]
    # Retrieve unmatched/remaining item-indexes from current/imported layout ([CurrentLayout-Indexes] | [ImportedLayout-Indexes])
    hwndUnmatched_Indexes = [currIndex for currIndex in range(len(currentLayout_List)) if currIndex not in [currPair[0] for currPair in hwndMatched_IndexPairs]]
    hwndRemaining_Indexes = [currIndex for currIndex in range(len(importedLayout_List)) if currIndex not in [currPair[1] for currPair in hwndMatched_IndexPairs]]







    # If (unspecified windows can be discarded) and (there are any unpaired windows available) and (there are remaining windows to create)
    if closeUnspecifiedWindows and len(currentLayout_hwndUnpaired) and len(importedLayout_hwndMissing):
        # Create a variable for storing the transferred item-indexes (used to maintain one-to-one relationship when transferring items)
        processedIndexes = []
        # For each unpaired/missing item (between the current and the imported layout)
        for itemIndex, (currentLayout_currItem, importLayout_currItem) in enumerate(zip(currentLayout_hwndUnpaired, importedLayout_hwndMissing)):
            # Append the current item to the list of stolen windows (using the window-handle from matching item in the current layout)
            importedLayout_hwndStolen.append([currentLayout_currItem[0]] + importLayout_currItem[1:])
            # Append the current item-index to the list of processed indexes
            processedIndexes.append(itemIndex)
        # Remove items from the lists of remaining items that has been retrieved/used
        [currentLayout_hwndUnpaired.pop(currIndex) for currIndex in sorted(processedIndexes, reverse=True)]
        [importedLayout_hwndMissing.pop(currIndex) for currIndex in sorted(processedIndexes, reverse=True)]




    # Compare current/imported layout and generate pairs of item-indexes based on whether their title/path match or not ([0]:CurrentLayoutIndex, [1]:ImportLayoutIndex)
    hwndMatched_IndexPairs = [[currentLayout_itemIndexLookup[currItem], currIndex] for currIndex, currItem in enumerate(importedLayout_itemIndexes) if currItem in currentLayout_itemIndexLookup]
    # Retrieve unmatched/remaining indexes from current/imported layout (CurrentLayoutIndex: Existing unmatched windows | ImportedLayout: Remaining windows to create)
    hwndUnmatched_Indexes = [currIndex for currIndex in range(len(currentLayout_List)) if currIndex not in [currPair[0] for currPair in hwndMatched_IndexPairs]]
    hwndRemaining_Indexes = [currIndex for currIndex in range(len(importedLayout_List)) if currIndex not in [currPair[1] for currPair in hwndMatched_IndexPairs]]



    # Retrieve unmatched/remaining item-indexes from current/imported layout (CurrentLayout-Indexes:Existing unmatched windows | ImportedLayout-Indexes: Remaining windows to create)

    # Compare current/imported layout and generate pairs of item-indexes based on whether their title/path match or not ([0]:CurrentIndex, [1]:ImportIndex)
    pairedLayouts_hwndMatched = [[currentLayout_itemIndexLookup[currItem], currIndex] for currIndex, currItem in enumerate(importedLayout_itemIndexes) if currItem in currentLayout_itemIndexLookup]
    # Retrieve remaining indexes from current/imported (existing unmatched windows / remaining windows to create)
    currentLayout_hwndUnmatched = [currIndex for currIndex in range(len(currentLayout_List)) if currIndex not in [currPair[0] for currPair in pairedLayouts_hwndMatched]]
    importedLayout_hwndMissing = [currIndex for currIndex in range(len(importedLayout_List)) if currIndex not in [currPair[1] for currPair in pairedLayouts_hwndMatched]]

    pairedLayouts_hwndUnmatched = [[currIndex, None] for currIndex, currItem in enumerate(currentLayout_List) if currIndex not in [currPair[0] for currPair in pairedLayouts_hwndMatched]]
    currentLayout_hwndUnmatched =
    pairedLayouts_hwndUnmatched = [[currIndex, None] for currIndex in range(len(currentLayout_List)) if currIndex not in [currPair[0] for currPair in pairedLayouts_hwndMatched]]
    #
    currentLayout_hwndUnmatched = []
    importedLayout_hwndMatched = [([currentLayout_List[currItem[0]][0]] + importedLayout_List[currItem[1]][1:]) for currItem in pairedLayouts_hwndMatched]
    importedLayout_hwndMatched

    print(pairedLayouts_hwndUnmatched)
    print(pairedLayouts_hwndMatched)
    importedLayout_hwndMatched = [([currentLayout_List[currItem[0]][0]] + importedLayout_List[currItem[1]][1:]) for currItem in pairedLayouts_hwndMatched]
    for x in importedLayout_hwndMatched:
        print(x)

    # Compare current/imported layout and generate pairs of item-indexes based on whether their title/path match or not ([[0]:CurrentIndex, [1]:ImportIndex] | [[0]:CurrentIndex, [1]:None])
    pairedLayouts_hwndMatched = [[currentLayout_itemIndexLookup[currItem], currIndex] for currIndex, currItem in enumerate(importedLayout_itemIndexes) if currItem in currentLayout_itemIndexLookup]
    pairedLayouts_hwndUnmatched = [[currIndex, None] for currIndex in range(len(currentLayout_List)) if currIndex not in [currPair[0] for currPair in pairedLayouts_hwndMatched]]



#     importedLayout_hwndMatched = ([currItem[0]] + currItem[1:]) for currItem in pairedLayouts_hwndMatched
#     importedLayout_hwndUnmatched

# importedLayout_hwndExistsAndMatched
# importedLayout_hwndExistsAndDifferent
# importedLayout_hwndAbsentFromCurrent
# importedLayout_hwndAbsent

# pairedLayouts_hwndMatched
# pairedLayouts_hwndDifferent
# pairedLayouts_hwndMissing
# pairedLayouts_hwndAbsent

#     importedLayout_hwndExistsAndRetrieved = []
#     importedLayout_hwndExistsButStolen = []
#     importedLayout_hwndMissing = []
# ntLayout_hwndMismatch
# currentLayout_hwndAbsent

# comparedLayouts_hwndExistsAndMatched
# comparedLayouts_hwndExistsAndUnmatched
# comparedLayouts_hwndAbsent


#     importedLayout_hwndCreated = []

#     for x in range(0,len(comparedLayouts_hwndIdentical)):
#         print(comparedLayouts_hwndIdentical[x])
#         actValCurr = comparedLayouts_hwndIdentical[x][0]
#         actValImp = comparedLayouts_hwndIdentical[x][1]
#         curr = currentLayout_List[actValCurr][1]
#         imp = importedLayout_List[actValImp][1]
#         print('Current: %s: %s' % (actValCurr, curr))
#         print('Import:  %s: %s' % (actValImp, imp))

#     print(comparedLayouts_hwndDifferent)
#     for x in range(0,len(comparedLayouts_hwndDifferent)):
#         print(comparedLayouts_hwndDifferent[x])
#         actValCurr = comparedLayouts_hwndDifferent[x][0]
#         actValImp = comparedLayouts_hwndDifferent[x][1]
#         curr = currentLayout_List[actValCurr][1]
#         imp = importedLayout_List[actValImp][1]


#     # Iterate through the items in the imported layout: importedLayout_List
#     for currItem in importedLayout_List:
#         currentLayout_subItemFetched[tuple(currItem[1:3])] = (tuple(currItem[1:3]) in currentLayout_subItemIndices)
#         # If a window matching the current import-item exists in the current layout (by matching title and path)
#         if (tuple(currItem[1:3]) in currentLayout_subItemIndices):
#             print("match")
#             # print('Current: %s >> Imported: %s' % ())
#             # Append the current item to the list of existing windows (using the window-handle from matching item in the current layout)
#             # currentLayout_hwndIdentical.append([])
#             # importedLayout_hwndPaired.append([currentLayout_subItemIndices[tuple(currItem[1:3])][0]] + currItem[1:])
#             # Update dictionary for the purpose of later being able to retrieve any unmatched windows in the current layout
#             currentLayout_subItemFetched[tuple(currItem[1:3])] = True
#         # Else (if the current import-item does not match any window in the current layout)
#         else:
#             print("missing")
#             # Append the current item to the list of missing/remaining items (window-handles for these must either be stolen or created)
#             # importedLayout_hwndMissing.append([False] + currItem[1:])
# # listExample = [4411,5348,1228,4411,8883,1992]
# # listExample_Indexes = [currItem.index() for currItem in listExample]
    time.sleep(9999)
    # Initialize variables for storing the imported layout-items separately (mainly for the purpose of re-using existing window-handles if/when possible)
    # Retrieve windows from the current layout with matching title/path to the inporte
    importedLayout_hwndPaired = []
    importedLayout_hwndMissing = []

    importedLayout_hwndStolen = []
    importedLayout_hwndCreated = []
    # Collect unpaired windows from the current layout (these can either be skipped, closed or transferred)
    currentLayout_hwndUnpaired = [currItem for currItem in currentLayout_List if tuple(currItem[1:3]) not in currentLayout_subItemFetched]
    # If (unspecified windows can be discarded) and (there are any unpaired windows available) and (there are remaining windows to create)
    if closeUnspecifiedWindows and len(currentLayout_hwndUnpaired) and len(importedLayout_hwndMissing):
        # Create a variable for storing the transferred item-indexes (used to maintain one-to-one relationship when transferring items)
        processedIndexes = []
        # For each unpaired/missing item (between the current and the imported layout)
        for itemIndex, (currentLayout_currItem, importLayout_currItem) in enumerate(zip(currentLayout_hwndUnpaired, importedLayout_hwndMissing)):
            # Append the current item to the list of stolen windows (using the window-handle from matching item in the current layout)
            importedLayout_hwndStolen.append([currentLayout_currItem[0]] + importLayout_currItem[1:])
            # Append the current item-index to the list of processed indexes
            processedIndexes.append(itemIndex)
        # Remove items from the lists of remaining items that has been retrieved/used
        [currentLayout_hwndUnpaired.pop(currIndex) for currIndex in sorted(processedIndexes, reverse=True)]
        [importedLayout_hwndMissing.pop(currIndex) for currIndex in sorted(processedIndexes, reverse=True)]

    print('importedLayout_hwndPaired: %s' % len(importedLayout_hwndPaired))
    print('importedLayout_hwndMissing: %s' % len(importedLayout_hwndMissing))
    print('importedLayout_hwndStolen: %s' % len(importedLayout_hwndStolen))
    print('importedLayout_hwndCreated: %s' % len(importedLayout_hwndCreated))
    print('currentLayout_List: %s' % len(currentLayout_List))
    print('currentLayout_hwndUnpaired: %s' % len(currentLayout_hwndUnpaired))


    # For each stolen window (currently open window that will be used instead of closing and creating a new window)
    for currItem in importedLayout_hwndStolen:
        # Specify the CLSID (Class Identifier) for COM-Object: ShellWindowsPermalink (File Explorer Windows)
        # shellWindows_CLSID = '{9BA05972-F6A8-11CF-A442-00A0C90A8F39}'
        # Create a dynamic list of COM objects through the ShellWindows class (continually tracks open windows in the system)
        shellWindows_Instances = win32com.client.Dispatch('{9BA05972-F6A8-11CF-A442-00A0C90A8F39}')
        # Retrieve the COM-object instance for the current window
        currItem_Instance = [currInstance.Navigate(currItem[2]) for currInstance in shellWindows_Instances if (currInstance.HWND == currItem[0])][0]
        print("-")
        # print(currItem_Instance.HWND)
        print(str(currItem))
        print("-")
        # currItem_Instance.Navigate(currItem[2])
        # currItem_Instance.Navigate("D:/Data/Prosjekter/Scripts/WIP/SaveWindowLayout")

    # print(len(shellWindows_Instances))

    # Close all remaining windows in the current layout
    [win32api.SendMessage(currWin[0], win32con.WM_CLOSE, 0, 0) for currWin in currentLayout_hwndUnpaired]

    # print(len(shellWindows_Instances))


    # # [CREATE THE MISSING WINDOWS (THAT COULD NOT BE PLACED ONTO EXISTING WINDOWS)]
    # # Collect all existing window-handles in a separate list (used to prevent retrieving the same window more than once)
    # windowHandles_Processed = [currItem[0] for currItem in currentLayout_List]
    # # If windows specified in the layout file should be created if they don't already exist
    # if createWindowsThatDontExist and len(importedLayout_hwndMissing):
    #     # For each item in importedLayout_hwndMissing (windows specified in layout-data that isn't already open)
    #     for currIndex, currItem in enumerate(importedLayout_hwndMissing):
    #         # Open a (minimized) window for the current item-path
    #         # openPathCommand = (('start /min %s' % currItem[2]) if ('file:///' in currItem[2] or 'shell:' in currItem[2]) else currItem[2])
    #         # openPathCommandResult = subprocess.Popen(openPathCommand, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    #         os.system(currItem[2])
    #         # Use a while-loop to wait for the newly created window to initialize
    #         while True:
    #             # Retrieve any window-handles that hasn't already been processed
    #             shellWindows_Instances = win32com.client.Dispatch('{9BA05972-F6A8-11CF-A442-00A0C90A8F39}')
    #             windowHandles_Current = [currInstance.HWND for currInstance in shellWindows_Instances if currInstance.HWND]
    #             windowHandles_hwndFound = [currHandle for currHandle in windowHandles_Current if currHandle not in windowHandles_Processed]
    #             # If a new window-handle has been retrieved
    #             if len(windowHandles_hwndFound):
    #                 print(str(windowHandles_hwndFound))
    #                 # Update the current item in importedLayout_hwndMissing and add the window-handle to windowHandles_Processed
    #                 importedLayout_hwndCreated.append([windowHandles_hwndFound[0]] + importedLayout_hwndMissing[currIndex][1:])
    #                 # importedLayout_hwndMissing.pop(currIndex)
    #                 windowHandles_Processed.append(windowHandles_hwndFound[0])
    #                 break


    # Combine imported items into one list and order items by z-index
    layoutData_List = (importedLayout_hwndPaired + importedLayout_hwndStolen + importedLayout_hwndCreated)
    layoutData_List = sorted(layoutData_List, key=lambda currItem: currItem[3])
    # For each item in layoutData_List (these are now windows that are currently open)
    for currItem in layoutData_List:
        # Extract current window-data and convert it to valid format for SetWindowPlacement to work
        currWin_ShowCmd = currItem[4]
        currWin_Coordinates = tuple([currItem[5][0], currItem[5][1], (currItem[5][0] + currItem[6][0]), (currItem[5][1] + currItem[6][1])])
        currWin_Placement = tuple([0, currItem[4], (-1, -1), (-1, -1), currWin_Coordinates])
        # Determine whether the current window is already correct or not
        currWin_Identical = (currWin_Placement == (win32gui.GetWindowPlacement(currItem[0])))
        # Apply layout for the current item
        # print(str(currItem))
        # print('%s' % currItem[1])
        # print('(BEFORE CHANGING) currWin_Placement: %s' % str(win32gui.GetWindowPlacement(currItem[0])))
        # print('      (FROM FILE) currWin_Placement: %s' % str(currWin_Placement))
        win32gui.SetWindowPlacement(currItem[0], currWin_Placement)
        # print(' (AFTER CHANGING) currWin_Placement: %s' % str(win32gui.GetWindowPlacement(currItem[0])))
        # print('--------------------------------------------------------------------------------------------------')

    # LEGGE INN VIEW-SETTINGS
    # FIX Z-INDEX
    # FIX PATHS FOR EXISTING WINDOWS
    # COMMENT OUT HWND_HANDLE

    # explorer flags:
    # /EXPAND,%windir% (expand gjør at kolonnen på venstre side viser hele treet til mappen du befinner deg i)

    # Print information
    # numWinsAlreadyOpen = len(importedLayout_hwndPaired)
    # numWinsOpened = len(importedLayout_hwndMissing)
    # numWinsApplied = (numWinsAlreadyOpen + numWinsOpened)
    # numWinsClosed = (len(winHandles_Close) if closeUnspecifiedWindows else 0)
    # print('Already open: %s' % numWinsAlreadyOpen)
    # print(('Opened windows: %s' % numWinsOpened) if createWindowsThatDontExist else ('Skipped windows: %s' % numWinsOpened))
    # print('Applied layout to: %s' % numWinsApplied)
    # print('Closed windows: %s' % numWinsClosed)



# --------------------------------------------------------------------------------------------------------------------
# APPLY LAYOUT
# --------------------------------------------------------------------------------------------------------------------
if (len(sys.argv) == 2) and (sys.argv[1] == "Apply"):
    # Open and retrieve data from the specified layoutFile (using the JSON module)
    with open(layoutFile_FilePath, 'r') as inputFile:
        importedLayout_Dict = [{currKey:currItem[currKey] for currKey in layoutFile_Keys} for currItem in json.load(inputFile)]
        importedLayout_List = [[currItem[currKey] for currKey in layoutFile_Keys] for currItem in importedLayout_Dict]
    fnExplorerWindows_ApplyLayout(importedLayout_List)
# --------------------------------------------------------------------------------------------------------------------



# --------------------------------------------------------------------------------------------------------------------
# CREATE LAYOUT
# --------------------------------------------------------------------------------------------------------------------
if (len(sys.argv) == 2) and (sys.argv[1] == "Create"):
    # Retrieve the current layout (as a dict)
    currentLayout_Dict = (fnExplorerWindows_GetCurrentLayout())[1]
    # Create and write data to the layout-file (using the JSON module)
    with open(layoutFile_FilePath, 'w') as outputFile:
        json.dump(currentLayout_Dict, outputFile, indent=4, sort_keys=False, separators=(',', ': '))
# --------------------------------------------------------------------------------------------------------------------
