# Import modules
import sys
import os
import subprocess
import json
import win32gui
import win32com.client as win32
import time

import win32api
import win32con

# NOTE: THIS ONLY WORK FOR EXPLORER-WINDOWS


# Settings: Specify whether or not to close all windows not specified in windowLayoutFile_Name
closeUndefinedWindows = True
# Settings: Specify name of the layout-file (to Store/Load window data)
windowLayoutFile_Name = "WindowLayout.json"
# Settings: Specify the full path of the layout-file
windowLayoutFile_Path = (os.getcwd() + '/' + windowLayoutFile_Name)
# Settings: Specify the name of the handles which will be stored in the layout-file (just for readability purposes)
jsonItemHandles = ['Handle', 'Title', 'Path', 'Pos', 'Size']



# FUNCTION: EITHER RETRIEVE OR DESTROY FILE-EXPLORER WINDOWS
def fnExplorerWindows_Actions(inputPaths=[], closeUndefined=True):
    # Create a variable for the result: (Handle | Title | Path | Pos[X,Y] | Size[X,Y])
    fileExplorerWindows_HwndData = []

    # Specify the CLSID (Class Identifier) for COM-Object: ShellWindowsPermalink
    ShellWindows_CLSID = '{9BA05972-F6A8-11CF-A442-00A0C90A8F39}'
    # For each File Explorer Window currently open (instances of COM-objects with specified CLSID)
    for currWin in win32.Dispatch(ShellWindows_CLSID):
        # Retrieve data from the current window
        currWinHwnd  = currWin.HWND
        currWinTitle = win32gui.GetWindowText(currWinHwnd)
        currWinPath  = ('explorer ' + currWin.LocationURL)
        currWinPos   = [currWin.Left, currWin.Top]
        currWinSize  = [currWin.Width, currWin.Height]
        # Manual handling of windows that doesn't return a path
        currWinPath = ('explorer file:///' if (currWinTitle == 'This PC') else currWinPath)
        currWinPath = ('explorer file:///%s/Desktop' % os.environ['USERPROFILE'] if (currWinTitle == 'Desktop') else currWinPath)
        currWinPath = ('shell:RecycleBinFolder' if (currWinTitle == 'Recycle Bin') else currWinPath)
        currWinPath = ('control' if (currWinTitle == 'All Control Panel Items') else currWinPath)
        # Store the current window data in a list
        currWinData = [currWinHwnd, currWinTitle, currWinPath, currWinPos, currWinSize]

        # If inputPaths contains data and closeUndefined, close the current window
        if len(inputPaths) and closeUndefined: win32api.SendMessage(currWinHwnd, win32con.WM_CLOSE, 0, 0)
        # If the current window path is passed in and it isn't already closed, close the current window
        if (currWinPath in inputPaths) and not closeUndefined: win32api.SendMessage(currWinHwnd, win32con.WM_CLOSE, 0, 0)
        # If no data was passed in and the current window is valid, add its [Handle, Title, Path, Position, Size] to the result
        if not (len(inputPaths)) and (currWinPath != ''): fileExplorerWindows_HwndData.append(currWinData)

    # Sort and return the result
    fileExplorerWindows_HwndData_Sorted = sorted(fileExplorerWindows_HwndData, key=lambda currItem: currItem[2])
    return fileExplorerWindows_HwndData_Sorted

# If a layout-file already exists
if (os.path.exists(windowLayoutFile_Path)):
    # Load data from the JSON file
    with open(windowLayoutFile_Path, 'r') as jsonfile:
        jsonData_Dict = [{jsonHandle:currItem[jsonHandle] for jsonHandle in jsonItemHandles} for currItem in json.load(jsonfile)]
        jsonData_List = [[currItem['Handle'],currItem['Title'],currItem['Path'],currItem['Pos'],currItem['Size']] for currItem in jsonData_Dict]

    # Close windows
    windowPaths = [currItem['Path'] for currItem in jsonData_Dict]
    fnExplorerWindows_Actions(inputPaths=windowPaths, closeUndefined=closeUndefinedWindows)
    # For each item in the json file (the layout-file)
    for currIndex, currItem in enumerate(jsonData_List):
        print(currItem[2])
        os.system(currItem[2])

    # print("\n")
    # print(jsonData_List)


# Retrieve window information: ['Handle', 'Title', 'Path', Pos:[X,Y], Size:[X,Y]]
fileExplorerWindows_HwndData = fnExplorerWindows_Actions()



# --------------------------------------------------------------------------------------------------------------------
# IF NO ARGUMENT WAS PASSED IN: CREATE THE CONTEXT MENU ITEMS (WINDOWS FILE RICK-CLICK MENU)
# --------------------------------------------------------------------------------------------------------------------
# If no argument was passed in
# if len(sys.argv) == 1:
# print(sys.argv[1] == "CloseAllOthers")
# win32gui.DestroyWindow(hwnd)

# ------------------------------------------------------------------------------------------------------------------------------------

# Convert the window-data into a dictionary (for json dump): [{'Handle':'Handle', 'Title':'Title', 'Path':'Path', 'Pos':[X,Y], 'Size':[X,Y]}]
jsonItemHandles = ['Handle', 'Title', 'Path', 'Pos', 'Size']
jsonDataDump = [{jsonHandle:itemValue for jsonHandle,itemValue in zip(jsonItemHandles, currItem)} for currItem in fileExplorerWindows_HwndData]


# Write the JSON file
with open(windowLayoutFile_Path, 'w') as jsonfile:
    json.dump(jsonDataDump, jsonfile, indent=4, sort_keys=True, separators=(',', ': '))

# ------------------------------------------------------------------------------------------------------------------------------------

# ------------------------------------------------------------------------------------------------------------------------------------




#     for currItem in jsonData:
# 'Handle','Title','Path','Pos','Size'
#     # Print the data
#     print(data)
#     print(data[0]['Handle'])  # prints "value1"
    # print(data['key2'])  # prints "value2"
    # print(data['key3'])  # prints [1, 2, 3]

    # json.dump(data, jsonfile, indent=4)

    # # os.system("file:\\")
    # time.sleep(10)
    # # For each item retrieved
    # for currIndex in range(0, len(fileExplorerWindows_HwndData["Handle"])):
    #     # Store the current item in separate variables (for readability purposes)
    #     currWinHwnd = fileExplorerWindows_HwndData["Handle"][currIndex]
    #     currWinTitle = fileExplorerWindows_HwndData["Title"][currIndex]
    #     currWinPath = fileExplorerWindows_HwndData["Path"][currIndex]
    #     currWinPos = fileExplorerWindows_HwndData["Pos"][currIndex]
    #     currWinSize = fileExplorerWindows_HwndData["Size"][currIndex]

    #     # Open a new instance of the current window (if it's not already open)
    #     alreadyOpen = win32gui.FindWindow('CabinetWClass', currWinTitle)
    #     if not alreadyOpen: os.system("explorer %s" % currWinPath)
    #     # explorerOpened = win32gui.FindWindow('CabinetWClass', currWinTitle)
    #     # hWnd = win32gui.FindWindow(class_name, caption)
    #     print(alreadyOpen)

        # win32gui.MoveWindow(currWinHwnd, currWinPos[0], currWinPos[1], currWinSize[0], currWinSize[1], True)

        # print('WinHandle: %s' % currWinHwnd)
        # print('WinTitle:  %s' % currWinTitle)
        # print('WinPath:   %s' % currWinPath)
        # print('WinPos:    %s' % currWinPos)
        # print('WinSize:   %s' % currWinSize)
        # print(fileExplorerWindows_HwndData["Handle"][currIndex])
        # # (0:HwndHandle | 1:FromLeftEdge | 2:FromTop | 3:Width | 4:Height | 5:Refresh)
        # currWinSize = win32gui.GetWindowRect(currWinHwnd)
        # currWinY = currWin.Top
        # currWinWidth = currWin.Width
        # currWinHeight = currWin.Height
        # currWinPos = [currWin.Left, currWin.Top, currWin.Width, currWin.Height]
        # # print(currWin.Width)
        # # print(currWin.Left)
        # # print(currWin.Top)
        # # currWinPath = currWin.LocationURL.replace('file:///','')
        # if currWinTitle == "SaveWindowLayoutx":
        #     print(currWinTitle)
        #     print(currWinPath)
        #     print(win32gui.GetWindowRect(currWinHwnd))
        #     print(currWinPos)
        #     # print([currWinX,currWinY,currWinWidth,currWinHeight])
        #     # print(currWin)
        #     # print(currWin)
        #     # print(currWin)
        #     time.sleep(3)
        #     win32gui.MoveWindow(currWinHwnd, currWinPos[0], currWinPos[1], currWinSize[0], currWinSize[1], True)
        #     # win32gui.MoveWindow(currWinHwnd, currWinSize[0], currWinSize[1], currWinSize[2], currWinSize[3], True)
        # # print(currWinPath)
        # # print(win32gui.GetWindowText(currWinHwnd))
        # print('\n')

        # print(currWin.Height)
        # print(currWin.Width)
        # print(currWin.Left)
        # print(currWin.Top)

        # explorerHwndData.append([currWinHwnd, currWinPath])
    # Return the result
    # return fileExplorerWindows_HwndData

# for x in dir(win32):
    # print(x)
# win32gui.MoveWindow(win32gui.GetForegroundWindow(), 160, 0, 1600, 720, True)
# file:\\ (åpner "this pc")

# for currIndex, currItem in enumerate(openExplorerWindows):
#     # print(currIndex)
#     currWinHwnd = openExplorerWindows[currIndex][0]
#     currWinPath = openExplorerWindows[currIndex][1]
#     currWinTitle = win32gui.GetWindowText(currWinHwnd)
#     currWinSize = win32gui.GetWindowRect(currWinHwnd)
    # print(currWinHwnd)
    # print(currWinPath)
    # print(currWinTitle)
    # print(currWinSize)
    # print('\n')
