# Import modules
import sys
import os
import subprocess
import json
import win32gui
import win32com.client as win32
import time

import win32api
import win32con

# NOTE: THIS ONLY WORK FOR EXPLORER-WINDOWS
# Save/Load Explorer Windows (Paths/Locations/Sizes)

# Constant: Specify path to the layout-file
layoutFile_FilePath = "WindowLayout.json"

# Constant: Specify name of the keys to use in the layout-file (for readability purposes) [Hwnd_ShowCmd: 1:Normal 2:Minimized 3:Maximized]
layoutFile_Keys = ['Hwnd_Handle', 'Hwnd_Title', 'Hwnd_OpenPath', 'Hwnd_ShowCmd', 'Hwnd_Pos', 'Hwnd_Size']

# If ApplyLayout: Specify whether or not to close windows that are not specified in layoutFile
closeUnspecifiedWindows = True

# If ApplyLayout: Specify whether or not to create specified windows if they are not already open
createWindowsThatDontExist = True


# FUNCTION: RETRIEVE AND RETURN THE LAYOUT-DATA FOR (CURRENTLY OPEN) FILE-EXPLORER WINDOWS
def fnExplorerWindows_GetCurrentLayout():
    # Create a list to store the layout data of each window in: ['Handle', 'Title', 'Path', (Normal), (Minimized), (Maximized), [Position], [Size]]
    fileExplorerWindows_HwndData = []

    # Specify the CLSID (Class Identifier) for COM-Object: ShellWindowsPermalink
    shellWindows_CLSID = '{9BA05972-F6A8-11CF-A442-00A0C90A8F39}'
    # For each (File Explorer Window) currently open (instances of COM-objects with specified CLSID)
    for currInstance in win32.Dispatch(shellWindows_CLSID):
        # currInstance: Retrieve data from the current window: ('Handle' | 'Title' | 'Path')
        currWin_Handle = currInstance.HWND
        currWin_Title  = win32gui.GetWindowText(currWin_Handle)
        currWin_Path   = (currInstance.LocationURL)
        # currWin_Path: Manual handling of windows that doesn't return a path
        currWin_Path = ('file:///' if (currWin_Title == 'This PC') else currWin_Path)
        currWin_Path = ('file:///%s/Desktop' % os.environ['USERPROFILE'] if (currWin_Title == 'Desktop') else currWin_Path)
        currWin_Path = ('start shell:RecycleBinFolder' if (currWin_Title == 'Recycle Bin') else currWin_Path)
        currWin_Path = ('control' if (currWin_Title == 'All Control Panel Items') else currWin_Path)
        # currWin_Path: Update path and prefix with explorer-command (so that the path can be executed as a command)
        currWin_Path = currWin_Path.replace('\\','/')
        currWin_Path = (('explorer %s' % currWin_Path) if ('file:///' in currWin_Path) else currWin_Path)
        currWin_Path = (('explorer %s' % currWin_Path) if ('ftp://' in currWin_Path) else currWin_Path)
        # currInstance: Retrieve data from the current window: (WindowState:1/2/3 | Position:[X,Y] | Size:[X,Y])
        currWin_Placement   = list(win32gui.GetWindowPlacement(currWin_Handle))
        currWin_ShowCmd     = currWin_Placement[1]
        currWin_Coordinates = list(currWin_Placement[4])
        currWin_Pos  = ([currWin_Coordinates[0], currWin_Coordinates[1]])
        currWin_Size = ([currWin_Coordinates[2] - currWin_Pos[0], currWin_Coordinates[3] - currWin_Pos[1]])
        # Append the current window data to the result
        fileExplorerWindows_HwndData.append([currWin_Handle, currWin_Title, currWin_Path, currWin_ShowCmd, currWin_Pos, currWin_Size])

    # Sort the layout-data by path and copy the data into a dictionary
    fileExplorerWindows_HwndData_List = sorted(fileExplorerWindows_HwndData, key=lambda currItem: currItem[2])
    fileExplorerWindows_HwndData_Dict = [{currKey:itemValue for currKey,itemValue in zip(layoutFile_Keys, currItem)} for currItem in fileExplorerWindows_HwndData_List]
    # Return the result [Result[0]:(List), Result[1]:(Dictionary)]
    return [fileExplorerWindows_HwndData_List, fileExplorerWindows_HwndData_Dict]


# FUNCTION: LOAD AND APPLY LAYOUT (ON FILE-EXPLORER-WINDOWS) FROM SPECIFIED LAYOUTFILE
def fnExplorerWindows_ApplyLayout():
    # Open and retrieve data from the specified layoutFile (using the JSON module)
    with open(layoutFile_FilePath, 'r') as inputFile:
        hwndLayout_Import_Dict = [{currKey:currItem[currKey] for currKey in layoutFile_Keys} for currItem in json.load(inputFile)]
        hwndLayout_Import_List = [[currItem[currKey] for currKey in layoutFile_Keys] for currItem in hwndLayout_Import_Dict]


    # Retrieve the current layout (used to determine how the imported layout should be applied and which windows to close)
    hwndLayout_Current_List = (fnExplorerWindows_GetCurrentLayout())[0]
    # Initialize variables for dictionary comprehension (for efficiently consolidating data between current/imported layout)
    layoutCurrent_Tmp_SubItemIndices = {tuple(currItem[1:3]):currItem for currItem in hwndLayout_Current_List}
    layoutCurrent_Tmp_SubItemFetched = {}
    # Initialize variables for storing the layout-data separately
    hwndLayout_Import_RetrievedItems = []
    hwndLayout_Import_RemainingItems = []
    # Iterate through the items in the imported layout: hwndLayout_Import_List
    for currItem in hwndLayout_Import_List:
        # If a window matching the current importItem (by title and path) is currently open
        if (tuple(currItem[1:3]) in layoutCurrent_Tmp_SubItemIndices):
            # Append the current item to the list of existing windows (using the window-handle from corresponding match in hwndLayout_Current_List)
            hwndLayout_Import_RetrievedItems.append([layoutCurrent_Tmp_SubItemIndices[tuple(currItem[1:3])][0]] + currItem[1:])
            # Update dictionary for retrieving unprocessed items in hwndLayout_Current_List
            layoutCurrent_Tmp_SubItemFetched[tuple(currItem[1:3])] = True
        # Else (if none of the currently open windows matches the current importItem)
        else:
            # Append the current item to the list of remaining items (windows (without a handle) to create)
            hwndLayout_Import_RemainingItems.append([False] + currItem[1:])


    # Collect remaining windows from the current layout (these can either be skipped, closed or replaced)
    hwndLayout_Current_RemainingItems = [currItem for currItem in hwndLayout_Current_List if tuple(currItem[1:3]) not in layoutCurrent_Tmp_SubItemFetched]

    # If (unspecified windows can be discarded) and (there are any unused windows open) and (there are remaining windows to create)
    if closeUnspecifiedWindows and len(hwndLayout_Current_RemainingItems) and len(hwndLayout_Import_RemainingItems):
        # Create a variable for storing the retrieved indexes (which should be removed from the lists after being processed)
        processedIndexes = []
        # For each remaining item
        for itemIndex, (currentLayoutItem, importLayoutItem) in enumerate(zip(hwndLayout_Current_RemainingItems, hwndLayout_Import_RemainingItems)):
            # Append the current item to the list of existing windows (using the window-handle from corresponding match in hwndLayout_Current_RemainingItems)
            hwndLayout_Import_RetrievedItems.append([currentLayoutItem[0]] + importLayoutItem[1:])
            # Append the current item-index to the list of processed indexes
            processedIndexes.append(itemIndex)
        # Remove items from the lists of remaining items that has been retrieved/used
        [hwndLayout_Current_RemainingItems.pop(currIndex) for currIndex in sorted(processedIndexes, reverse=True)]
        [hwndLayout_Import_RemainingItems.pop(currIndex) for currIndex in sorted(processedIndexes, reverse=True)]


    import win32gui
    # Retrieve the handle to the topmost window in the Z-index
    hwnd = win32gui.GetTopWindow(None)
    # Create a list to store the handles to the windows in the Z-index
    z_index = []
    # Traverse the Z-index and retrieve the handles to all visible windows
    while hwnd != 0:
        if win32gui.IsWindowVisible(hwnd):
            z_index.append(hwnd)
        hwnd = win32gui.GetWindow(hwnd, win32con.GW_HWNDNEXT)
    # Sort the list of handles based on the position and size of the windows
    z_index.sort(key=lambda hwnd: win32gui.GetWindowRect(hwnd))

    # Print the handles to the windows in the Z-index
    print(len(hwndLayout_Current_List))
    print(z_index)
    print("----")
    print(str(hwndLayout_Current_List))
    print("----")
    # import win32gui
    # import win32con

    # Retrieve the handle to the topmost window in the Z-index
    hwnd = win32gui.GetTopWindow(None)
    # Create a list to store the handles to the windows in the Z-index
    z_index = []
    # Traverse the Z-index and retrieve the handles to all visible windows
    while hwnd != 0:
        if win32gui.IsWindowVisible(hwnd):
            class_name = win32gui.GetClassName(hwnd)
            if class_name == "CabinetWClass":
                z_index.append(hwnd)
        hwnd = win32gui.GetWindow(hwnd, win32con.GW_HWNDNEXT)

    # Print the handles to the windows in the Z-index
    print(sorted(z_index))


# #
#     import win32gui
#     import win32con

    # Specify the list of window handles to retrieve the z-order for
    hwnd_list = [currItem[0] for currItem in hwndLayout_Current_List]

    # Create a list to store the z-order of the specified windows
    z_order = []

    # Traverse the Z-index and retrieve the z-order of the specified windows
    hwnd = win32gui.GetTopWindow(None)
    while hwnd != 0:
        if hwnd in hwnd_list:
            print(str(win32gui.GetWindowPlacement(hwnd)))
            z_order.append(hwnd)
        hwnd = win32gui.GetWindow(hwnd, win32con.GW_HWNDNEXT)

    # Print the z-order of the specified windows
    print('new:')
    print(sorted(z_order))
    print('\n')

    # for x in range(0,len(hwndLayout_Current_List)):
        # print()

    # print(len(hwndLayout_Import_RetrievedItems))
    # print(len(hwndLayout_Import_RemainingItems))
    # print(len(hwndLayout_Current_RemainingItems))

    print('\n')
    print('hwndLayout_Import_RemainingItems:')
    for x in (hwndLayout_Import_RemainingItems):
        print(('- %s' % x))
    print('\n')
    print('hwndLayout_Current_RemainingItems:')
    for x in (hwndLayout_Current_RemainingItems):
        print(('- %s' % x))
    time.sleep(9999)
    #     # If the current item hasn't been retrieved, add it to the list of remaining items (windows (with a handle) to close/replace/skip)
    #     if tuple(currItem[1:3]) not in layoutCurrent_Tmp_SubItemFetched:
    #         closeUnspecifiedWindows
    #         hwndLayout_Current_RemainingItems.append(currItem)

    # # Iterate through the items in the current layout: hwndLayout_Current_List
    # for currItem in hwndLayout_Current_List:
    #     # If the current item hasn't been retrieved, add it to the list of remaining items (windows (with a handle) to close/replace/skip)
    #     if tuple(currItem[1:3]) not in layoutCurrent_Tmp_SubItemFetched:
    #         closeUnspecifiedWindows
    #         hwndLayout_Current_RemainingItems.append(currItem)


    # CREATE WINDOWS AND RETRIEVE THEIR HANDLE
    # Create a variable for storing window-handles of all open windows (used to prevent retrieving the same window more than once)
    winHandles_Processed = [currItem[0] for currItem in hwndLayout_Import_RetrievedItems]
    winHandles_New = [currHandle for currHandle in winHandles_Current if currHandle not in ([currItem[0] for currItem in hwndLayout_Current_List] + winHandles_Processed)]
    print('winHandles_Processed: %s' % str(winHandles_Processed))
    # If windows specified in the layout file should be created if they don't already exist
    if createWindowsThatDontExist:
        # For each item in hwndLayout_Import_RemainingItems (windows specified in layout-data that isn't already open)
        for currIndex, currItem in enumerate(hwndLayout_Import_RemainingItems):
            # Open a window for the current item-path
            os.system(currItem[2])
            # Use a while-loop to wait for the newly created window to initialize
            while True:
                # Retrieve any window-handles that have not already been processed
                shellWindows_Instances = win32.Dispatch('{9BA05972-F6A8-11CF-A442-00A0C90A8F39}')
                winHandles_Current = [currInstance.HWND for currInstance in shellWindows_Instances if currInstance.HWND]
                winHandles_New = [currHandle for currHandle in winHandles_Current if currHandle not in ([currItem[0] for currItem in hwndLayout_Current_List] + winHandles_Processed)]
                # If a new window-handle has been retrieved
                if len(winHandles_New):
                    # Update the current item in hwndLayout_Import_RemainingItems and add the window-handle to winHandles_Processed
                    # print('winHandles_Processed: %s' % str(winHandles_Processed))
                    hwndLayout_Import_RemainingItems[currIndex][0] = winHandles_New[0]
                    winHandles_Processed.append(winHandles_New[0])
                    print('               Title: %s' % str(currItem[1]))
                    print('      winHandles_New: %s' % str(winHandles_New[0]))
                    print('winHandles_Processed: %s' % str(winHandles_Processed))
                    break



    # Close all windows that are not part of the loaded layout-file (if closeUnspecifiedWindows is True)
    winHandles_Close = [currItem for currItem in hwndLayout_Current_List if currItem[0] not in winHandles_Processed]
    [win32api.SendMessage(currWin[0], win32con.WM_CLOSE, 0, 0) for currWin in (winHandles_Close if closeUnspecifiedWindows else [])]


    # Combine hwndLayout_Import_RetrievedItems and hwndLayout_Import_RemainingItems into one list (with skipped windows excluded)
    layoutData_List = [currItem for currItem in (hwndLayout_Import_RetrievedItems + hwndLayout_Import_RemainingItems) if (currItem[0] != False)]
    # Order the items in the list identically to the layout-file
    layoutData_List = (hwndLayout_Import_RetrievedItems + hwndLayout_Import_RemainingItems)
    hwndLayout_Import_ListIndices = {(subItem[1], subItem[2]): currIndex for currIndex, subItem in enumerate(hwndLayout_Import_List)}
    layoutData_List.sort(key=lambda currItem: hwndLayout_Import_ListIndices[(currItem[1], currItem[2])])

    for x in layoutData_List:
        print(x)
    print("\n")
    # For each item in layoutData_List (these are now windows that are currently open)
    for currItem in layoutData_List:
        # Extract current window-data and convert it to valid format for SetWindowPlacement to work
        currWin_ShowCmd = currItem[3]
        currWin_Coordinates = tuple([currItem[4][0], currItem[4][1], (currItem[4][0] + currItem[5][0]), (currItem[4][1] + currItem[5][1])])
        currWin_Placement = tuple([0, currItem[3], (-1, -1), (-1, -1), currWin_Coordinates])
        # Determine whether the current window is already correct or not
        currWin_Identical = (currWin_Placement == (win32gui.GetWindowPlacement(currItem[0])))
        # Apply layout for the current item
        print('%s' % currItem[1])
        print('(BEFORE CHANGING) currWin_Placement: %s' % str(win32gui.GetWindowPlacement(currItem[0])))
        print('      (FROM FILE) currWin_Placement: %s' % str(currWin_Placement))
        win32gui.SetWindowPlacement(currItem[0], currWin_Placement)
        print(' (AFTER CHANGING) currWin_Placement: %s' % str(win32gui.GetWindowPlacement(currItem[0])))
        print('--------------------------------------------------------------------------------------------------')



    # Print information
    numWinsAlreadyOpen = len(hwndLayout_Import_RetrievedItems)
    numWinsOpened = len(hwndLayout_Import_RemainingItems)
    numWinsApplied = (numWinsAlreadyOpen + numWinsOpened)
    numWinsClosed = (len(winHandles_Close) if closeUnspecifiedWindows else 0)
    print('Already open: %s' % numWinsAlreadyOpen)
    print(('Opened windows: %s' % numWinsOpened) if createWindowsThatDontExist else ('Skipped windows: %s' % numWinsOpened))
    print('Applied layout to: %s' % numWinsApplied)
    print('Closed windows: %s' % numWinsClosed)



# --------------------------------------------------------------------------------------------------------------------
# APPLY LAYOUT
# --------------------------------------------------------------------------------------------------------------------
if (len(sys.argv) == 2) and (sys.argv[1] == "Apply"):
    fnExplorerWindows_ApplyLayout()
# --------------------------------------------------------------------------------------------------------------------



# --------------------------------------------------------------------------------------------------------------------
# CREATE LAYOUT
# --------------------------------------------------------------------------------------------------------------------
if (len(sys.argv) == 2) and (sys.argv[1] == "Create"):
    # Retrieve the current layout (as a dict)
    hwndLayout_Current_Dict = (fnExplorerWindows_GetCurrentLayout())[1]
    # Create and write data to the layout-file (using the JSON module)
    with open(layoutFile_FilePath, 'w') as outputFile:
        json.dump(hwndLayout_Current_Dict, outputFile, indent=4, sort_keys=False, separators=(',', ': '))
# --------------------------------------------------------------------------------------------------------------------
