import os
import sys
import re
import urllib.parse
import ctypes
import win32gui
import win32process
import win32con
import win32com.client
import win32com.shell.shellcon as shellcon
# import win32com

import time

# special_folder_name = None
# special_folder_path = None
# Initialize a list of COM objects for the Shell.Application class (File-Explorer Windows)
# shell_window_instances = win32com.client.Dispatch('Shell.Application')

# # Retrieve/generate a list of all the special folder constants ('ShellSpecialFolderConstants')
# special_folder_constants = [getattr(shellcon, name) for name in dir(shellcon) if name.startswith("CSIDL_")]
# special_folders = [shell_window_instances.Namespace(constant) for constant in special_folder_constants]
# filtered_special_folders = list(filter(lambda folder: hasattr(folder, 'Application'), special_folders))
# result = [[special_folder.Self.Name, special_folder.Self.Path] for special_folder in filtered_special_folders]
# # result = [[special_folder.Self.Name, special_folder.Self.Path, win32gui.FindWindowEx(0, 0, None, special_folder.Self.Name)] for special_folder in filtered_special_folders]
# print(result)

# # print((filtered_special_folders[0].Items()))
# # print(dir(filtered_special_folders[0].Self.Application))
# print(filtered_special_folders[0].Self.Name)
# print(filtered_special_folders[0].Self.Path)


# result = [[special_folder.Self.Name, special_folder.Self.Path, win32gui.FindWindowEx(0, 0, None, special_folder.Self.Name)] for special_folder in filtered_special_folders]

# # Get a list of the object's attributes.
# attributes = dir(filtered_special_folders[0])
# print(attributes)
# # Search for an attribute that might be related to the HWND.
# for attr in attributes:
#     if 'hwnd' in attr.lower():
#         print(attr)
# Initialize a list of COM objects for the Shell.Application class (File-Explorer Windows)
# shell_window_instances = win32com.client.Dispatch('Shell.Application')

# # Retrieve/generate a list of all the special folder constants ('ShellSpecialFolderConstants')
# special_folder_constants = [getattr(shellcon, name) for name in dir(shellcon) if name.startswith("CSIDL_")]
# # Create a list of Namespace objects for the special folder constants that have the 'Application' attribute
# special_folders = [shell_window_instances.Namespace(constant) for constant in special_folder_constants if hasattr(constant, 'Application')]
# print(special_folders)
# # Create a list of lists containing the name and path of each Namespace object
# result = [[folder.Self.Name, folder.Self.Path] for folder in special_folders]


# special_folders = [shell_window_instances.Namespace(constant) for constant in special_folder_constants]
# filtered_special_folders = [constant for constant in special_folders if hasattr(constant, 'Application')]

# for x in result:
#     print(x)
# print(filtered_special_folders)
# special_folders = (list(filter(lambda x: hasattr(x, 'Application'), [shell_window_instances.Namespace(current_constant) for current_constant in special_folder_constants])))
# print(len(special_folders))
# print(len(filtered_special_folders))

# print(len(special_folders2))
# special_folders = if hasattr(current_window_instance, 'LocationURL')
# for f in special_folders:
#     if not hasattr(f, 'Application'):
#         print("----")
#         print(dir(f))
#         print("----")
    # special_folders
    # if not f:
    #     print(dir(f))
    #     print(f)
# print(special_folders)

#     # Retrieve the special folder object for the current constant
#     special_folder = shell_window_instances.Namespace(current_constant)
#     # Check if the special folder object was successfully retrieved
#     if special_folder:
#         # Retrieve the name and path of the special folder
#         current_folder_name = special_folder.Self.Name
#         current_folder_path = special_folder.Self.Path
#         print(current_folder_name)
#         print(current_folder_path)
# special_folder_name = None
# special_folder_path = None

# # Initialize a list of COM objects for the Shell.Application class (File-Explorer Windows)
# # Retrieve/generate a list of all the special folder constants ('ShellSpecialFolderConstants')
# # Iterate over the list of special folder constants
# [print(shell_window_instances.Namespace(current_constant).Self.Name, shell_window_instances.Namespace(current_constant).Self.Path) for current_constant in [getattr(shellcon, name) for name in dir(shellcon) if name.startswith("CSIDL_")] if shell_window_instances.Namespace(current_constant)]

# time.sleep(999)

class Window:
    def __init__(self, hwnd, shell_windows_instance, shell_windows_instance_mapping, special_folders_data):
        # Initialize instance variables with data passed from the WindowManager class
        self.hwnd = hwnd
        self.shell_windows_instance = shell_windows_instance
        self.shell_windows_instance_mapping = shell_windows_instance_mapping
        self.special_folders_data = special_folders_data

        # Initialize instance variables with general window data
        self.title = win32gui.GetWindowText(hwnd)
        self.class_name = win32gui.GetClassName(hwnd)
        self.rect = win32gui.GetWindowRect(hwnd)
        self.position = (self.rect[0], self.rect[1])
        self.size = (self.rect[2] - self.rect[0], self.rect[3] - self.rect[1])

        # Initialize instance variables for type-specific window data
        self.process = None
        self.path = None
        self.path_retrieved = False
        self.type = None #SPECIAL, FILE-EXPLORER, OTHER
        # Process [detail-variables]
        self.thread_id = None
        self.process_id = None
        self.process_handle = None
        self_process_executable_path = None

    # def filter_windows_by_conditions():

    def get_conditional_window_data(self):
        """
        Returns a list of dictionaries containing the layout data of each currently open file-explorer window
        Each dictionary contains the following keys:
        - 'handle': The window handle of the file-explorer window (int)
        - 'title': The title of the file-explorer window (str)
        - 'path': The path of the file-explorer window (str)
        - 'z_index': The z-index of the file-explorer window (GetWindow)
        - 'show_state': The window state of the file-explorer window (int)
        - 'always_on_top': Whether or not the file-explorer window is always on top (bool)
        - 'position': The position of the file-explorer window (x, y coordinates) (tuple of int)
        - 'size': The size of the file-explorer window (width, height) (tuple of int)
        """
        # Retrieve the process handle of the window
        hwnd_thread_id, hwnd_process_id = win32process.GetWindowThreadProcessId(self.hwnd)
        hwnd_process_query = (win32con.PROCESS_QUERY_INFORMATION | win32con.PROCESS_VM_READ)
        hwnd_process_handle = ctypes.windll.kernel32.OpenProcess(hwnd_process_query, False, hwnd_process_id)
        self.process = (win32process.GetModuleFileNameEx(hwnd_process_handle, 0) if hwnd_process_handle else None)
        # print(str(hwnd_process_handle))
        # self.process = win32process.GetModuleFileNameEx(hwnd_process_handle, 0)
        print(str(self.process))
        # If a process handle was retrieved
        if hwnd_process_handle:
            # Retrieve and update the instance variable for 'process'
            self.process = win32process.GetModuleFileNameEx(hwnd_process_handle, 0)
            self.process = self.process

            # if self.process:
            #     # self.path = os.path.normpath(urllib.parse.unquote(self.path))
            #     print('title: %s' % str(self.title))
            #     print('class_name: %s' % str(self.class_name))
            #     print('rect: %s' % str(self.rect))
            #     print('position: %s' % str(self.position))
            #     print('position: %s' % str(self.position))
            #     print('process: %s' % str(self.process))
            #     print('%s - path: %s' % ((str(self.path_retrieved), str(self.path))))
            #     print("----")

            # Determine whether or not it is a 'File-Explorer Window'
            is_explorer_folder = (self.class_name == 'CabinetWClass' and 'explorer.exe' in self.process)
            is_special_folder = next((item for item in self.special_folders_data if item[0] == self.title), False)
            # - 'CabinetWClass'
            # - 'explorer.exe'
            # - hwnd_special_folder is True

            # If class and process corresponds with a 'File-Explorer Window'
            if ((self.class_name == 'CabinetWClass') and ('explorer.exe' in self.process)):
                # If this is a 'Special Folder' (special system folders which requires manual handling of path)
                hwnd_special_folder = next((item for item in self.special_folders_data if item[0] == self.title), False)
                if hwnd_special_folder:
                    # Retrieve the path corresponding to the window title (modified with correct prefix based on path type)
                    has_normal_path = (hwnd_special_folder[2] and not bool(re.search(r'::{[\w-]*}', hwnd_special_folder[1])))
                    hwnd_modified_path = (f'file:/{hwnd_special_folder[1]}' if has_normal_path else f'shell:{hwnd_special_folder[1]}')
                    # Update the instance variables for 'path', 'path_retrieved' and 'type'
                    self.path = os.path.normpath(hwnd_modified_path)
                    self.path_retrieved = True
                    self.type = 'Special Folder'

                # If this is a window of type 'File-Explorer Window' or 'Special Folder'
                else:
                    # # Retrieve folder path of the window (through its shell instance)
                    hwnd_shell_instance = self.shell_windows_instance_mapping[self.hwnd]
                    # print(hwnd_shell_instance)
                    # print(type(hwnd_shell_instance))
                    hwnd_shell_path = hwnd_shell_instance.LocationURL
                    self.path = os.path.normpath(hwnd_shell_path)
                    # # Retrieve and update the instance variables
                    # print(self.path)
                    # print(not bool(re.search(r'::{[\w-]*}', hwnd_special_folder[1])))
        # file.write(f"{self.type}\n")

                # Retrieve manually title indicates this to be a 'Special Folder' (store )
                # Retrieve path corresponding to title


                # # Update the instance variable for 'type'
                # self.type = 'File-Explorer Window'

                # print(hwnd_shell_path)
                # print(os.path.normpath(hwnd_shell_path))
                # print(hwnd_shell_instance.LocationURL)
                # print(hwnd_shell_instance.LocationURL)
                # print(hwnd_shell_instance.LocationURL)
#                 # print(hwnd_shell_instance.LocationURL)
# self.title
# self.class_name
# self.rect
# self.position
# # self.size
#                 self.path = os.path.normpath(urllib.parse.unquote(self.path))
#                 print('title: %s' % str(self.title))
#                 print('class_name: %s' % str(self.class_name))
#                 print('rect: %s' % str(self.rect))
#                 print('position: %s' % str(self.position))
#                 print('position: %s' % str(self.position))
#                 print('process: %s' % str(self.process))
#                 print('%s - path: %s' % ((str(self.path_retrieved), str(self.path))))
#                 print("----")
                # # GJØR DETTE TIL SLUTT = urllib.parse.unquote(hwnd_shell_instance.LocationURL).lstrip('/').replace("\\","/")


                # print(hwnd_shell_path)
                # print(hwnd_shell_path2)

                # If no path was returned

                # 'shell:::{B4BFCC3A-DB2C-424C-B029-7FE99A87C641}'
                # '::{B4BFCC3A-DB2C-424C-B029-7FE99A87C641}'
                # hwnd_special_folder = next((item for item in self.special_folders_data if item[0] == self.title), False)
                # print(type(hwnd_shell_instance))
                # print(self.title in self.special_folders_data)
                # print(self.title)
                # print(hwnd_shell_path)
                # shell = win32com.client.Dispatch("Shell.Application")
                # self.path = urllib.parse.unquote(shell.LocationURL).lstrip('/').replace("\\","/")
                # self.path = shell.SpecialFolders(self.title)

            # Else
            elif self.process:
                self.type = "Process Window"
        else:
            self.type = "Unlinked Window"

        # Get the executable filename of the process (if successfully accessed/retrieved)
        # If a process handle wasn't retrieved
        hwnd_process_name = None


    def save(self, file):
        file.write(f"{self.type}\n")
        file.write(f"{self.title}\n")
        file.write(f"{self.class_name}\n")
        file.write(f"{self.position[0]}, {self.position[1]}\n")
        file.write(f"{self.size[0]}, {self.size[1]}\n")
        file.write(f"{self.type}\n")
        if self.path:
            file.write(f"{self.path}\n")
        elif self.process:
            file.write(f"{self.process}\n")

class WindowManager:
    def __init__(self):
        """
        xxxxxxxxxxxxxxxxxxxxxxxxxx
        """
        # Initialize an empty list to store Window objects
        self.all_windows = []

        # Initialize condition: Visible (dette blir en input)
        self.visible_only = False


        # Initialize a COM object instance of the 'Shell.Application' CLSID/ProgID, which
        # represents the desktop shell (allows access to various additional window data).
        CLSID_SHELL_WINDOWS = 'Shell.Application'
        self.shell_windows_instance = win32com.client.Dispatch(CLSID_SHELL_WINDOWS)

        # Initialize a dictionary that maps instance objects to their corresponding hwnd's (window handles).
        # This enables access to the instance by hwnd (without having to re-initialize the COM object).
        self.shell_windows_instance_mapping = {
            window.HWND: window for window in self.shell_windows_instance.Windows()
        }

        # Generate a list of 'ShellSpecialFolderConstants' constants (refers to system folders such as
        # 'Control Panel', 'Recycle Bin', etc), retrieve their valid COM namespaces (containers).
        SPECIAL_FOLDER_CONSTANTS = [getattr(shellcon, name) for name in dir(shellcon) if name.startswith("CSIDL_")]
        special_folder_namespaces = [self.shell_windows_instance.Namespace(constant) for constant in SPECIAL_FOLDER_CONSTANTS]
        filtered_namespaces = list(filter(lambda namespace: hasattr(namespace, 'Application'), special_folder_namespaces))
        # Initialize list with data for each 'special folders' item ([shell.const.Name, shell.const.Path, shell.const.Path.Exists]).
        self.special_folders_data = [[item.Self.Name,item.Self.Path,os.path.exists(item.Self.Path)] for item in filtered_namespaces]




    def update(self):
        # Reset the list of Window objects
        self.all_windows = []
        # Enumerate all windows and pass them to the enum_callback method
        win32gui.EnumWindows(self.enum_callback, None)

    def enum_callback(self, hwnd, _):
        # visibleOnly or AllWindows
        if (self.visible_only and win32gui.IsWindowVisible(hwnd)) or (not self.visible_only):
            # Create a Window object for the current window
            window = Window(hwnd, self.shell_windows_instance, self.shell_windows_instance_mapping, self.special_folders_data)

            # Retrieve type-specific window data for the current window
            # This method will only retrieve conditional window data if the window is one of the defined window types in the application.
            # The retrieved data will be used to classify and handle the window appropriately.
            window.get_conditional_window_data()

            # Add the current window to the list of windows
            self.all_windows.append(window)

    def save(self, filename):
        # Open the file at the specified filepath in write mode
        with open(filename, "w") as file:
            # Iterate through the list of retrieved windows
            for window in self.all_windows:
                # Save the current window to the file
                window.save(file)

def main(args):
    # If the first command-line argument is 'save'
    if args[0] == "save":
        # Create a new WindowManager object
        manager = WindowManager()
        # Update the list of windows in the WindowManager object
        manager.update()
        # Save the windows to the file at the specified filepath
        manager.save(args[1])
    # If the first command-line argument is 'load'
    elif args[0] == "load":
        pass


manager = WindowManager()
manager.update()
manager.save("windows.txt")
