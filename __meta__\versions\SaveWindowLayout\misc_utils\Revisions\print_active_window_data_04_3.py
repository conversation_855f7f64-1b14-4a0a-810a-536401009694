# Import modules
import os
import sys
import re
import time
import random
import ctypes
from ctypes import byref
import urllib.parse
from enum import Enum
# Error handling
import traceback
import pywintypes
# Import pywin32 modules
import comtypes
import win32com.client
from win32com.shell import shell
import win32com.shell.shellcon as shellcon
import win32con
import win32gui
import win32api
import win32process
# Module for coloring the prompt
from ansimarkup import ansiprint, AnsiMarkup, parse

# SUPERBRA REFERANSE: https://github.com/kenjinote/win32/tree/80ee822f6ebcbcc8f60042e0d14a39ef6989c731/desktop-src/shell
# https://learn.microsoft.com/en-us/windows/win32/shell/shellfolderview-selectitem

# Initializes a COM object instance of the 'Shell.Application' CLSID/ProgID.
shell_windows_obj = win32com.client.Dispatch('Shell.Application')

# ADDING A PATH TO THE NAMESPACE
# folder_path = os.path.normpath("D:/Data/Prosjekter/Scripts/WIP/SaveWindowLayout/misc_utils")
# folder = shell_windows_obj.NameSpace(folder_path)
# print(dir(folder))

# Initialize shell namespaces for 'special folders' registered on the system.
csidl_constants = filter(lambda x: x.startswith('CSIDL_'), vars(shellcon))
csidl_values = [getattr(shellcon, const) for const in csidl_constants]
csidl_namespaces = [shell_windows_obj.Namespace(const) for const in csidl_values]
valid_namespaces = (ns for ns in csidl_namespaces if hasattr(ns, 'Application'))

# Create a mapping used to ebable access to the 'special folders' by 'title'.
special_folders = [[ns.Self.Name, ns.Self.Path] for ns in valid_namespaces]
special_folders_mapping = {item[0]: item[1] for item in special_folders}

# For each shell window
for window in shell_windows_obj.Windows():
    print("-"*50)
    # Retrieve the initial window data
    window_type = 'NORMAL'
    window_hwnd = window.HWND
    window_title = window.LocationName
    window_create_cmd = os.path.normpath(urllib.parse.unquote(window.LocationURL))

    # If this is a 'special folder'
    if window_title in special_folders_mapping:
        # Check if the path refers to a GUID (global unique identification number).
        csidl_path = special_folders_mapping[window_title]
        csidl_path_guid_match = bool(re.search(r'::{[\w-]*}', csidl_path))
        csidl_path_is_guid = csidl_path_guid_match if not os.path.exists(csidl_path) else False
        # Transform the path into a cmd-command (using 'Shell:{GUID}' or 'File:/URI').
        command_prefix = 'Shell:' if csidl_path_is_guid else 'File:/'
        create_command = os.path.normpath(urllib.parse.unquote(f'{command_prefix}{csidl_path}'))
        # Overwrite 'window_type' and 'window_create_cmd'
        window_type = 'SPECIAL'
        window_create_cmd = create_command

    if True:
        # FOLDER(CONTENT):
        # ------------------------------------------------------------
        # RETRIEVE ITEM IN FOCUS IN FOLDER (STR)
        focused_item_object = window.Document.FocusedItem
        focused_item = focused_item_object.Name if focused_item_object else focused_item_object
        print('focused_item: %s' % focused_item)
        # ------------------------------------------------------------
        # RETRIEVE SELECTED FILES IN FOLDER (LIST)
        selected_files_object = window.Document.SelectedItems()
        selected_files = [current.Name for current in selected_files_object]
        print('selected_files: %s' % selected_files)
        # ------------------------------------------------------------
        # RETRIEVE: ALL FILES IN FOLDER
        files_in_folder_objs = window.Document.Folder.Items()
        filenames = [current.Name for current in files_in_folder_objs]
        filepaths = [current.Path for current in files_in_folder_objs]
        print('filenames: %s' % filenames)
        # ------------------------------------------------------------


        # ------------------------------------------------------------
        # INVOKE ACTIONS ON FILE FROM THE RIGHT-CLICK CONTEXT MENU
        filename_to_set_in_focus = ["windows.txt"]
        mathing_file_objects = [current for current in window.Document.Folder.Items() if current.Name in filename_to_set_in_focus]
        if len(mathing_file_objects):
            # Get available attributes (verbs) that can be invoked on the file
            file_obj_attributes_object = mathing_file_objects[0].Verbs()
            file_obj_attributes_names = [attr.Name for attr in file_obj_attributes_object]
            print('num attributes available for item: %s' % len(file_obj_attributes_names))
            # Execute operation "Open" from the context menu on the file
            # mathing_file_objects[0].InvokeVerb("Open")
        # ------------------------------------------------------------
        # SELECT ALL FILES IN FOLDER
        # https://learn.microsoft.com/en-us/windows/win32/shell/shellfolderview-selectitem
        files_in_folder_objs = window.Document.Folder.Items()
        for file_object in files_in_folder_objs:
            window.Document.SelectItem(file_object, 1)
        # ------------------------------------------------------------
        # SELECT FILES IN FOLDER BY STRINGS (FILENAMES)
        names_to_select_strings = ["windows.txt", "New foslder", "WSHO_Classes_Notes.py","reference_utils.py","Revisions"]
        mathing_file_objects = [current for current in window.Document.Folder.Items() if current.Name in names_to_select_strings]
        # print([o.Name for o in mathing_file_objects])
        for file_object in mathing_file_objects:
            window.Document.SelectItem(file_object, 1)
        # ------------------------------------------------------------
        # SET FILE IN FOCUS
        names_to_select_strings = ["windows.txt"]
        mathing_file_objects = [current for current in window.Document.Folder.Items() if current.Name in names_to_select_strings]
        # print([o.Name for o in mathing_file_objects])
        for file_object in mathing_file_objects:
            window.Document.SelectItem(file_object, 16)



        # ------------------------------------------------------------
        # RETRIEVE FOLDER(VIEW):
        # Folder Zoom / Icon Size (px) (int)
        current_view_zoom = window.Document.IconSize
        print('current_view_zoom: %s' % current_view_zoom)
        # Set IconSize
        # window.Document.IconSize = 256
        # ------------------------------------------------------------
        # View-mode (List/Details/etc) (int --> Str)
        # FOLDER_VIEW_MODES:
        # -1, 'FVM_AUTO'
        # 1, 'FVM_ICON'
        # 2, 'FVM_SMALLICON'
        # 3, 'FVM_LIST'
        # 4, 'FVM_DETAILS'
        # 5, 'FVM_THUMBNAIL'
        # 6, 'FVM_TILE'
        # 7, 'FVM_THUMBSTRIP'
        # 8, 'FVM_CONTENT'
        current_view_mode = window.Document.CurrentViewMode
        print('current_view_mode: %s' % current_view_mode)
        # Set to details
        # window.Document.CurrentViewMode = 4
        # ------------------------------------------------------------
        # RETRIEVE/SET SORT-BY
        # window.Document.SortColumns = "prop:-System.DateModified;"
        sort_column = window.Document.SortColumns
        sort_column = sort_column if focused_item else False
        print('sort_column: %s' % sort_column)
        # Viktig: Denne kan feile om man prøver å sette read only (control panel etc)
        if sort_column:
            window.Document.SortColumns = "prop:-System.Size;"
        # ------------------------------------------------------------
        # RETRIEVE GROUP-BY
        group_by = window.Document.GroupBy
        window.Document.GroupBy = "System.Null"
        print('group_by: %s' % group_by)
        # ------------------------------------------------------------



time.sleep(1111)

# used to provide paths for 'special folders'.

# Use the shell instance to create 'special folders' shell namespaces.

# If this is a Windows File-Explorer Window (typically a folder/directory)

#  All shell windows can be opened through explorer.exe, the way this is done
#  is by retrieving their path, then converting the path into a create-command.


        #   this is to make the path executable (in that it creates the actual window).
        #

        # - Update the instance variable 'type' with the identifier 'SPECIAL_FOLDER'.
        # - Check if the path refers to a GUID (global unique identification number).
        # - Update the instance variable 'create_cmd' with the modified path-command.
#         window_create_cmd = create_command
#         window_type = WindowType.SPECIAL_FOLDER

#     window_title = window.LocationName
#     window_title = window.LocationName
#     window_title = window.LocationName
#     window_title = window.LocationName
#     window_title = window.LocationName

# shell_windows = [window for window in shell_windows_instance.Windows()]
#         self._placement = win32gui.GetWindowPlacement(self.hwnd)
#         self._rect = win32gui.GetWindowRect(self.hwnd)

#         # Initialize instance variables with general window data
#         self.hwnd_visibility_state = win32gui.IsWindowVisible(self.hwnd)
#         self.hwnd_title = win32gui.GetWindowText(self.hwnd)
#         self.hwnd_class = win32gui.GetClassName(self.hwnd)
#         self.hwnd_controls_state = self._placement[1]
#         self.hwnd_position = (self._rect[0], self._rect[1])
#         self.hwnd_size = (self._rect[2] - self._rect[0], self._rect[3] - self._rect[1])

#         # Initialize instance variables for type-specific window data
#         self.hwnd_process = None
#         self.hwnd_process_id = None
#         self.hwnd_create_cmd = None
#         self.hwnd_type = None


# print(filter(lambda x: x.startswith('CSIDL_'), vars(shellcon)))
# print([lambda x: x.startswith('CSIDL_'), vars(shellcon)])

# csidl_folder_id = list(filter(lambda x: x.startswith('CSIDL_'), vars(shellcon)))
# print(len(a))
#  used to provide paths for 'special folders'.

# # Generate a list of 'special folders' constants registered on the system.
# csidl_folder_id = [const for const in dir(shellcon) if const.startswith("CSIDL_")]
# csidl_folder_constants = [getattr(shellcon, name) for name in csidl_constants]
# # Initializes a COM object instance of the 'Shell.Application' CLSID/ProgID.
# shell_windows_instance = win32com.client.Dispatch('Shell.Application')


# shell_windows = [window for window in shell_windows_instance.Windows()]

# csidl_namespaces = [shell_windows_instance.Namespace(const) for const in csidl_values]
# valid_namespaces = (ns for ns in csidl_namespaces if hasattr(ns, 'Application'))
# special_folders = [[ns.Self.Name, ns.Self.Path] for ns in valid_namespaces]



# # ----------------------------------------------------------------------------
# # In Windows, 'special folder' is a folder represented as an interface rather
# # than a path, such as 'Desktop', 'Control Panel', 'Recycle Bin', etc.
# # These are registered on the system as constants identified by 'CSIDL'.
# #
# # Create shell object namespaces for each retrieved 'CSIDL' constant.
# csidl_namespaces = [shell_windows_instance.Namespace(const) for const in csidl_values]
# valid_namespaces = (ns for ns in csidl_namespaces if hasattr(ns, 'Application'))
# # Retrieve identifier ('Name') and path ('Path') from each 'special folder'.
# special_folders = [[ns.Self.Name, ns.Self.Path] for ns in valid_namespaces]


# shell_windows_normal =
# shell_windows_special =

# #
# for shell_folder in shell_windows:


# normal_folders_shell =
# special_folders_shell =

# normal_shell_windows = (w for w in shell_windows if w.HWND not in special_folders_mapping.keys())
# special_shell_windows = filterfalse(lambda x: x.HWND not in special_folders_mapping.keys(), shell_windows)
# # Normal shell windows
# normal_shell_windows = [window for window in shell_windows if window.HWND not in special_folders_mapping.keys()]
# # Special shell windows
# special_shell_windows = [window for window in shell_windows if window.HWND in special_folders_mapping.keys()]


# shell_folders_normal =
# shell_folders_special =
# print(shell_windows_mapped)
# # If this is a Windows File-Explorer Window (typically a folder/directory)
# if self.hwnd_class == 'CabinetWClass':
#     # Retrieve the folder path through its shell instance
#     hwnd_shell_instance = shell_windows_instance_mapping[self.hwnd]
#     hwnd_shell_path = hwnd_shell_instance.LocationURL

#     # If it's a 'SPECIAL_FOLDER' (explorer window without retrievable path):
#     # - Update the instance variable 'type' with the identifier 'SPECIAL_FOLDER'.
#     # - Check if the path refers to a GUID (global unique identification number).
#     # - Transform the path into a cmd-command (using 'Shell:{GUID}' or 'File:/URI'),
#     #   this is to make the path executable (in that it creates the actual window).
#     # - Update the instance variable 'create_cmd' with the modified path-command.
#     if self.hwnd_title in special_folders_mapping:
#         self.hwnd_type = WindowType.SPECIAL_FOLDER
#         folder_path = special_folders_mapping[self.hwnd_title]
#         folder_path_guid_match = bool(re.search(r'::{[\w-]*}', folder_path))
#         folder_path_is_guid = folder_path_guid_match if not os.path.exists(folder_path) else False
#         command_prefix = 'Shell:' if folder_path_is_guid else 'File:/'
#         create_command = os.path.normpath(urllib.parse.unquote(f'{command_prefix}{folder_path}'))
#         self.hwnd_create_cmd = create_command

windows_all_hwnds = []



# Retrieve all hwnd's
current_hwnd = win32gui.GetTopWindow(None)
while current_hwnd:
    windows_all_hwnds.append(current_hwnd)
    current_hwnd = win32gui.GetWindow(current_hwnd, win32con.GW_HWNDNEXT)
print(len(windows_all_hwnds))


# Retrieve hwnd's that are part of shell windows
shell_hwnds = []
for hwnd in windows_all_hwnds:
    if hwnd in shell_windows_mapped:
        shell_hwnds.append(shell_windows_mapped[hwnd])

print('any windows: %s' % len(windows_all_hwnds))
print('shell windows: %s' % len(shell_hwnds))
# print(dir(shell_hwnds[0]))
# print(dir(shell_hwnds[0].Document))
# print(dir(shell_hwnds[0].Document.Folder))

# hwnd
# hwnd_visibility_state
# hwnd_title
# hwnd_class
# hwnd_controls_state
# hwnd_position
# hwnd_size
# hwnd_process
# hwnd_process_id
# hwnd_create_cmd
# hwnd_type
# hwnd_placement
# hwnd_rect




for hwnd in shell_hwnds:
    #
    print('-' * 100)

    # ------------------------------------------------------------
    # OBJ(FOLDER):
    # Title
    current_folder_title = hwnd.LocationName
    print('current_folder_title: %s' % current_folder_title)
    # ------------------------------------------------------------
    # Path
    current_folder_path = hwnd.LocationURL
    print('current_folder_path: %s' % current_folder_path)
    # ------------------------------------------------------------
    # FOLDER(CONTENT):
    # Focused Filename (1) (str)
    focused_item_object = hwnd.Document.FocusedItem
    focused_item = focused_item_object.Name if focused_item_object else focused_item_object
    print('focused_item: %s' % focused_item)
    # ------------------------------------------------------------
    # Selected Filenames (list)
    selected_files = [current.Name for current in hwnd.Document.SelectedItems()]
    print('selected_files: %s' % selected_files)
    # ------------------------------------------------------------


    # ------------------------------------------------------------
    # FOLDER(VIEW):
    # Folder Zoom / Icon Size (px) (int)
    current_view_zoom = hwnd.Document.IconSize
    print('current_view_zoom: %s' % current_view_zoom)
    # ------------------------------------------------------------
    # View-mode (List/Details/etc) (int --> Str)
    FOLDER_VIEW_MODE = [
                            [-1, 'FVM_AUTO'],
                            [1, 'FVM_ICON'],
                            [2, 'FVM_SMALLICON'],
                            [3, 'FVM_LIST'],
                            [4, 'FVM_DETAILS'],
                            [5, 'FVM_THUMBNAIL'],
                            [6, 'FVM_TILE'],
                            [7, 'FVM_THUMBSTRIP'],
                            [8, 'FVM_CONTENT'],
                        ]
    current_view = hwnd.Document.CurrentViewMode
    print('current_view: %s' % str(FOLDER_VIEW_MODE[current_view]))
    # ------------------------------------------------------------
    # Sort-by
    sort_column = hwnd.Document.SortColumns
    sort_column = sort_column if focused_item else False
    print(sort_column)
    # Viktig: Denne kan feile om man prøver å sette read only (control panel etc)
    # if sort_column:
    #     hwnd.Document.SortColumns = "prop:-System.Size;"
    # ------------------------------------------------------------
    # ------------------------------------------------------------

# shell = win32com.client.Dispatch("Shell.Application")
# windows = shell.Windows()

# # Iterate through all the open windows
# for i in range(windows.Count):
#     window = windows.Item(i)
#     print("Title: ", window.LocationName)
#     print("URL: ", window.FullName)

# time.sleep(9999)
# Resize terminal window (PosX, PosY, Width, Height)
cmd_hwnd = win32gui.GetForegroundWindow()
win32gui.MoveWindow(cmd_hwnd, 160, 120, 1600, 720, True)


# GET ACTIVE/FOREGROUND WINDOW DATA
def get_active_window_info():
    # Get foreground window hwnd
    hwnd = win32gui.GetForegroundWindow()
    if not hwnd:
        return False

    # These will fail if not window
    # if not win32gui.IsWindow(hwnd):
    #     return False

    # Get the current monitor
    monitor_handle = win32api.MonitorFromWindow(hwnd, win32con.MONITOR_DEFAULTTONEAREST)
    monitor_info = win32api.GetMonitorInfo(monitor_handle)
    monitor_device = (monitor_info["Device"])

    # Get general window data: Intermediate values used to compute state/position/size
    hwnd_placement = win32gui.GetWindowPlacement(hwnd)
    hwnd_rect = win32gui.GetWindowRect(hwnd)
    hwnd_controls_state = hwnd_placement[1]
    hwnd_position = (hwnd_rect[0], hwnd_rect[1])
    hwnd_size = (hwnd_rect[2] - hwnd_rect[0], hwnd_rect[3] - hwnd_rect[1])

    # Get general hwnd (Error if 'GetWindowPlacement' or 'GetWindowRect')
    hwnd_visibility_state = win32gui.IsWindowVisible(hwnd)
    hwnd_title = win32gui.GetWindowText(hwnd)
    hwnd_class = win32gui.GetClassName(hwnd)

    # Prepare data for windows with a process handle
    hwnd_process_path = None
    hwnd_process_id = None

    # win32api: Retrieve the executable filename by accessing the current window's process
    # hwnd_thread_id, hwnd_process_id = win32process.GetWindowThreadProcessId(hwnd)
    # hwnd_process_handle = win32api.OpenProcess(win32con.PROCESS_QUERY_INFORMATION, False, hwnd_process_id)

    # CTYPES: Retrieve the process handle of the window
    hwnd_thread_id, hwnd_process_id = win32process.GetWindowThreadProcessId(hwnd)
    hwnd_process_query = (win32con.PROCESS_QUERY_INFORMATION | win32con.PROCESS_VM_READ)
    hwnd_process_handle = ctypes.windll.kernel32.OpenProcess(hwnd_process_query, False, hwnd_process_id)
    # If a process handle was optained
    if hwnd_process_handle:
        # Get the window style
        # style = win32api.GetWindowLong(hwnd_process_handle, win32con.GWL_STYLE)
        # # Check the window style for the specific view setting
        # if style & win32con.LVS_TYPEMASK == win32con.LVS_ICON:
        #     print("Current view: Extra large icons")
        # elif style & win32con.LVS_TYPEMASK == win32con.LVS_SMALLICON:
        #     print("Current view: Small icons")
        # elif style & win32con.LVS_TYPEMASK == win32con.LVS_LIST:
        #     print("Current view: List")
        # elif style & win32con.LVS_TYPEMASK == win32con.LVS_REPORT:
        #     print("Current view: Details")
        # else:
        #     print("Current view: unknown")
        try:
            hwnd_process_path = win32process.GetModuleFileNameEx(hwnd_process_handle, 0)
        except pywintypes.error:
            print(traceback.format_exc())


    # Return a dictionary with keys and values for each piece of information
    return {
        "monitor":monitor_device,
        "title":hwnd_title,
        "class":hwnd_class,
        "hwnd":hwnd,
        "visibility":hwnd_visibility_state,
        "controls_state":hwnd_controls_state,
        "position":hwnd_position,
        "size":hwnd_size,
        "placement":hwnd_placement,
        # "rect":hwnd_rect,
        "process_path":hwnd_process_path,
        "process_id":hwnd_process_id
    }


# PRINT COLOR RANGE
# for color_int in range(0,255):
#     os.system('color')
#     color_print = lambda x: ansiprint(f"<fg {color_int}>{x}</fg {color_int}>")
#     color_print('%s: this is the current color' % color_int)


# Specify colors to exclude (dark colors)
dark_colors_exclude = [0] + list(range(16, 22)) + list(range(52, 55)) + list(range(58, 64)) + list(range(232, 241))


# Store the previous window
previous_window_data = None
while True:
    # Get the active window information
    active_window_data = get_active_window_info()
    # If any changes has been made to the current window (continous check)
    if (active_window_data != False) and (active_window_data != previous_window_data):
        try:
            # Enable ANSI escapes and specify a random color (to print to terminal)
            os.system('color')
            color_int = random.randint(15,255)
            while color_int in dark_colors_exclude:
                color_int = random.randint(15,255)
            color_print = lambda x: ansiprint(f"<fg {color_int}>{x}</fg {color_int}>")

            # Check specific changes
            monitor_changed = (previous_window_data and (active_window_data["monitor"] != previous_window_data["monitor"]))
            hwnd_changed = (previous_window_data and (active_window_data["hwnd"] != previous_window_data["hwnd"]))
            pos_changed = (previous_window_data and (active_window_data["position"] != previous_window_data["position"]))
            size_changed = (previous_window_data and (active_window_data["size"] != previous_window_data["size"]))
            placement_changed = (previous_window_data and (active_window_data["placement"] != previous_window_data["placement"]))

            # If the window handle has been changed, log information
            if hwnd_changed or size_changed or pos_changed:
                # Set cmd title (to monitor)
                os.system('TITLE "%s   |   Size:%s   |   Pos:%s"' % (str(active_window_data["monitor"]), str(active_window_data["size"]), str(active_window_data["position"])))


                tmpData = {}
                tmpData.update(active_window_data)
                del tmpData["monitor"]

                # Print separator line relative to window width
                cmd_hwnd_rect = win32gui.GetWindowRect(cmd_hwnd)
                cmd_hwnd_width = (cmd_hwnd_rect[2] - cmd_hwnd_rect[0])
                cmd_line_width = int(cmd_hwnd_width * 0.11)
                color_print("-" * (cmd_line_width))
                # Print (right-justified) window data
                keys = [str(item) for item in list(tmpData.keys())]
                values = [str(item) for item in list(tmpData.values())]
                max_key_length = max(len(key) for key in keys)
                for key, value in zip(keys, values):
                    color_print(key.rjust(max_key_length + 20) + " : " + value)
            # Update the handle to the previous window
            previous_window_data = active_window_data
            # Wait for a short period of time before checking the active window again
            time.sleep(0.15)
        except:
            # color_print('<fg 15>ERROR: %s</fg 15>' % str(active_window_data))
            print(str(previous_window_data))
            print(str(active_window_data))
            print(win32gui.GetForegroundWindow())
            pass
