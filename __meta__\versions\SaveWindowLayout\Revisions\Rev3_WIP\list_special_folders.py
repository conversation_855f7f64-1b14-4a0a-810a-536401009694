import os
import time
import subprocess
import win32com.client
import win32com.shell.shell as shell
import win32com.shell.shellcon as shellcon

# ------------------------------------------------------------------------------------------------
# 1: RETRIEVE SPECIAL FOLDER PATHS DYNAMICALLY

# Retrieve/generate a list of all the special folder constants ('ShellSpecialFolderConstants')
special_folder_constants = [getattr(shellcon, name) for name in dir(shellcon) if name.startswith("CSIDL_")]


# Retrieve/generate a list of all the special folder local clsid's
special_folder_local_clsids = [name for name in dir(shellcon) if name.startswith("CSIDL_")]

# Create a list to store the retrieved names and paths of special folders
special_folders_retrieved = []
# Create an instance of the Shell Application object
obj_shell = win32com.client.Dispatch("Shell.Application")
# Iterate over the list of special folder constants
for current_constant in special_folder_constants:
    # Retrieve the special folder object for the current constant
    special_folder = obj_shell.Namespace(current_constant)
    # Check if the special folder object was successfully retrieved
    if special_folder:
        # Retrieve the name and path of the special folder
        current_folder_name = special_folder.Self.Name
        current_folder_path = special_folder.Self.Path

        # Check: https://learn.microsoft.com/en-us/windows/win32/shell/folder-getdetailsof
        # print(special_folder.GetDetailsOf(special_folder, 2))

        # Opens the current folder
        # obj_shell.Open(special_folder)

        # Check
        # print(dir(obj_shell))
        # print((obj_shell.Explore))
        # time.sleep(5)

        # Eksempel:
        # Name: Printers, Path: ::{21EC2020-3AEA-1069-A2DD-08002B30309D}\::{2227A280-3AEA-1069-A2DE-08002B30309D}
        # shell:::{21EC2020-3AEA-1069-A2DD-08002B30309D}\::{2227A280-3AEA-1069-A2DE-08002B30309D}


        # Add the constant, name and path of the current special folder to the result
        special_folders_retrieved.append([current_constant, current_folder_name, current_folder_path])
        # Print name/path
        print(f"Name: {current_folder_name}, Path: {current_folder_path}")

print(len(special_folders_retrieved))

for index, item in enumerate(special_folders_retrieved):
    print('index: %s | constant: %s | name: %s | path: %s' % (index, item[0], item[1], item[2]))

    # Check: Må separere mellom shell/normal path
    # openPathCommand = ('start /min explorer shell:%s' % item[2])
    openPathCommand = ('start explorer "shell:%s"' % item[2])
    print(openPathCommand)
    # openPathCommandResult = subprocess.Popen(openPathCommand, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    time.sleep(1)
time.sleep(9999)
# ------------------------------------------------------------------------------------------------


# ------------------------------------------------------------------------------------------------
# 2: SPECIFY SPECIAL FOLDER CONSTANTS MANUALLY
# CONSTANTS FOR SPECIAL FOLDERS IN WINDOWS
# https://www.autoitscript.com/forum/topic/42394-constants-for-special-folders-in-windows/
INTERNET_EXPLORER = 0x1
PROGRAMS = 0x2
CONTROL_PANEL = 0x3
PRINTERS_AND_FAXES = 0x4
MY_DOCUMENTS = 0x5
FAVORITES = 0x6
STARTUP = 0x7
MY_RECENT_DOCUMENTS = 0x8
SENDTO = 0x9
RECYCLE_BIN = 0xa
START_MENU = 0xb
MY_MUSIC = 0xd
MY_VIDEOS = 0xe
DESKTOP = 0x10
MY_COMPUTER = 0x11
MY_NETWORK_PLACES = 0x12
NETHOOD = 0x13
FONTS = 0x14
TEMPLATES = 0x15
ALL_USERS_START_MENU = 0x16
ALL_USERS_PROGRAMS = 0x17
ALL_USERS_STARTUP = 0x18
ALL_USERS_DESKTOP = 0x19
APPLICATION_DATA = 0x1a
PRINTHOOD = 0x1b
LOCAL_SETTINGS_APPLICATION_DATA = 0x1c
ALL_USERS_FAVORITES = 0x19
LOCAL_SETTINGS_TEMPORARY_INTERNET_FILES = 0x20
COOKIES = 0x21
LOCAL_SETTINGS_HISTORY = 0x22
ALL_USERS_APPLICATION_DATA = 0x23
WINDOWS = 0x24
SYSTEM32 = 0x25
PROGRAM_FILES = 0x26
MY_PICTURES = 0x27
USER_PROFILE = 0x28
COMMON_FILES = 0x2b
ALL_USERS_TEMPLATES = 0x2e
ADMINISTRATIVE_TOOLS = 0x2f
NETWORK_CONNECTIONS = 0x31
CD_BURNING_FOLDER = 0x003b

# Store the constant values as hex in a list
# The prefix '0x' means hexadecimal (base 16) values, which means that this is actually the following integers: 1-11 + 13-14 +16-28 + 32-40 + 43 + 46-47 + 49 + 59
special_folder_constants = [0x1,0x2,0x3,0x4,0x5,0x6,0x7,0x8,0x9,0xa,0xb,0xd,0xe,0x10,0x11,0x12,0x13,0x14,0x15,0x16,0x17,0x18,0x19,0x1a,0x1b,0x1c,0x19,0x20,0x21,0x22,0x23,0x24,0x25,0x26,0x27,0x28,0x2b,0x2e,0x2f,0x31,0x003b]





# ------------------------------------------------------------------------------------------------
# TESTING
# # Get a list of all the special folder special_folders_constants
# special_folders_constants = [getattr(shellcon, name) for name in dir(shellcon) if name.startswith("CSIDL_")]
# constantsNames = [name for name in dir(shellcon) if name.startswith("CSIDL_")]
# # constantsNames = [name for name in dir(shellcon)]
# for name in constantsNames:
#     print(name)
#     try:
#         test = shell.SHGetFolderPath(0, exec("shellcon.%s" % name), None, 0)
#         print(test)
#     except:
#         pass
# print(aa)
# getattr(shellcon, CLSID_ExplorerBrowser)
# print(shell.SHGetFolderPath(0, shellcon.CLSID_ExplorerBrowser, None, 0))
# ------------------------------------------------------------------------------------------------

# ------------------------------------------------------------------------------------------------
# TESTING
# Create a dictionary that maps the special_folders_constants to their names, paths, and CLSIDs
# constants_dict = {}
# obj_shell = win32com.client.Dispatch("Shell.Application")
# for index, constant in enumerate(special_folders_constants):
#     try:
#         # Get the folder item object
#         folder_item = obj_shell.NameSpace(constant)
#         # Get the folder's name, path, and PIDL
#         name = folder_item.Self.Name
#         path = folder_item.Self.Path
#         get_path = lambda name: shell.SHGetFolderPath(0, getattr(shellcon, name), None, 0).encode('utf8')
#         print(get_path(constantsNames[index]))
#     except:
#         pass
    # pidl, _ = shell.SHParseDisplayName(path)
    # Get the folder's CLSID
    # data = shell.SHGetDataFromIDList(pidl, shell.SHGDFIL_FINDDATA)
    # clsid = data.strFile
    # Add the folder's information to the dictionary
    # constants_dict[constant] = (name, path, clsid)

# Print the dictionary
# print(constants_dict)

# # objShell = win32com.client.Dispatch("WScript.Shell")
# allUserDocs = objShell.SpecialFolders("AllUsersDesktop")
# print(allUserDocs)
# ------------------------------------------------------------------------------------------------


# ------------------------------------------------------------------------------------------------
# TESTING
# def get_special_folder_pidl(folder):
#     # Get the folder item object
#     obj_shell = win32com.client.Dispatch("Shell.Application")
#     folder_item = obj_shell.NameSpace(folder)
#     # Get the folder's PIDL
#     return folder_item.ParseName("").AbsolutePidl

# # Get a list of all the special folder special_folders_constants
# # special_folders_constants


# # Get a list of all the special folder special_folders_constants
# special_folders_constants = [getattr(shellcon, name) for name in dir(shellcon) if name.startswith("CSIDL_")]

# # Create a dictionary that maps the special_folders_constants to their names, paths, and CLSIDs
# constants_dict = {}
# obj_shell = win32com.client.Dispatch("Shell.Application")
# for constant in special_folders_constants:
#     # Get the folder item object
#     folder_item = obj_shell.NameSpace(constant)
#     # Get the folder's name and path
#     name = folder_item.Self.Name
#     path = folder_item.Self.Path
#     # Get the folder's PIDL
#     pidl = get_special_folder_pidl(constant)
#     # Get the folder's CLSID
#     data = shell.SHGetDataFromIDList(pidl, shell.SHGDFIL_FINDDATA)
#     clsid = data.strFile
#     # Add the folder's information to the dictionary
#     constants_dict[constant] = (name, path, clsid)

# # Print the dictionary
# print(constants_dict)
# ------------------------------------------------------------------------------------------------


# ------------------------------------------------------------------------------------------------
# TESTING
# def get_special_folder_pidl(folder):
#     # Get the folder's path
#     obj_shell = win32com.client.Dispatch("Shell.Application")
#     folder_item = obj_shell.NameSpace(folder)
#     path = folder_item.Self.Path
#     # Get the folder's PIDL
#     pidl, _ = shell.SHParseDisplayName(None, path, 0, 0)
#     return pidl

# # Get a list of all the special folder special_folders_constants
# special_folders_constants = [getattr(shellcon, name) for name in dir(shellcon) if name.startswith("CSIDL_")]

# # Create a dictionary that maps the special_folders_constants to their names, paths, and CLSIDs
# constants_dict = {}
# obj_shell = win32com.client.Dispatch("Shell.Application")
# for constant in special_folders_constants:
#     # Get the folder item object
#     folder_item = obj_shell.NameSpace(constant)
#     # Get the folder's name, path, and PIDL
#     name = folder_item.Self.Name
#     path = folder_item.Self.Path
#     pidl = get_special_folder_pidl(constant)
#     # Get the folder's CLSID
#     data = shell.SHGetDataFromIDList(pidl, shell.SHGDFIL_FINDDATA)
#     clsid = data.strFile
#     # Add the folder's information to the dictionary
#     constants_dict[constant] = (name, path, clsid)

# # Print the dictionary
# print(constants_dict)


# # Print the dictionary
# print(constants_dict)
# ------------------------------------------------------------------------------------------------






# ------------------------------------------------------------------------------------------------
# TESTING
# def get_special_folder_clsid(folder):
#     # Get the folder item object
#     obj_shell = win32com.client.Dispatch("Shell.Application")
#     folder_item = obj_shell.NameSpace(folder)
#     print(dir(folder_item))
#     pidl = folder_item.GetIDList()
#     # Get the folder's CLSID
#     data = shell.SHGetDataFromIDList(pidl, shell.SHGDFIL_FINDDATA)
#     clsid = data.strFile
#     print(folder_item)
#     # Get the folder's CLSID
#     clsid = folder_item.GetDetailsOf(folder_item, 0)
#     print(clsid)
#     return clsid
# # import win32com.shell.shellcon as shellcon
# import win32com.shell.shell as shell

# def get_special_folder_pidl(folder):
#     # Get the folder's path
#     obj_shell = win32com.client.Dispatch("Shell.Application")
#     folder_item = obj_shell.NameSpace(folder)
#     print(dir(folder_item.Self))
#     print(folder_item.Self.Path)
#     path = folder_item.Self.Path
#     # Get the folder's PIDL
#     pidl, _ = shell.SHParseDisplayName(path)
#     return pidl

# # Get the PIDL of the Desktop special folder
# pidl = get_special_folder_pidl(shellcon.CSIDL_ALTSTARTUP)
# print(pidl)
# Get the CLSID of the Desktop special folder
# clsid = get_special_folder_clsid(shellcon.CSIDL_ALTSTARTUP)
# print(clsid)  # prints "{B4BFCC3A-DB2C-424C-B029-7FE99A87C641}"

# # open_special_folder(MY_NETWORK_PLAC

# open_special_folder(special_folders_constants[2])
# ------------------------------------------------------------------------------------------------


# ------------------------------------------------------------------------------------------------
# TESTING
# shell = win32com.client.Dispatch("WScript.Shell")
# print(dir(shell.ExpandEnvironmentStrings))
# print((shell.ExpandEnvironmentStrings))
# # print((shell.ExpandEnvironmentStrings()))
# # Get the ShellSpecialFolderConstants object
# special_folders_constants = shell.ShellSpecialFolderConstants

# # Get the list of attributes of the object
# attributes = dir(special_folders_constants)

# # Print the attributes that represent the special_folders_constants
# for attribute in attributes:
#     if not attribute.startswith('__'):
#         print(f'{attribute}: {getattr(special_folders_constants, attribute)}')


#
# shell = win32com.client.Dispatch("WScript.Shell").ShellSpecialFolderConstants
# common_folders = shell.ShellSpecialFolderConstants()
# print(common_folders)

# # Get the special folders object
# special_folders = shell.SpecialFolders
# print((special_folders.Count()))
# print(dir(special_folders.Count))
# # Get the names of the special folders
# folder_names = [name for name in special_folders]

# # Print the names and paths of the special folders
# for name in folder_names:
#     path = special_folders.Item(name)
#     print(f'{name}: {path}')
# ------------------------------------------------------------------------------------------------
