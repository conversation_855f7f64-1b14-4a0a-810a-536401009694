{"folders": [{"name": "[ Py_Win32WindowMonitor ]", "path": "./.VENV", "folder_exclude_patterns": ["*"], "file_exclude_patterns": ["*"]}, {"name": "LOCAL - Not Tracked", "path": "C:/Users/<USER>/Desktop/PRJ/GIT_DISCONNECTED/win32windowmonitor_py"}, {"name": "Project - Root", "path": ".", "folder_exclude_patterns": ["win32windowmonitor_py"]}, {"name": "Project - Code", "path": "win32windowmonitor_py"}], "build_systems": [{"name": "-> win32windowmonitor_py \t <Py_Prj>", "shell_cmd": "\"%APPDATA%/_py_venvs/Py_Win32WindowMonitor__Python310/venv/Scripts/python.exe\" -u \"$file\"", "working_dir": "${folder:${project_path:${file_path}}}", "file_regex": "^[ ]*File \"(...*?)\", line ([0-9]*)", "selector": "source.python"}]}