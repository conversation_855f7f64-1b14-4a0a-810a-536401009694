# Import modules
import sys
import os
import subprocess
import json
import win32gui
import win32com.client as win32
import time

import win32api
import win32con

# NOTE: THIS ONLY WORK FOR EXPLORER-WINDOWS
# Save/Load Explorer Windows (Paths/Locations/Sizes)

# Constant: Specify path to the layout-file
layoutFile_FilePath = "WindowLayout.json"

# Constant: Specify name of the keys to use in the layout-file (for readability purposes)
layoutFile_Keys = ['Hwnd_Handle', 'Hwnd_Title', 'Hwnd_OpenPath', 'Hwnd_Minimized', 'Hwnd_Pos', 'Hwnd_Size']

# If ApplyLayout: Specify whether or not to close windows that are not specified in layoutFile
closeUnspecifiedWindows = True

# If ApplyLayout: Specify whether or not to create specified windows if they are not already open
createWindowsThatDontExist = True


# 1. Action: RetrieveCurrentWindowLayout (Retrieve all explorer windows currently open)
# 2. Action: LoadSpecifiedWindowLayout (Open/Position/Resize explorer windows passed in)
#    Option: CloseUnspecifiedWindows (Close all open windows that are not path of the input-layout)

# FUNCTION: RETRIEVE AND RETURN THE LAYOUT-DATA FOR (CURRENTLY OPEN) FILE-EXPLORER WINDOWS
def fnExplorerWindows_GetCurrentLayout():
    # Create a list to store the layout data of each window in: ['Handle', 'Title', 'Path', (Minimized), [Position], [Size]]
    fileExplorerWindows_HwndData = []

    # Specify the CLSID (Class Identifier) for COM-Object: ShellWindowsPermalink
    shellWindows_CLSID = '{9BA05972-F6A8-11CF-A442-00A0C90A8F39}'
    # For each (File Explorer Window) currently open (instances of COM-objects with specified CLSID)
    for currInstance in win32.Dispatch(shellWindows_CLSID):
        # currInstance: Retrieve data from the current window: ('Handle' | 'Title' | 'Path' | (Minimized))
        currWin_Handle = currInstance.HWND
        currWin_Title  = win32gui.GetWindowText(currWin_Handle)
        currWin_Path   = (currInstance.LocationURL)
        currWin_Minimized = win32gui.IsIconic(currWin_Handle)

        # currWin_Path: Manual handling of windows that doesn't return a path
        currWin_Path = ('file:///' if (currWin_Title == 'This PC') else currWin_Path)
        currWin_Path = ('file:///%s/Desktop' % os.environ['USERPROFILE'] if (currWin_Title == 'Desktop') else currWin_Path)
        currWin_Path = ('shell:RecycleBinFolder' if (currWin_Title == 'Recycle Bin') else currWin_Path)
        currWin_Path = ('control' if (currWin_Title == 'All Control Panel Items') else currWin_Path)
        # currWin_Path: Update path and prefix with explorer-command (so that the path can be executed as a command)
        currWin_Path = currWin_Path.replace('\\','/')
        currWin_Path = (('explorer %s' % currWin_Path) if ('file:///' in currWin_Path) else currWin_Path)

        # currInstance: Restore the current window if minimized (necessary to retrieve its position successfully)
        win32gui.ShowWindow(currWin_Handle, win32con.SW_SHOWNORMAL if currWin_Minimized else win32con.SW_SHOWNOACTIVATE)
        # currInstance: Retrieve data from the current window: (Position:[X,Y] | Size:[X,Y])
        currWinPos = [currInstance.Left, currInstance.Top]
        currWinSize = [currInstance.Width, currInstance.Height]
        # currInstance: Revert the current window back to its original minimized/un-minimized state
        win32gui.ShowWindow(currWin_Handle, win32con.SW_MINIMIZE) if currWin_Minimized else None

        # Append the current window data to the result
        fileExplorerWindows_HwndData.append([currWin_Handle, currWin_Title, currWin_Path, currWin_Minimized, currWinPos, currWinSize])

    # Sort the layout-data by path and copy the data into a dictionary
    fileExplorerWindows_HwndData_List = sorted(fileExplorerWindows_HwndData, key=lambda currItem: currItem[2])
    fileExplorerWindows_HwndData_Dict = [{currKey:itemValue for currKey,itemValue in zip(layoutFile_Keys, currItem)} for currItem in fileExplorerWindows_HwndData_List]
    # Return the result [Result:(List), Result:(Dictionary)]
    return [fileExplorerWindows_HwndData_List, fileExplorerWindows_HwndData_Dict]


# FUNCTION: LOAD AND APPLY EXPLORER-WINDOWS LAYOUT FROM INPUTDATA
def fnExplorerWindows_SetNewLayout():
    # Retrieve data from the layoutFile (using the JSON module)
    with open(layoutFile_FilePath, 'r') as inputFile:
        importedLayout_Dict = [{currKey:currItem[currKey] for currKey in layoutFile_Keys} for currItem in json.load(inputFile)]
        importedLayout_List = [[currItem[currKey] for currKey in layoutFile_Keys] for currItem in importedLayout_Dict]

    # Retrieve the current layout (as a list)
    currentLayout_List = (fnExplorerWindows_GetCurrentLayout())[0]

    # Initialize a variables for splitting the layout-data into separate variables (by whether they already exist or not)
    layoutData_Exists = []
    layoutData_Create = []
    # Generate comparison strings (used to prevent closing current explorer windows that are specified in the layout file)
    currentLayout_compareStrs = [str(currItem[1:3]) for currItem in currentLayout_List]
    importedLayout_compareStrs = [str(currItem[1:3]) for currItem in importedLayout_List]
    # Iterate through the items in the imported layout file
    for currIndex, importItem in enumerate(importedLayout_List):
        # If a currently open window matches the current importItem (by title and path)
        if importedLayout_compareStrs[currIndex] in currentLayout_compareStrs:
            # Append the current item to the list of existing windows (using the window-handle from currentLayout_List)
            currWin_Handle = currentLayout_List[currentLayout_compareStrs.index(importedLayout_compareStrs[currIndex])][0]
            layoutData_Exists.append([currWin_Handle] + importItem[1:])
        # Else (if none of the currently open windows matches the current importItem)
        else:
            # Append the current item to the list of windows to create (using False as its window-handle)
            layoutData_Create.append([False] + importItem[1:])

    # Close all windows that are not part of the loaded layout-file (if closeUnspecifiedWindows is True)
    winHandles_Close = [currItem for currItem in currentLayout_List if str(currItem[1:3]) not in importedLayout_compareStrs]
    # [win32api.SendMessage(currWin[0], win32con.WM_CLOSE, 0, 0) for currWin in (winHandles_Close if closeUnspecifiedWindows else [])]

    # sorted_items = sorted(layoutData_Apply, key=lambda currItem: (currItem[0], currItem[1]))
    # Apply the layout
    # for currItem in winHandles_Load:

    print("exists:")
    print (len(layoutData_Exists))
    for x in layoutData_Exists:
        print(x)
    print("\n\n")

    print("create:")
    print (len(layoutData_Create))
    for x in layoutData_Create:
        print(x)
    print("\n\n")

    print("delete:")
    print (len(winHandles_Close))
    for x in winHandles_Close:
        print(x)
    print("\n\n")


    # for x in currentLayout_List:
        # print(x)
    # Split the current layout into windows to keep, windows to close and windows to create
    # winHandles_Keep = [currItem for currItem in currentLayout_List if currItem[1:3] in [importItem[1:3] for importItem in importedLayout_List]]
    # # for x in winHandles_Keep: print(x)

    # print (len(importedLayout_List))
    # for x in importedLayout_List:
    #     print(x)
    # print("\n\n")

    # # C
    # currentLayout_Keep = []
    # for currItem in currentLayout_List:
    #     for importItem in importedLayout_List:
    #         if (currItem[1:3] == importItem[1:3]):
    #             currentLayout_Keep.append(currItem[:1] + importItem[1:])
    #             break

    # print (len(currentLayout_Keep))
    # for x in currentLayout_Keep:
    #     print(x)

    # result = [([item[0]] + subitem[1:]) for item in currentLayout_List for subitem in importedLayout_List if item[1] == subitem[1] and item[2] == subitem[2]]
    # # result = [[item[0], item[2], subitem[3]] for item in currentLayout_List for subitem in importedLayout_List if item[1] == subitem[1] and item[2] == subitem[2]]
    # print (len(result))
    # for x in result:
    #     print(x)
    # print("\n")
    # for x in importedLayout_List: print(x)
    # winHandles_Keep = [(currItem[:1] + importItem[1:]) for currItem, importItem in zip(currentLayout_List, importedLayout_List) if currItem[1:3] == importItem[1:3]]
    # for x in winHandles_Keep: print(x)
    # result = [(item1[:1] + item2[1:]) for item1, item2 in zip(currentLayout_List, importedLayout_List) if item1[1:3] == item2[1:3]]
    # print (len(result))
    # for x in result:
    #     print(x)

    #
    # result = [(item1[:1] + item2[1:]) for item1 in currentLayout_List for item2 in importedLayout_List if item1[1:3] == item2[1:3]]
    # print (len(result))
    # for x in result:
    #     print(x)

    # result = [item for item in currentLayout_List for b_item in importedLayout_List if item[1:3] == b_item[1:3]]
    # result = [item[:1] + b_item[2:] for item in result for b_item in importedLayout_List if item[1:3] == b_item[1:3]]
    # print (len(result))
    # for x in result:
    #     print(x)

    # result = [item for item in currentLayout_List for b_item in importedLayout_List if item[1:3] == b_item[1:3]]
    # result = [item[:1] + b_item[2:] for item in result for b_item in importedLayout_List if item[1:3] == b_item[1:3] and item not in result and b_item not in result]
    # print (len(result))
    # for x in result:
    #     print(x)

    # TROR DENNE FUNKER 2
    # result = [item[:1] + importedLayout_List[j][2:] for item in (currentLayout_List) for j, b_item in enumerate(importedLayout_List) if item[1:3] == b_item[1:3]]
    # print (len(result))
    # for x in result:
    #     print(x)


    # TROR DENNE FUNKER
    # result = [(item1[:1] + item2[1:]) for item1 in importedLayout_List[:len(currentLayout_List)] for item2 in currentLayout_List if item1[1:3] == item2[1:3]]
    # print (len(result))
    # for x in result:
    #     print(x)

    # result = [(item1[:1] + item2[1:]) for item1 in importedLayout_List[:-1] for item2 in currentLayout_List if item1[1:3] == item2[1:3]]
    # print (len(result))
    # for x in result:
    #     print(x)

    # result = [(item1[:1] + item2[1:]) for item1 in importedLayout_List for item2 in currentLayout_List if item1[1:3] == item2[1:3]]
    # print (len(result))
    # for x in result:
    #     print(x)




    # winHandles_Create = [importItem for importItem in importedLayout_List if importItem[1:2] in [currItem[1:2] for currItem in currentLayout_List]]

    # # existing current windows (currentLayout_List: indexes) to keep
    # currentLayoutKeep_Indexes = [currIndex for (currIndex,currItem) in enumerate(currentLayout_List) if currItem[1:3] in [importItem[1:3] for importItem in importedLayout_List]]
    # print (str(currentLayoutKeep_Indexes))
    # currentLayoutKeep_Indexes = [currentLayout_List.index(currItem) for currItem in currentLayout_List if currItem[1:3] in [importItem[1:3] for importItem in importedLayout_List]]
    # print (str(currentLayoutKeep_Indexes))
    # # existing imported windows (importedLayout_List: indexes) to keep
    # importedLayoutKeep_Indexes = [currIndex for (currIndex,importItem) in enumerate(importedLayout_List) if importItem[1:3] in [currItem[1:3] for currItem in currentLayout_List]]
    # print (str(importedLayoutKeep_Indexes))
    # importedLayoutKeep_Indexes = [importedLayout_List.index(importItem) for importItem in importedLayout_List if importItem[1:3] in [currItem[1:3] for currItem in currentLayout_List]]
    # print (str(importedLayoutKeep_Indexes))



    # Open new windows for all items in layout-file that is not already open


    # for x in currentLayout_List:
        # print(x)
    # currentLayout_Dict = currentLayout[1]

fnExplorerWindows_SetNewLayout()
    # # Split the current layout into windows to close and windows to keep
    # winHandles_Close = []
    # winHandles_Keep = []
    # # Retrieve currently open windows
    # fnExplorerWindows_GetCurrentLayout()
    # # Kan hente ut hwndHandle for alle vinduer som eksisterer i min liste

    # # Specify the CLSID (Class Identifier) for COM-Object: ShellWindowsPermalink
    # ShellWindows_CLSID = '{9BA05972-F6A8-11CF-A442-00A0C90A8F39}'
    # # For each (File Explorer Window) currently open (instances of COM-objects with specified CLSID)
    # for currInstance in win32.Dispatch(ShellWindows_CLSID):

    #     # jsonData_List = [jsonData_Dict[currItemHandle] for currItemHandle in layoutFile_Keys]
    #     print(jsonData_Dict)
    #     print(len(jsonData_Dict))
    #     print("\n")
    #     print(jsonData_List)
        # print(len(jsonData_List))
# dictionary = {'Hwnd_Handle': 789832, 'Hwnd_Title': 'This PC', 'Hwnd_OpenPath': 'explorer file:///', 'Hwnd_Minimized': 0, 'Hwnd_Pos': [301, 263], 'Hwnd_Size': [1259, 745]}

# # Extract the values in the desired order using a list comprehension
# result = [jsonData_Dict[currItemHandle] for currItemHandle in layoutFile_Keys]

# print(result)  # Output: [789832, 'This PC', 'explorer file:///', 0, [301, 263], [1259, 745]]


#         # If inputPaths contains data and closeUndefined, close the current window
#         if len(inputPaths) and closeUndefined: win32api.SendMessage(currWinHwnd, win32con.WM_CLOSE, 0, 0)
#         # If the current window path is passed in and it isn't already closed, close the current window
#         if (currWinPath in inputPaths) and not closeUndefined: win32api.SendMessage(currWinHwnd, win32con.WM_CLOSE, 0, 0)
#         # If no data was passed in and the current window is valid, add its [Handle, Title, Path, Position, Size] to the result
#         if not (len(inputPaths)) and (currWinPath != ''): fileExplorerWindows_HwndData.append(currWinData)

# for key in ['Hwnd_Handle', 'Hwnd_Title', 'Hwnd_OpenPath', 'Hwnd_Minimized', 'Hwnd_Pos', 'Hwnd_Size']:
#     # Append the value corresponding to the current key to the result list
#     result.append(dictionary[key])

# What is the best way of converting the following dictionary: ({'Hwnd_Handle': 789832, 'Hwnd_Title': 'This PC', 'Hwnd_OpenPath': 'explorer file:///', 'Hwnd_Minimized': 0, 'Hwnd_Pos': [301, 263], 'Hwnd_Size': [1259, 745]}) into a list like this: ([789832, 'This PC', 'explorer file:///', 0, [301, 263], [1259, 745]])?

    # # Create and write data to the layout-file (using the JSON module)
    # with open(outputLayout_FilePath, 'w') as outputFile:
    #     json.dump(fileExplorerWindows_HwndData_Dict, outputFile, indent=4, sort_keys=False, separators=(',', ': '))

# If a layout-file already exists
# if (os.path.exists(layoutFile_FilePath)):

#     # Close windows
#     windowPaths = [currItem['Path'] for currItem in jsonData_Dict]
#     fnExplorerWindows_Actions(inputPaths=windowPaths, closeUndefined=closeUnspecifiedWindows)
#     # For each item in the json file (the layout-file)
#     for currIndex, currItem in enumerate(jsonData_List):
#         print(currItem[2])
#         os.system(currItem[2])

    # print("\n")
    # print(jsonData_List)


# Retrieve window information: ['Handle', 'Title', 'Path', Pos:[X,Y], Size:[X,Y]]
# fileExplorerWindows_HwndData = fnExplorerWindows_GetCurrentLayout(layoutFile_FilePath)


# fnExplorerWindows_SetNewLayout(layoutFile_FilePath)


# --------------------------------------------------------------------------------------------------------------------
# IF NO ARGUMENT WAS PASSED IN: CREATE THE CONTEXT MENU ITEMS (WINDOWS FILE RICK-CLICK MENU)
# --------------------------------------------------------------------------------------------------------------------
# If no argument was passed in
# if len(sys.argv) == 1:
# print(sys.argv[1] == "CloseAllOthers")
# win32gui.DestroyWindow(hwnd)

# ------------------------------------------------------------------------------------------------------------------------------------


# ------------------------------------------------------------------------------------------------------------------------------------
# Using Python 3, when dumping data to a json file the resulting order in the json file doesn't match the dictionary I passed in. From the code below, could you modify it so that the elements is written to the json file in the correct order (Handle, Title, Path, Pos, Size)?
# jsonData_List = []
# jsonData_List.append({'Handle': 789832, 'Title': 'This PC', 'Path': 'explorer file:///', 'Pos': [301, 263], 'Size': [1259, 745]})
# with open("datadump.json", 'w') as jsonfile:
#     json.dump(jsonData_List, jsonfile, indent=4, sort_keys=True, separators=(',', ': '))

# [
#     {
#         "Handle": 789832,
#         "Path": "explorer file:///",
#         "Pos": [
#             301,
#             263
#         ],
#         "Size": [
#             1259,
#             745
#         ],
#         "Title": "This PC"
#     }
# ]
# ------------------------------------------------------------------------------------------------------------------------------------




#     for currItem in jsonData:
# 'Handle','Title','Path','Pos','Size'
#     # Print the data
#     print(data)
#     print(data[0]['Handle'])  # prints "value1"
    # print(data['key2'])  # prints "value2"
    # print(data['key3'])  # prints [1, 2, 3]

    # json.dump(data, jsonfile, indent=4)

    # # os.system("file:\\")
    # time.sleep(10)
    # # For each item retrieved
    # for currIndex in range(0, len(fileExplorerWindows_HwndData["Handle"])):
    #     # Store the current item in separate variables (for readability purposes)
    #     currWinHwnd = fileExplorerWindows_HwndData["Handle"][currIndex]
    #     currWinTitle = fileExplorerWindows_HwndData["Title"][currIndex]
    #     currWinPath = fileExplorerWindows_HwndData["Path"][currIndex]
    #     currWinPos = fileExplorerWindows_HwndData["Pos"][currIndex]
    #     currWinSize = fileExplorerWindows_HwndData["Size"][currIndex]

    #     # Open a new instance of the current window (if it's not already open)
    #     alreadyOpen = win32gui.FindWindow('CabinetWClass', currWinTitle)
    #     if not alreadyOpen: os.system("explorer %s" % currWinPath)
    #     # explorerOpened = win32gui.FindWindow('CabinetWClass', currWinTitle)
    #     # hWnd = win32gui.FindWindow(class_name, caption)
    #     print(alreadyOpen)

        # win32gui.MoveWindow(currWinHwnd, currWinPos[0], currWinPos[1], currWinSize[0], currWinSize[1], True)

        # print('WinHandle: %s' % currWinHwnd)
        # print('WinTitle:  %s' % currWinTitle)
        # print('WinPath:   %s' % currWinPath)
        # print('WinPos:    %s' % currWinPos)
        # print('WinSize:   %s' % currWinSize)
        # print(fileExplorerWindows_HwndData["Handle"][currIndex])
        # # (0:HwndHandle | 1:FromLeftEdge | 2:FromTop | 3:Width | 4:Height | 5:Refresh)
        # currWinSize = win32gui.GetWindowRect(currWinHwnd)
        # currWinY = currInstance.Top
        # currWinWidth = currWin.Width
        # currWinHeight = currWin.Height
        # currWinPos = [currWin.Left, currWin.Top, currWin.Width, currWin.Height]
        # # print(currWin.Width)
        # # print(currWin.Left)
        # # print(currWin.Top)
        # # currWinPath = currWin.LocationURL.replace('file:///','')
        # if currWinTitle == "SaveWindowLayoutx":
        #     print(currWinTitle)
        #     print(currWinPath)
        #     print(win32gui.GetWindowRect(currWinHwnd))
        #     print(currWinPos)
        #     # print([currWinX,currWinY,currWinWidth,currWinHeight])
        #     # print(currWin)
        #     # print(currWin)
        #     # print(currWin)
        #     time.sleep(3)
        #     win32gui.MoveWindow(currWinHwnd, currWinPos[0], currWinPos[1], currWinSize[0], currWinSize[1], True)
        #     # win32gui.MoveWindow(currWinHwnd, currWinSize[0], currWinSize[1], currWinSize[2], currWinSize[3], True)
        # # print(currWinPath)
        # # print(win32gui.GetWindowText(currWinHwnd))
        # print('\n')

        # print(currWin.Height)
        # print(currWin.Width)
        # print(currWin.Left)
        # print(currWin.Top)

        # explorerHwndData.append([currWinHwnd, currWinPath])
    # Return the result
    # return fileExplorerWindows_HwndData

# for x in dir(win32):
    # print(x)
# win32gui.MoveWindow(win32gui.GetForegroundWindow(), 160, 0, 1600, 720, True)
# file:\\ (åpner "this pc")

# for currIndex, currItem in enumerate(openExplorerWindows):
#     # print(currIndex)
#     currWinHwnd = openExplorerWindows[currIndex][0]
#     currWinPath = openExplorerWindows[currIndex][1]
#     currWinTitle = win32gui.GetWindowText(currWinHwnd)
#     currWinSize = win32gui.GetWindowRect(currWinHwnd)
    # print(currWinHwnd)
    # print(currWinPath)
    # print(currWinTitle)
    # print(currWinSize)
    # print('\n')
