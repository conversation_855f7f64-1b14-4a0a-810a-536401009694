# Import modules
import os
import win32gui
import win32com.client as win32

import time
# import win32process
# import psutil
# import uuid

# https://www.tenforums.com/tutorials/3123-clsid-key-guid-shortcuts-list-windows-10-a.html
# CLSID Key / GUID: This PC
# explorer /e,::{20D04FE0-3AEA-1069-A2D8-08002B30309D}
# CLSID Key / GUID: Control Panel
# explorer /e,::{5399E694-6CE5-4D6C-8FCE-1D8870FDCBA0}



# existing: file:///C:/Users/<USER>/Desktop
# open new window: explorer file:///C:/Users/<USER>/Desktop

# FUNCTION: RETRIEVE AND RETURN FILE-EXPLORER WINDOWS (WINDOW-HANDLES AND THEIR FOLDER-PATHS)
def fnRetrieveOpenExplorerWindows():
    # Create a variable for the result: (Handle | Title | Path | Pos[X,Y] | Size[X,Y])
    fileExplorerWindows_HwndData = {"Handle":[], "Title":[], "Path":[], "Pos":[], "Size":[]}
    # a = win32gui.GetForegroundWindow()
    # print("Foreground: %s" % a)




    # Specify COM-Object CLSID (ShellWindowsPermalink)
    ShellWindows_CLSID = '{9BA05972-F6A8-11CF-A442-00A0C90A8F39}'
    # For each File Explorer Window currently open
    for currWin in win32.Dispatch(ShellWindows_CLSID):
        print(win32gui.GetFocus())
        # Retrieve data from the current window
        currWinHwnd = currWin.HWND
        currWinTitle  = win32gui.GetWindowText(currWinHwnd)
        currWinPath   = currWin.LocationURL
        currWinPos    = [currWin.Left, currWin.Top]
        currWinSize   = [currWin.Width, currWin.Height]
        #
        currWinPath = ('file:///' if (currWinTitle == 'This PC') else currWinPath)

        # Debug
        # print('WinHandle: %s' % currWinHwnd)
        # print('WinTitle:  %s' % currWinTitle)
        # print('WinPath:   %s' % currWinPath)
        # print('WinPos:    %s' % currWinPos)
        # print('WinSize:   %s' % currWinSize)

        # print(win32gui.GetClassName(currWinHwnd))

        #
        if (currWinPath != ''):
            fileExplorerWindows_HwndData["Handle"].append(currWinHwnd)
            fileExplorerWindows_HwndData["Title"].append(currWinTitle)
            fileExplorerWindows_HwndData["Path"].append(currWinPath)
            fileExplorerWindows_HwndData["Pos"].append(currWinPos)
            fileExplorerWindows_HwndData["Size"].append(currWinSize)

            print('WinHandle: %s' % currWinHwnd)
            print('WinTitle:  %s' % currWinTitle)
            print('WinPath:   %s' % currWinPath)
            print('WinPos:    %s' % currWinPos)
            print('WinSize:   %s' % currWinSize)

    print('\n\n')
    time.sleep(7)
    for x in range(0, len(fileExplorerWindows_HwndData["Handle"])):
        print('WinHandle: %s' % currWinHwnd)
        print('WinTitle:  %s' % currWinTitle)
        print('WinPath:   %s' % currWinPath)
        print('WinPos:    %s' % currWinPos)
        print('WinSize:   %s' % currWinSize)
        currWinHwnd = fileExplorerWindows_HwndData["Handle"][x]
        currWinTitle = fileExplorerWindows_HwndData["Title"][x]
        currWinPath = fileExplorerWindows_HwndData["Path"][x]
        currWinPos = fileExplorerWindows_HwndData["Pos"][x]
        currWinSize = fileExplorerWindows_HwndData["Size"][x]
        # print(fileExplorerWindows_HwndData["Handle"][x])
        win32gui.MoveWindow(currWinHwnd, currWinPos[0], currWinPos[1], currWinSize[0], currWinSize[1], True)

        # # (0:HwndHandle | 1:FromLeftEdge | 2:FromTop | 3:Width | 4:Height | 5:Refresh)
        # currWinSize = win32gui.GetWindowRect(currWinHwnd)
        # currWinY = currWin.Top
        # currWinWidth = currWin.Width
        # currWinHeight = currWin.Height
        # currWinPos = [currWin.Left, currWin.Top, currWin.Width, currWin.Height]
        # # print(currWin.Width)
        # # print(currWin.Left)
        # # print(currWin.Top)
        # # currWinPath = currWin.LocationURL.replace('file:///','')
        # if currWinTitle == "SaveWindowLayoutx":
        #     print(currWinTitle)
        #     print(currWinPath)
        #     print(win32gui.GetWindowRect(currWinHwnd))
        #     print(currWinPos)
        #     # print([currWinX,currWinY,currWinWidth,currWinHeight])
        #     # print(currWin)
        #     # print(currWin)
        #     # print(currWin)
        #     time.sleep(3)
        #     win32gui.MoveWindow(currWinHwnd, currWinPos[0], currWinPos[1], currWinSize[0], currWinSize[1], True)
        #     # win32gui.MoveWindow(currWinHwnd, currWinSize[0], currWinSize[1], currWinSize[2], currWinSize[3], True)
        # # print(currWinPath)
        # # print(win32gui.GetWindowText(currWinHwnd))
        # print('\n')

        # print(currWin.Height)
        # print(currWin.Width)
        # print(currWin.Left)
        # print(currWin.Top)

        # explorerHwndData.append([currWinHwnd, currWinPath])
    # Return the result
    return fileExplorerWindows_HwndData

# for x in dir(win32):
    # print(x)
# win32gui.MoveWindow(win32gui.GetForegroundWindow(), 160, 0, 1600, 720, True)
# file:\\ (åpner "this pc")
openExplorerWindows = fnRetrieveOpenExplorerWindows()

# for currIndex, currItem in enumerate(openExplorerWindows):
#     # print(currIndex)
#     currWinHwnd = openExplorerWindows[currIndex][0]
#     currWinPath = openExplorerWindows[currIndex][1]
#     currWinTitle = win32gui.GetWindowText(currWinHwnd)
#     currWinSize = win32gui.GetWindowRect(currWinHwnd)
    # print(currWinHwnd)
    # print(currWinPath)
    # print(currWinTitle)
    # print(currWinSize)
    # print('\n')
