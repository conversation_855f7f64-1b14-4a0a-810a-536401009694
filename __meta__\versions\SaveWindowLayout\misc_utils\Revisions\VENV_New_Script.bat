@ECHO OFF

:: --------------------------------------------------------------------------
:: SET CURRENT DIRECTORY TO THE LOCATION OF THIS BATCH FILE
:: --------------------------------------------------------------------------
CD /D %~dp0
SET CURRENT_SCRIPT_PATH=%CD%

:: --------------------------------------------------------------------------
:: STATIC DEFINITIONS
:: --------------------------------------------------------------------------
:: Static definitions related to VENV-Template-Utils:
SET "TEMPLATE_VENV_PATH=D:\Programmering\Development_Python_Projects\Virtual_Environment_Projects\VENV_Template_Python3"
SET "TEMPLATE_UTIL_PATH=%TEMPLATE_VENV_PATH%\VENV_Utilities_Python"
SET "TEMPLATE_SCRIPT_PATH=%TEMPLATE_UTIL_PATH%\Create_New_Script.py"
SET "TEMPLATE_PYTHON_PATH=%TEMPLATE_VENV_PATH%\venv\Scripts\python.exe"

:: --------------------------------------------------------------------------
:: CODE BELOW IS EXECUTED FROM THE VENV ROOT DIRECTORY
:: --------------------------------------------------------------------------
:: Execute utility used to create new python-project.
"%TEMPLATE_PYTHON_PATH%" "%TEMPLATE_SCRIPT_PATH%" "%CURRENT_SCRIPT_PATH%"

:: EXIT
TITLE "Finished"
PAUSE > NUL
EXIT
