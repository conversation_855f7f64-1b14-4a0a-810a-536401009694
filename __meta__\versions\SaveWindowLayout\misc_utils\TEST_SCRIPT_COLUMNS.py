# I'm using Python 3 and the pywin32 module. I have created a script that successfully retrieve the files of a (file explorer) folder, it also successfully sets the view mode to Details and the sorting to sort by Name.

# I'm trying to also retrieve information about which columns are visible in the detail view, but I haven't been able to find a way to do this.

# When reading the pywin32-documentation there's an object called 'PyIColumnProvider Object' which has a method called 'GetColumnInfo', I think this is what I need to initialize.
# However I don't know how to initalize this from the folder I've already retrieved. When I check the documentation for 'PyIColumnProvider.Initialize' it says that the parameters is 'psci : PyCSHCOLUMNINIT', but I don't know what that means.

# Are you able to modify my current code to also retrieve column information? Here's the current code:
# Import modules
import win32com.client
from win32com.shell import shell
import win32com.shell.shellcon as shellcon
# import pythoncom
import time
# # Dispatch the 'Shell.Application' object
# shell_windows_obj = win32com.client.Dispatch('Shell.Application')
# # Retrieve all currently open File-Explorer Windows
# shell_windows = [window for window in shell_windows_obj.Windows()]


# # For each shell window
# for window in shell_windows:
#     # If the window-title is 'misc_utils'
#     if window.LocationName == 'misc_utils':
#         # Retrieve the basic window data
#         window_hwnd = window.HWND
#         window_title = window.LocationName
#         window_path = window.LocationURL

#         # Retrieve all of the files in folder
#         files_in_folder_objs = window.Document.Folder.Items()
#         filenames = [current.Name for current in files_in_folder_objs]
#         filepaths = [current.Path for current in files_in_folder_objs]

#         # Set the folder view mode to "Details"
#         window.Document.CurrentViewMode = 4
#         # Set the sort column to sort by "Name"
#         window.Document.SortColumns = "prop:System.ItemNameDisplay;"

#         # Retrieve the columns and their width
#         # ** TODO: HOW CAN I RETRIEVE COLUMN INFORMATION??? ***

#         # Get the IShellFolder interface for the folder ('PyIShellFolder')
#         folder = shell.SHGetDesktopFolder()
#         # SHGDN = SHGetNameFromIDList
#         folder_pidl = shell.SHParseDisplayName(window.LocationURL, shellcon.SHGDN_FORPARSING)[0]
#         folder.BindToObject(folder_pidl, None, shell.IID_IShellFolder)


#         print(folder.GetDisplayNameOf(folder_pidl, shellcon.SHGDN_NORMAL))

#         folder.CreateViewObject(window_hwnd, shell.IID_IColumnProvider)
#         print(dir(folder))
#         # # Retrieve the 'Shell.Explorer' object
#         # explorer = window.Document.Application
#         # # Retrieve the 'PyIColumnProvider' object
#         # columnProvider = explorer.ColumnProvider
#         # # Retrieve the column information
#         # columnInfo = columnProvider.GetColumnInfo()
#         # # Do something with the columnInfo
#         # print(columnInfo)
import os
import pythoncom
from win32com.shell import shell, shellcon
# Import modules
import time
import os
import random
import argparse
import signal
# Import pywin32 modules
import win32gui
import win32api
import win32com.client
# Import ppretty (for displaying the structure of objects/classes)
from ppretty import ppretty

import os
import pythoncom
from win32com.shell import shell, shellcon


# details.str.uType contains the type of the column
# details.str.pOleStr contains the name of the column
# print(details.str.pOleStr)


# columnProvider = shell.SHCreateShellFolderView(shell.IID_IColumnProvider, shellcon.FVM_DETAILS)
# print(columnProvider)
# Retrieve the 'IID_IShellFolder' interface of the folder
# folder = shell.SHGetDesktopFolder()
# Create an instance of the 'IID_IColumnProvider' interface
# columnProvider = folder.BindToHandler(None, shell.BHID_ColumnProvider, shell.IID_IColumnProvider)

# Dispatch the 'Shell.Application' object
shell_windows_obj = win32com.client.Dispatch('Shell.Application')
# Retrieve all currently open File-Explorer Windows
shell_windows = [window for window in shell_windows_obj.Windows()]

# For each shell window
for window in shell_windows:
    # If the window-title is 'misc_utils'
    if window.LocationName == 'misc_utils':
        # Retrieve the basic window data
        window_hwnd = window.HWND
        window_title = window.LocationName
        window_path = window.LocationURL

        # bbbb = (window.Document.Folder.GetDetailsOf(None, 1))
        # print(dir(window.Document.Folder.Self))
        # print(bbbb)
        # print(dir(bbbb))
        # print(dir(window.Document.Folder.GetDetailsOf))
        # print(window_path)
        # print(window._oleobj_.GetTypeInfo().GetTypeAttr())
        # print(dir(pythoncom))
        # print(dir(window))
        folder = shell.SHGetDesktopFolder()
        folder_pidl, eaten = shell.SHParseDisplayName(window_path, shellcon.SHGDN_FORPARSING)
        view = folder.CreateViewObject(window_hwnd, shell.IID_IShellView)
        bbb = folder.BindToObject(folder_pidl, None, shell.IID_IShellFolder)
        # bbb.GetUIObjectOf(window.HWND, folder_pidl, folder_pidl)
        # GetUIObjectOf(self, hwndOwner, pidls, iid, inout)
        # DesktopFolder.GetUIObjectOf(Handle, 1, PathPIDL, IID_IContextMenu, nil, Result);
        # print(shell.SHGetDesktopFolder(SHGetViewStatePropertyBag))
        # print(dir(folder._builtMethods_))
        print(bbb)
        print(dir(bbb))
        # # ab = (window._oleobj_.GetTypeInfo())
        # # print(ab)
        # print(type(folder))
        # print(dir(folder))
        # print((type(view)))
        # print(dir(view))
        # Get the first column
        # aa = view.GetDefaultColumn()
        # col_id = folder.SHCOLUMNID()
        # print(col_id)
        # col_id.fmtid = shell.FMTID_Storage
        # col_id.pid = shell.PID_STG_NAME
        # details = folder.GetDetailsOf(None, 1)
        # Get the directory structure of the window object
        window_object_structure = ppretty(
            # Object to represent
            folder,
            # (type:str)  | Indentation level of the output.
            indent='  ',
            # (type:int)  | Maximum width of the output string.
            width=100,
            # (type:int)  | Maximum depth of introspecion.
            depth=5,
            # (type:int)  | Maximum length of sequences (lists, tuples, etc.).
            seq_length=500,
            # (type:bool) | Show or hide "protected" attributes/members (i.e. those that start with "_").
            show_protected=True,
            # (type:bool) | Show or hide "private" attributes/members (i.e. those that start with "__").
            show_private=False,
            # (type:bool) | Show or hide static attributes/members.
            show_static=True,
            # (type:bool) | Show or hide properties (i.e. those defined with the @property decorator).
            show_properties=True,
            # (type:bool) | Show or hide the memory address of the object.
            show_address=False,
            # (type:int)  | Maximum string length.
            str_length=50
        )

        # # Write the output to a text file
        # script_name = os.path.splitext(os.path.basename(__file__))[0]
        # output_name = (script_name + '_output_result.py')
        # with open(output_name, 'w') as file:
        #     file.write(window_object_structure)

        # Finished
        print('Finished')

        # # details.str.uType contains the type of the column
        # # details.str.pOleStr contains the name of the column
        # print(details.str.pOleStr)
        # # a = get_folder_columns(window_path)
        # # print(a)
        # print(type(folder))
        # print(type(view))
        # # folder_iface = folder.QueryInterface(win32com.client.constants.IID_IShellFolder)
        # # columnProvider = shell.SHCreateShellFolderView(shell.IID_IColumnProvider, shellcon.FVM_DETAILS)
        # # folder.BindToObject(folder_pidl, None, shell.IID_IShellView)
        # # print(dir(win32com.client.constants))
        # # print(shell.IID_IColumnProvider)
        # # print(dir(shell.IID_IColumnProvider))
        # # test = win32com.client.Dispatch(shell.IID_IColumnProvider)
        # # print(test)
        # # print(dir(test))
        # # print(shell.IID_IColumnProvider)
        # # folder_iface = folder.QueryInterface(pythoncom.IID_IShellFolder)
        # print(view)
        # print(dir(view))
        # bb = view.GetItemObject()
        # print(bb)

        # view = folder.CreateViewObject(folder, shell.IID_IColumnProvider)
        # print(type(folder))
        # print(dir(folder.Document))

time.sleep(999)
# # Create a ShellFolder object
# shell = win32com.client.Dispatch("Shell.Application")
# desktop = shell.Desktop
# folder = desktop.ParseName("C:\\Windows")
# folder_iface = folder.QueryInterface(win32com.client.constants.IID_IShellFolder)

# Attach a IID_IColumnProvider to the IID_IShellFolder or IID_IShellView
column_provider_iface = folder_iface.QueryInterface(win32com.client.constants.IID_IColumnProvider)

#Initialize the columns
column_provider_iface.Initialize(psci)

#Retrieve column information
column_info = column_provider_iface.GetColumnInfo()
    # Attach a IID_IShellView to the IID_IShellFolder
    # view_iface = folder_iface.BindToObject(None, None, win32com.client.constants.IID_IShellView)



        # attributes = folder.GetAttributesOf([folder_pidl], shellcon.SFGAO_FILESYSTEM)
        # print(dir(attributes))

        # # column_count = folder.GetAttributesOf(folder_pidl, 0)
        # print(attributes)
        # print(columnProvider)



time.sleep(9999)


# Attach a IID_IShellView to the IID_IShellFolder
view_iface = folder_iface.BindToObject(None, None, win32com.client.constants.IID_IShellView)
        # columnProvider = folder.BindToHandler(None, shell.IID_IColumnProvider, shell.IID_IColumnProvider)

  # shell.SHGetDesktopFolder()
  # shell.SHGetFolderLocation()
  # OleCheckUTF8(SHGetFolderLocation(Handle, CSIDL_BITBUCKET, 0, 0, PathPIDL));

        # folder = desktop.ParseDisplayName("C:\\Windows", shellcon.SHGDN_FORPARSING)
        # view = folder.CreateViewObject(window_hwnd, shell.IID_IShellView)
        # folder_iface = folder.QueryInterface(win32com.client.constants.IID_IShellFolder)


        # Set the folder view mode to "Details"
        # window.Document.CurrentViewMode = 4

        # # Get the IShellFolder interface for the folder ('PyIShellFolder')
        # folder = shell.SHGetDesktopFolder()
        # print(dir(folder))
        # view = folder.CreateViewObject(window_hwnd, shell.IID_IShellView)
        # print("ja")
        # print(dir(view))

        # ------------------------------------------------------------
        # SHGDN = SHGetNameFromIDList
        # folder_pidl = shell.SHParseDisplayName(window_path, shellcon.SHGDN_FORPARSING)[0]
        # folder.BindToObject(folder_pidl, None, shell.IID_IShellFolder)
        # ------------------------------------------------------------

        # # Set the sort column to sort by "Name"
        # window.Document.SortColumns = "prop:System.ItemNameDisplay;"

        # # Retrieve the 'IColumnProvider' object
        # columnProvider = window.Document.Folder.BindToHandler(None, shell.IID_IColumnProvider)
        # # Retrieve the column information
        # columnInfo = columnProvider.GetColumnInfo()
        # # Do something with the columnInfo
        # print(columnInfo)
