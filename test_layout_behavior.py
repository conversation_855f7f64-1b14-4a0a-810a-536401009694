#!/usr/bin/env python3
"""
Test script to demonstrate the new window distribution behavior.

This shows how windows are distributed across cells when there are more windows
than available grid cells.
"""

def demonstrate_window_distribution():
    """Show how windows are distributed across grid cells."""
    
    test_cases = [
        {"windows": 5, "rows": 1, "cols": 3, "description": "5 windows in 1x3 grid"},
        {"windows": 4, "rows": 1, "cols": 2, "description": "4 windows in 1x2 grid"},
        {"windows": 7, "rows": 2, "cols": 2, "description": "7 windows in 2x2 grid"},
        {"windows": 6, "rows": 1, "cols": 4, "description": "6 windows in 1x4 grid"},
    ]
    
    for case in test_cases:
        windows = case["windows"]
        rows = case["rows"]
        cols = case["cols"]
        description = case["description"]
        
        print(f"\n{description}:")
        print("=" * len(description))
        
        total_cells = rows * cols
        windows_per_cell = windows // total_cells
        extra_windows = windows % total_cells
        
        print(f"Total cells: {total_cells}")
        print(f"Base windows per cell: {windows_per_cell}")
        print(f"Extra windows to distribute: {extra_windows}")
        
        window_index = 0
        
        for cell_index in range(total_cells):
            if window_index >= windows:
                break
                
            row = cell_index // cols
            col = cell_index % cols
            
            windows_in_this_cell = windows_per_cell + (1 if cell_index < extra_windows else 0)
            
            cell_windows = []
            for _ in range(windows_in_this_cell):
                if window_index < windows:
                    cell_windows.append(f"W{window_index + 1}")
                    window_index += 1
            
            print(f"Cell ({row},{col}): {', '.join(cell_windows)} ({len(cell_windows)} windows)")
        
        print(f"Total windows placed: {window_index}")

def main():
    print("Window Distribution Demonstration")
    print("This shows how the new tiling behavior distributes windows across grid cells.")
    demonstrate_window_distribution()
    
    print("\n" + "="*60)
    print("BEHAVIOR EXPLANATION:")
    print("- All windows are distributed across available cells")
    print("- Extra windows are distributed to the first cells")
    print("- When multiple windows share a cell, they're arranged in a sub-grid")
    print("- Maximum 2 columns per cell for readability")

if __name__ == "__main__":
    main()
