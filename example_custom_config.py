#!/usr/bin/env python3
"""
Example: Custom Window Tiler Configuration

This example demonstrates how to customize the WindowTilerConfig to add new
process classifications and modify the behavior of the window tiler.
"""

# Import the necessary classes from the main script
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from window_tiler import Config, WindowTilerApp

def create_custom_config():
    config = Config()

    # Add more applications
    config.types.update({
        'brave.exe': 'Browser',
        'vivaldi.exe': 'Browser',
        'alacritty.exe': 'Terminal',
        'hyper.exe': 'Terminal',
        'brackets.exe': 'Editor',
        'webstorm64.exe': 'IDE',
        'vlc.exe': 'Media',
        'spotify.exe': 'Media',
        'discord.exe': 'Communication',
        'slack.exe': 'Communication'
    })

    # Skip problematic processes
    config.skip.update({'steam.exe', 'nvidia-share.exe'})

    return config

def main():
    """Run the window tiler with custom configuration."""
    print("Creating custom window tiler configuration...")

    # Create custom config
    custom_config = create_custom_config()

    # Show some info about the custom config
    print(f"Applications configured: {len(custom_config.types)}")
    print(f"Excluded processes: {len(custom_config.skip)}")

    # Create and run the app with custom config
    print("\nStarting Window Tiler with custom configuration...")
    app = WindowTilerApp(custom_config)
    app.run()

if __name__ == "__main__":
    main()
