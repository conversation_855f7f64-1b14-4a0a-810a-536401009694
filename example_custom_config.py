#!/usr/bin/env python3
"""
Example: Custom Window Tiler Configuration

This example demonstrates how to customize the WindowTilerConfig to add new
process classifications and modify the behavior of the window tiler.
"""

# Import the necessary classes from the main script
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from window_tiler import WindowTilerConfig, WindowTilerApp

def create_custom_config():
    config = WindowTilerConfig()

    # Add more browsers
    config.add_process_type('BROWSER', ['brave.exe', 'vivaldi.exe', 'waterfox.exe'])

    # Add more terminals
    config.add_process_type('TERMINAL', ['alacritty.exe', 'hyper.exe', 'tabby.exe'])

    # Add more editors
    config.add_process_type('EDITOR', ['brackets.exe', 'vim.exe'])

    # Add more IDEs
    config.add_process_type('IDE', ['webstorm64.exe', 'phpstorm64.exe', 'clion64.exe'])

    # Add custom window types
    config.process_types['MEDIA'] = ['vlc.exe', 'spotify.exe', 'foobar2000.exe', 'potplayer.exe']
    config.process_types['COMMUNICATION'] = ['discord.exe', 'slack.exe', 'teams.exe', 'zoom.exe']

    # Exclude problematic processes
    config.exclude_process('steam.exe')
    config.exclude_process('nvidia-share.exe')

    return config

def main():
    """Run the window tiler with custom configuration."""
    print("Creating custom window tiler configuration...")

    # Create custom config
    custom_config = create_custom_config()

    # Show some info about the custom config
    print(f"Window types configured: {list(custom_config.process_types.keys())}")
    print(f"Total excluded processes: {len(custom_config.excluded)}")

    # Create and run the app with custom config
    print("\nStarting Window Tiler with custom configuration...")
    app = WindowTilerApp(custom_config)
    app.run()

if __name__ == "__main__":
    main()
