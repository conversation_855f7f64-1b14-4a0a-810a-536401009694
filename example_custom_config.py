#!/usr/bin/env python3
"""
Example: Custom Window Tiler Configuration

This example demonstrates how to customize the WindowTilerConfig to add new
process classifications and modify the behavior of the window tiler.
"""

# Import the necessary classes from the main script
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from window_tiler import WindowTilerConfig, WindowTilerApp

def create_custom_config():
    """Create a custom configuration with additional process classifications."""
    
    # Create the base config
    config = WindowTilerConfig()
    
    # Add additional browser processes
    config.add_process_classification('BROWSER', [
        'brave.exe',
        'opera.exe', 
        'vivaldi.exe',
        'waterfox.exe'
    ])
    
    # Add additional terminal/console applications
    config.add_process_classification('TERMINAL', [
        'alacritty.exe',
        'hyper.exe',
        'terminus.exe',
        'tabby.exe'
    ])
    
    # Add additional code editors
    config.add_process_classification('EDITOR', [
        'notepad++.exe',
        'sublime_text.exe',
        'atom.exe',
        'brackets.exe'
    ])
    
    # Add additional IDEs
    config.add_process_classification('IDE', [
        'rider64.exe',
        'webstorm64.exe',
        'phpstorm64.exe',
        'clion64.exe',
        'goland64.exe'
    ])
    
    # Add a custom window type for media applications
    config.process_classifications['MEDIA'] = [
        'vlc.exe',
        'spotify.exe',
        'foobar2000.exe',
        'musicbee.exe',
        'potplayer.exe'
    ]
    
    # Add a custom window type for communication apps
    config.process_classifications['COMMUNICATION'] = [
        'discord.exe',
        'slack.exe',
        'teams.exe',
        'zoom.exe',
        'skype.exe',
        'telegram.exe'
    ]
    
    # Exclude some additional processes that shouldn't be tiled
    config.add_excluded_process('steam.exe')  # Steam overlay can be problematic
    config.add_excluded_process('nvidia-share.exe')  # NVIDIA overlay
    
    # You can also remove processes from exclusion if needed
    # config.remove_excluded_process('conhost.exe')
    
    return config

def main():
    """Run the window tiler with custom configuration."""
    print("Creating custom window tiler configuration...")
    
    # Create custom config
    custom_config = create_custom_config()
    
    # Show some info about the custom config
    print(f"Window types configured: {list(custom_config.process_classifications.keys())}")
    print(f"Total excluded processes: {len(custom_config.excluded_processes)}")
    
    # Create and run the app with custom config
    print("\nStarting Window Tiler with custom configuration...")
    app = WindowTilerApp(custom_config)
    app.run()

if __name__ == "__main__":
    main()
