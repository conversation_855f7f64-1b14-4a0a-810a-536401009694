# This script retrieves data for currently open ShellWindows (File Explorer windows) and applies a layout to these windows.
# The layout is specified in a JSON file at LAYOUT_FILE_PATH, and the data includes the window handle, title, path, z-index, window state, always-on-top status, position, and size.
# The keys for the retrieved window data are specified using the WINDOW_DATA_KEYS list, and the options for applying the layout are specified using the APPLY_LAYOUT_OPTIONS dictionary.
# The CLSID (Class Identifier) for the ShellWindows COM object is specified using the SHELL_WINDOWS_CLSID constant, and a dynamic list of COM objects is created through the ShellWindows class.



# Import necessary modules
import sys
import os
import time
import json
import subprocess
import urllib.parse
import win32api
import win32gui
import win32com.client
import win32con
import argparse
from tabulate import tabulate


# Constant: CLSID for ShellWindowsPermalink COM object (alternatively 'Shell.Application' could be used)
SHELL_WINDOWS_CLSID = '{9BA05972-F6A8-11CF-A442-00A0C90A8F39}'

# Constants: Initialize aliases to use as reference to the different parts of window data.
WINDOW_DATA_KEYS = [
    'hwnd',  # Window handle
    'title',  # Window title
    'path',  # Window path
    'z_index',  # Window stacking order
    'show_state',  # Window hidden/normal/minimized/maximized state
    'always_on_top',  # Window always-on-top status
    'position',  # Window position (x,y) from top-left corner of the screen
    'size',  # Window size (width, height)
]

# Constant: Used for mapping specified window titles to corresponding CLSID's
SHELL_WINDOWS_OVERRIDE = {
    'Desktop': 'shell:::{B4BFCC3A-DB2C-424C-B029-7FE99A87C641}',
    'This PC': 'shell:::{20D04FE0-3AEA-1069-A2D8-08002B30309D}',
    'Recycle Bin': 'shell:::{645FF040-5081-101B-9F08-00AA002F954E}',
    'All Control Panel Items': 'shell:::{5399E694-6CE5-4D6C-8FCE-1D8870FDCBA0}',
    'Network Connections': 'shell:::{7007ACC7-3202-11D1-AAD2-00805FC1270E}'
}



# RETRIEVE AND RETURN LAYOUT DATA FOR CURRENTLY OPEN FILE-EXPLORER WINDOWS
def get_current_layout_data():
    """
    Returns a list of dictionaries containing the layout data of each currently open file-explorer window
    Each dictionary contains the following keys:
    - 'handle': The window handle of the file-explorer window (int)
    - 'title': The title of the file-explorer window (str)
    - 'path': The path of the file-explorer window (str)
    - 'z_index': The z-index of the file-explorer window (GetWindow)
    - 'show_state': The window state of the file-explorer window (int)
    - 'always_on_top': Whether or not the file-explorer window is always on top (bool)
    - 'position': The position of the file-explorer window (x, y coordinates) (tuple of int)
    - 'size': The size of the file-explorer window (width, height) (tuple of int)
    """
    # Initialize a list to store data for all (currently open) file-explorer windows
    window_data_retrieved = []
    # Initialize a dynamic list of COM objects through the ShellWindows class (continually tracks open windows in the system)
    shell_windows_instances = win32com.client.Dispatch(SHELL_WINDOWS_CLSID)
    # Mapping of window handles to corresponding instances
    shell_windows_instances_mapping = {instance.HWND: instance for instance in shell_windows_instances}
    # Get the handle of the window that is at the top of the z-order (the window on top)
    current_window_handle = win32gui.GetTopWindow(None)
    # Use a while loop to ensure traversing through all windows (in the z-order)
    while current_window_handle:
        # If the current window handle is in the list of currently open 'ShellWindows'
        if current_window_handle in shell_windows_instances_mapping:
            # Retrieve title and path from the current window
            current_window_shell_window_instance = shell_windows_instances_mapping[current_window_handle]
            current_window_title = win32gui.GetWindowText(current_window_handle)
            current_window_path = urllib.parse.unquote(current_window_shell_window_instance.LocationURL).lstrip('/').replace("\\","/")
            current_window_path = SHELL_WINDOWS_OVERRIDE.get(current_window_title, current_window_path)
            # If a path was retrieved
            if (current_window_path != ''):
                # Retrieve the remaining data for the current window
                current_window_placement = list(win32gui.GetWindowPlacement(current_window_handle))
                current_window_coordinates = current_window_placement[4]
                current_window_data = {key: value for key, value in zip(WINDOW_DATA_KEYS, [
                    current_window_handle,
                    current_window_title,
                    current_window_path,
                    len(window_data_retrieved),
                    current_window_placement[1],
                    0,
                    (current_window_coordinates[0], current_window_coordinates[1]),
                    (current_window_coordinates[2] - current_window_coordinates[0], current_window_coordinates[3] - current_window_coordinates[1])
                ])}
                # Append the layout data for the current window to the list of dictionaries
                window_data_retrieved.append(current_window_data)
        # Get the next window in the z-order
        current_window_handle = win32gui.GetWindow(current_window_handle, win32con.GW_HWNDNEXT)

    # Return the current layout window data (as list with dictionaries for each currently open file-explorer windows)
    return window_data_retrieved



# a = get_current_layout_data()
# for x in a:
#     print(x)
# # for x in a[1]:
# #     print(x)
# time.sleep(9999)


# Save the layout data for (currently open) file-explorer windows to a file.
def save_layout():
    """Save the layout data for (currently open) file-explorer windows to a file."""
    # TODO: Implement this function
    print("save_layout")



# FUNCTION: LOAD AND APPLY LAYOUT (ON FILE-EXPLORER-WINDOWS) FROM SPECIFIED LAYOUTFILE
def apply_layout(input_layout_data):
    """Load the layout data from a file and apply it to (currently open) file-explorer windows."""

    # [COMPARE IMPORTED LAYOUT WITH CURRENT LAYOUT]
    # Retrieve the existing layout (data on all shell-windows already open)
    existingLayout_List = (fnExplorerWindows_GetExistingLayout())[0]
    # Initialize variables for dictionary/list-comprehension (for efficiently consolidating data between existing/imported layout)
    existingLayout_itemIndexLookup = {tuple(currItem[1:3]):currIndex for currIndex, currItem in enumerate(existingLayout_List)}
    importedLayout_itemIndexes = [tuple(currItem[1:3]) for currItem in input_layout_data]
    # Compare existing/imported layout and generate pairs of item-indexes based on whether their title/path match or not [[0]:ExistingLayoutIndexes, [1]:ImportLayoutIndexes]
    pairedLayouts_hwndMatched_IndexPairs = [[existingLayout_itemIndexLookup[currItem], currIndex] for currIndex, currItem in enumerate(importedLayout_itemIndexes) if currItem in existingLayout_itemIndexLookup]
    # Retrieve unmatched/remaining item-indexes from existing/imported layout ([ExistingLayout-Indexes] | [ImportedLayout-Indexes])
    existingLayout_hwndUnmatched_Indexes = [currIndex for currIndex in range(len(existingLayout_List)) if currIndex not in [currPair[0] for currPair in pairedLayouts_hwndMatched_IndexPairs]]
    importedLayout_hwndRemaining_Indexes = [currIndex for currIndex in range(len(input_layout_data)) if currIndex not in [currPair[1] for currPair in pairedLayouts_hwndMatched_IndexPairs]]
    # Retrieve the actual window-data for the layout to apply (separated by corresponding type of action)
    createLayout_Windows_Update = [([existingLayout_List[currItem[0]][0]] + input_layout_data[currItem[1]][1:]) for currItem in pairedLayouts_hwndMatched_IndexPairs]
    createLayout_Windows_Create = [input_layout_data[currItem] for currItem in importedLayout_hwndRemaining_Indexes]
    createLayout_Windows_Unused = [existingLayout_List[currItem] for currItem in existingLayout_hwndUnmatched_Indexes]

    # Initialize variables for printing information (from the resulting action performed on them)
    appliedWindowAction_Skipped   = (createLayout_Windows_Unused if not closeUnspecifiedWindows else [])
    appliedWindowAction_Closed    = (createLayout_Windows_Unused if closeUnspecifiedWindows else [])
    appliedWindowAction_Created   = []
    appliedWindowAction_Unchanged = []
    appliedWindowAction_Updated   = []

    # [CLOSE UNMATCHED WINDOWS]
    # If closeUnspecifiedWindows: Close all remaining unused windows in the existing layout
    [win32api.SendMessage(currItem[0], win32con.WM_CLOSE, 0, 0) if closeUnspecifiedWindows else None for currItem in createLayout_Windows_Unused]

    # [CREATE REMAINING WINDOWS]
    # Collect all existing window-handles in a separate list (used to prevent retrieving the same window more than once)
    windowHandles_Processed = [currItem[0] for currItem in createLayout_Windows_Update]
    # If windows specified in the layout file (which don't already exist) should be created
    if createWindowsThatDontExist and len(createLayout_Windows_Create):
        # For each item in createLayout_Windows_Create (windows specified in layout-data that isn't already open)
        for currIndex, currItem in enumerate(createLayout_Windows_Create):
            # Open a (minimized) window for the current item-path
            openPathCommand = ('start /min explorer %s' % currItem[2])
            openPathCommandResult = subprocess.Popen(openPathCommand, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            # Use a while-loop to wait for the newly created window to initialize
            while True:
                # Retrieve any window-handles that hasn't already been processed
                windowHandles_Current = [currInstance.HWND for currInstance in shellWindows_Instances if currInstance.HWND]
                windowHandles_hwndFound = [currHandle for currHandle in windowHandles_Current if currHandle not in windowHandles_Processed]
                # If a new window-handle has been created (and retrieved)
                if len(windowHandles_hwndFound):
                    # Update the current item in createLayout_Windows_Create and add the window-handle to windowHandles_Processed
                    createLayout_Windows_Create[currIndex][0] = windowHandles_hwndFound[0]
                    windowHandles_Processed.append(windowHandles_hwndFound[0])
                    appliedWindowAction_Created.append(createLayout_Windows_Create[currIndex])
                    break


    # [APPLY LAYOUT]
    # Combine imported items into one list (ordered by z-index)
    layoutData_List = (createLayout_Windows_Update + createLayout_Windows_Create)
    layoutData_List = sorted(layoutData_List, key=lambda currItem: currItem[3], reverse=True)
    # For each item in layoutData_List (these are now windows that are currently open)
    for currItem in layoutData_List:
        # Extract current window-data and convert it to valid format for SetWindowPlacement to work
        currWin_WindowState = currItem[4]
        currWin_Coordinates = tuple([currItem[6][0], currItem[6][1], (currItem[6][0] + currItem[7][0]), (currItem[6][1] + currItem[7][1])])
        currWin_Placement = tuple([0, currItem[4], (-1, -1), (-1, -1), currWin_Coordinates])
        # Determine whether the current window is already correct or not
        currWin_Identical = (currWin_Placement == (win32gui.GetWindowPlacement(currItem[0])))
        appliedWindowAction_Unchanged.append(currItem) if currWin_Identical else appliedWindowAction_Updated.append(currItem)
        # Apply layout for the current item
        currWin_TopMost = (win32con.HWND_NOTOPMOST if not currItem[5] else win32con.HWND_TOPMOST)
        win32gui.SetWindowPos(currItem[0], win32con.HWND_TOPMOST, 0, 0, 0, 0, win32con.SWP_NOMOVE | win32con.SWP_NOSIZE)
        win32gui.SetWindowPos(currItem[0], currWin_TopMost, 0, 0, 0, 0, win32con.SWP_NOMOVE | win32con.SWP_NOSIZE)
        win32gui.SetWindowPlacement(currItem[0], currWin_Placement)

    # [PRINT INFORMATION]
    # Remove duplicates
    appliedWindowAction_Updated = [currItem for currItem in appliedWindowAction_Updated if currItem not in appliedWindowAction_Created]
    # Print information
    appliedLayout_Headers = ['SKIPPED', 'CLOSED', 'CREATED', 'UNCHANGED', 'UPDATED']
    appliedLayout_Actions = []
    appliedLayout_Actions.append(sorted(appliedWindowAction_Skipped, key=lambda currItem: currItem[3], reverse=True))
    appliedLayout_Actions.append(sorted(appliedWindowAction_Closed, key=lambda currItem: currItem[3], reverse=True))
    appliedLayout_Actions.append(sorted(appliedWindowAction_Created, key=lambda currItem: currItem[3], reverse=True))
    appliedLayout_Actions.append(sorted(appliedWindowAction_Unchanged, key=lambda currItem: currItem[3], reverse=True))
    appliedLayout_Actions.append(sorted(appliedWindowAction_Updated, key=lambda currItem: currItem[3], reverse=True))
    appliedLayout_Actions_sortedPairs = sorted(zip(appliedLayout_Headers, appliedLayout_Actions), key=lambda x: len(x[1]), reverse=False)
    appliedLayout_Headers = [currItem[0] for currItem in appliedLayout_Actions_sortedPairs]
    appliedLayout_Actions = [currItem[1] for currItem in appliedLayout_Actions_sortedPairs]
    for currIndex, currItem in enumerate(appliedLayout_Actions):
        print('[%s: %s WINDOWS]' % (appliedLayout_Headers[currIndex], len(currItem)))
        print(tabulate(currItem, headers=layoutFile_Keys, tablefmt="rst")) if len(currItem) else ""
        print('\n')

    # # Create a dictionary using a dictionary comprehension
    # applied_layout_dict = {currHeader: currAction for currHeader, currAction in zip(appliedLayout_Headers, appliedLayout_Actions)}
    # print(applied_layout_dict.keys())


    # print('[SKIPPED: %s WINDOWS]' % len(appliedWindowAction_Skipped))
    # (print(tabulate(appliedWindowAction_Skipped, headers=layoutFile_Keys, tablefmt="rst")) if len(appliedWindowAction_Skipped) else "")
    # print('[CLOSED: %s WINDOWS]' % len(appliedWindowAction_Closed))
    # (print(tabulate(appliedWindowAction_Closed, headers=layoutFile_Keys, tablefmt="rst")) if len(appliedWindowAction_Closed) else "")
    # print('[UNCHANGED: %s WINDOWS]' % len(appliedWindowAction_Unchanged))
    # (print(tabulate(appliedWindowAction_Unchanged, headers=layoutFile_Keys, tablefmt="rst")) if len(appliedWindowAction_Unchanged) else "")
    # print('[UPDATED: %s WINDOWS]' % len(appliedWindowAction_Updated))
    # (print(tabulate(appliedWindowAction_Updated, headers=layoutFile_Keys, tablefmt="rst")) if len(appliedWindowAction_Updated) else "")

def main():
    # Resize terminal window (PosX, PosY, Width, Height)
    os.system("TITLE Window Layout (Saver/Loader)")
    win32gui.MoveWindow(win32gui.GetForegroundWindow(), 160, 0, 1600, 720, True)

    # Create the parser
    parser = argparse.ArgumentParser(description='Save or load a window layout',
                                     usage='script.py [-h] --script-action {save,load} [--layout-filepath "window-layout.json"] [--close-unspecified-windows 1] [--create_missing_windows 1] [--include-special-folders 1]')
    # Add the required argument
    parser.add_argument('--script-action', choices=['save', 'load'], required=True, type=str,
                        help='specify whether to save or load layout [example: python script.py --script-action save]')
    parser.add_argument('--layout-filepath', required=True, type=str,
                        help='specify the name of the layout file to save or load [example: python script.py --script-action load --layout-filepath "window-layout.json"]')
    # Add the optional arguments
    parser.add_argument('--close-unspecified-windows', type=int,
                        help='a boolean indicating whether to close existing windows that are not specified in the layout file')
    parser.add_argument('--create_missing_windows', type=int,
                        help='a boolean value indicating whether to create windows that do not exist in the current layout')
    parser.add_argument('--include-special-folders', type=int,
                        help='a boolean value indicating whether to include "special" shell windows in the layout')

    # Parse the arguments
    args = parser.parse_args()
    # Retrieve input arguments (or default to value if unspecified)
    script_action = str(args.script_action)
    layout_filepath = str(args.layout_filepath)
    close_unspecified_windows = (bool(args.close_unspecified_windows) if not (args.close_unspecified_windows is None) else True)
    create_missing_windows = (bool(args.create_missing_windows) if not (args.create_missing_windows is None) else True)
    include_special_folders = (bool(args.include_special_folders) if not (args.include_special_folders is None) else True)

    # Action was set to: Save
    if (script_action.lower() == 'save'):
        # Retrieve the current layout (as a dict)
        current_layout = get_current_layout_data()
        # Create and write data to the layout-file (using the JSON module)
        with open(layout_filepath, 'w') as layout_output_file:
            json.dump(current_layout, layout_output_file, indent=4, sort_keys=False, separators=(',', ': '))
        # Save the layout
        print(f'Saving layout to file "{layout_filepath}" with options: include_special_folders={include_special_folders}')

    # Action was set to: Load
    elif (script_action.lower() == 'load'):
        # Load the layout
        print(f'Loading layout from file "{layout_filepath}" with options: close_unspecified_windows={close_unspecified_windows} create_missing_windows={create_missing_windows} include_special_folders={include_special_folders}')




# By using the if __name__ == '__main__': construct, you can ensure that the "main" function is only run when the script is being executed directly, rather than when it is being imported.
if __name__ == '__main__':
    # Call the main function
    main()
