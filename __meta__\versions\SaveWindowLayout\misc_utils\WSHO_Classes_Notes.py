win32com.client.Dispatch() function in Python, each class provides access to a 
specific feature or functionality of the operating system. Here are some examples
of common classes and their uses:


# Level 1: WScript.Shell
# Provides access to: Windows Script Host (WSH) Object Model
# General functionality of the operating system, such as running programs,
# manipulating the registry, working with the file system, etc. Examples:
# - Running programs and scripts through the Run method,
# - Manipulating the registry using methods such as RegRead, RegWrite, and RegDelete,
# - Interacting with the file system using methods such as CreateShortcut, CopyFile, DeleteFile, and MoveFile,
# - Displaying pop-up message boxes using the Popup method
# - Accessing and manipulating environment variables using the Environment property
# WScript.Shell.SUBCLASSES:
# - WScript.Shell.ShortCut: create and manipulate shortcut files.
# - WScript.Shell.UrlShortCut: create and manipulate shortcut files that link to internet resources.
# - WScript.Shell.Environment: access and manipulate environment variables.
# - WScript.Shell.SpecialFolders: access special folders on the system.
# - WScript.Shell.ScriptExec: execute scripts and provides options for configuring the execution.

AppActivate
CreateShortcut
CurrentDirectory
Environment
Exec
ExpandEnvironmentStrings
LogEvent
Popup
RegDelete
RegRead
RegWrite
Run
SendKeys
SpecialFolders


# Level 2: WScript.Shell.Application:
# Provides access to the 'ShellWindows' windows, windows that are created by
# Windows Internet Explorer (explorer.exe). It allows you to access and
# manipulate the properties and methods of these windows, such as the
# window's title, URL, visibility, and more.

# WScript.Shell - METHODS:
# - WScript.Shell.AppActivate(title): Activates an application window.
# - WScript.Shell.CreateShortcut(link_file): Creates a shortcut object.
# - WScript.Shell.Exec(command): Runs an application in a child command-shell, providing access to the StdIn/StdOut/StdErr streams.
# - WScript.Shell.ExpandEnvironmentStrings(str): Returns the expanded version of a specified environment variable.
# - WScript.Shell.LogEvent(event_type, message, target): Writes an entry to the Windows event log.
# - WScript.Shell.Popup(text, seconds_to_wait, title, flags): Displays a message box and waits for the user to click a button.
# - WScript.Shell.RegDelete(name): Deletes a value or key from the registry.
# - WScript.Shell.RegRead(name): Reads a value from the registry.
# - WScript.Shell.RegWrite(name, value, type): Writes a value to the registry.
# - WScript.Shell.Run(command, [window_style], [wait_on_return]): Runs an application and returns without waiting for the application to terminate.
# - WScript.Shell.SendKeys(keys, wait): Sends one or more keystrokes to the active window (as if typed on the keyboard).
# - WScript.Shell.SpecialFolders(folder_id): Returns the location of special folders.
# - WScript.Shell.Environment(name): Returns the value of an environment variable.

# WScript.Shell - PROPERTIES:
# - WScript.Shell.Application.AddressBar: Represents the address bar of the window.
# - WScript.Shell.Application.Application: Represents the parent object of the window.
# - WScript.Shell.Application.Busy: Indicates if the window is busy.
# - WScript.Shell.Application.CLSID: The CLSID of the window's object.
# - WScript.Shell.Application.ClientToWindow: Converts a point on the client area of the window to a point on the window.
# - WScript.Shell.Application.Container: Represents the container of the window.
# - WScript.Shell.Application.Document: Represents the object that provides access to the document displayed in the window.
# - WScript.Shell.Application.ExecWB: Executes a command on the window.
# - WScript.Shell.Application.FullName: The full name of the window's executable file.
# - WScript.Shell.Application.FullScreen: Indicates if the window is in full-screen mode.
# - WScript.Shell.Application.GetProperty: Returns the value of a property of the window.
# - WScript.Shell.Application.GoBack: Navigates the window's history to the previous page.
# - WScript.Shell.Application.GoForward: Navigates the window's history to the next page.
# - WScript.Shell.Application.GoHome: Navigates the window to its home page.
# - WScript.Shell.Application.GoSearch: Navigates the window to the search page.
# - WScript.Shell.Application.HWND: The window handle of the window.
# - WScript.Shell.Application.Height: The height of the window.
# - WScript.Shell.Application.Left: The left position of the window.
# - WScript.Shell.Application.LocationName: The title of the document displayed in the window.
# - WScript.Shell.Application.LocationURL: The URL of the document displayed in the window.
# - WScript.Shell.Application.MenuBar: Indicates if the menu bar is visible.
# - WScript.Shell.Application.Name: The name of the window.
# - WScript.Shell.Application.Navigate: Navigates the window to a specified URL or file.
# - WScript.Shell.Application.Navigate2: Navigates the window to a specified URL or file and provides additional navigation options.
# - WScript.Shell.Application.Offline: Indicates if the window is in offline mode.
# - WScript.Shell.Application.Parent: Represents the parent of the window.
# - WScript.Shell.Application.Path: The path of the window's executable file.
# - WScript.Shell.Application.PutProperty: Sets the value of a property of the window.
# - WScript.Shell.Application.QueryStatusWB: Returns the status of a command on the window.
# - WScript.Shell.Application.Quit: Closes the window.
# - WScript.Shell.Application.ReadyState: Indicates the readiness state of the document displayed in the window.
# - WScript.Shell.Application.Refresh: Refreshes the document displayed in the window.
# - WScript.Shell.Application.Refresh2: Refreshes the document displayed in the window and provides additional refresh options.
# - WScript.Shell.Application.RegisterAsBrowser: Registers the window as a Web browser.
# - WScript.Shell.Application.RegisterAsDropTarget: Registers the window as a drop target.
# - WScript.Shell.Application.Resizable: Indicates if the window can be resized.
# - WScript.Shell.Application.ShowBrowserBar: Indicates if a browser bar is visible.
# - WScript.Shell.Application.Silent: Indicates if the window is in silent mode.
# - WScript.Shell.Application.StatusBar: Indicates if the status bar is visible.
# - WScript.Shell.Application.StatusText: The text displayed in the status bar of the window.
# - WScript.Shell.Application.Stop: Stops the current navigation.
# - WScript.Shell.Application.TheaterMode: Indicates if the window is in theater mode.
# - WScript.Shell.Application.ToolBar: Indicates if the toolbar is visible.
# - WScript.Shell.Application.Top: The top position of the window.
# - WScript.Shell.Application.TopLevelContainer: Indicates if the window is a top-level container.
# - WScript.Shell.Application.Type: The type of the window.
# - WScript.Shell.Application.Visible: Indicates if the window is visible.
# - WScript.Shell.Application.Width: The width of the window.

# WScript.Shell:
# Provides access to the Windows Script Host object model, which allows you to 
# perform various tasks on the operating system, such as running programs, 
# manipulating the registry, working with the file system, and more.
# - Run a program or script: Run method that allows you to run a program or script with specific command-line arguments.
# - Manipulating the registry: WScript.Shell provides methods such as RegRead, RegWrite, and RegDelete that allow you to read, write, and delete registry values.
# - Working with the file system: WScript.Shell provides methods such as CreateShortcut, CopyFile, DeleteFile, and MoveFile that allow you to create shortcuts, copy, delete, and move files and directories.
# - Pop-up message boxes: Popup method that allows you to display a message box with a specified message, caption, and buttons.
# - Creating environment variables: Environment property that allows you to access and manipulate environment variables.

# Excel.Application:
# Provides access to the Microsoft Excel application, which allows you to create,
# edit, and manipulate Excel spreadsheet files. You can use this class to automate
# tasks such as creating charts, formatting cells, and more.

# Outlook.Application:
# Provides access to the Microsoft Outlook application, which allows you to work 
# with email, contacts, and calendar items. You can use this class to automate 
# tasks such as sending emails, creating appointments, and more.

# WinHttp.WinHttpRequest.5.1:
# Provides access to the WinHTTP functionality, which allows you to send HTTP requests
# and handle HTTP responses. You can use this class to automate tasks such as 
# sending web requests, parsing web pages, and more.

# InternetExplorer.Application:
# Provides access to the Internet Explorer application, which allows you to interact
# with the browser, you can use this class to automate tasks such as navigating 
# to web pages, manipulating the

# ---------------------------------------------------------------------------------

# You can think of the classes provided by the Windows Script Host Object Model
# as being organized hierarchically, with some classes providing more general
# functionality and others providing more specific functionality.

# WScript.Shell class provides access to more general functionality of the operating system, such
# as running programs, manipulating the registry, working with the file system, and more.

# Shell.Application class provides access to more specific functionality of the
# operating system, specifically the ShellWindows collection, which is a collection 
# of all the open Internet Explorer windows and non-Internet Explorer windows that are created by Windows Internet Explorer.

# Other classes such as Excel.Application and Outlook.Application provide
# access to specific functionality of other Microsoft applications.

# You can think of it as a tree, where the WScript.Shell class is at the root
# and other classes branch off from it, providing more specific functionality
# as you move further down the tree.


# It's important to note that this is just one way of thinking about the classes,
# and the actual implementation may be more complex than a simple hierarchical
# structure, but it can be a useful way to understand the relationship between
# the different classes and how they can be used to accomplish different tasks.
