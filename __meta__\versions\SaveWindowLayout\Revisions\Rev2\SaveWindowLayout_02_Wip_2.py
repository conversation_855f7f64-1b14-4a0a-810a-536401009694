# This script retrieves data for currently open ShellWindows (File Explorer windows) and applies a layout to these windows.
# The layout is specified in a JSON file at LAYOUT_FILE_PATH, and the data includes the window handle, title, path, z-index, window state, always-on-top status, position, and size.
# The keys for the retrieved window data are specified using the WINDOW_DATA_KEYS list, and the options for applying the layout are specified using the APPLY_LAYOUT_OPTIONS dictionary.
# The CLSID (Class Identifier) for the ShellWindows COM object is specified using the SHELL_WINDOWS_CLSID constant, and a dynamic list of COM objects is created through the ShellWindows class.



# Import necessary modules
import sys
import os
import time
import json
import subprocess
import urllib.parse
import win32api
import win32gui
import win32com.client
import win32con
import argparse
from tabulate import tabulate


# Constant: CLSID for ShellWindowsPermalink COM object (alternatively 'Shell.Application' could be used)
SHELL_WINDOWS_CLSID = '{9BA05972-F6A8-11CF-A442-00A0C90A8F39}'

# Constants: Initialize aliases to use as reference to the different parts of window data.
WINDOW_DATA_KEYS = [
    'hwnd',  # Window handle
    'title',  # Window title
    'path',  # Window path
    'z_index',  # Window stacking order
    'show_state',  # Window hidden/normal/minimized/maximized state
    'always_on_top',  # Window always-on-top status
    'position',  # Window position (x,y) from top-left corner of the screen
    'size',  # Window size (width, height)
]

# Constant: Used for mapping specified window titles to corresponding CLSID's
SHELL_WINDOWS_OVERRIDE = {
    'Desktop': 'shell:::{B4BFCC3A-DB2C-424C-B029-7FE99A87C641}',
    'This PC': 'shell:::{20D04FE0-3AEA-1069-A2D8-08002B30309D}',
    'Recycle Bin': 'shell:::{645FF040-5081-101B-9F08-00AA002F954E}',
    'All Control Panel Items': 'shell:::{5399E694-6CE5-4D6C-8FCE-1D8870FDCBA0}',
    'Network Connections': 'shell:::{7007ACC7-3202-11D1-AAD2-00805FC1270E}'
}

    """
    Returns a list of dictionaries containing the layout data of each currently open file-explorer window.
    Each dictionary contains the following keys:
    - 'handle': The window handle of the file-explorer window (int).
    - 'title': The title of the file-explorer window (str).
    - 'path': The path of the file-explorer window (str).
    - 'z_index': The z-index of the file-explorer window (int).
    - 'show_state': The window state of the file-explorer window (int).
    - 'always_on_top': Whether or not the file-explorer window is always on top (bool).
    - 'position': The position of the file-explorer window (x, y coordinates) (tuple of int).
    - 'size': The size of the file-explorer window (width, height) (tuple of int).
    :return: A list of dictionaries containing the layout data of each open file-explorer window.
    :rtype: list[dict[int, str, str, int, int, int, tuple(int, int), tuple(int, int)]]
    """
def get_current_layout_data()

egen parameter for: should new windows be created behind or infront of the currently existing windows

hide1:0
hide2:11

# HIDE AND ACTIVATE NEXT
0:  SW_HIDE                       # Hides the window and activates another window

# SHOW AND ACTIVATE
1:  SW_SHOWNORMAL       # Activates and displays a window

# MINIMIZE
2:  SW_SHOWMINIMIZED              # Activates the window and displays it as a minimized window
6:  SW_MINIMIZE                   # Minimizes the window and activates the next top-level window in the Z order
11: SW_FORCEMINIMIZE              # Minimizes a window, even if the thread that owns the window is not responding

# MAXIMIZE
3:  SW_SHOWMAXIMIZED  # Activates the window and displays it as a maximized window
3:  SW_MAXIMIZE  # Activates the window and displays it as a maximized window

4:  SW_SHOWNOACTIVATE             # Displays a window in its most recent size and position without activating it

5:  SW_SHOW                       # Activates the window and displays it in its current size and position

7:  SW_SHOWMINNOACTIVE            # Displays the window as a minimized window without activating it
8:  SW_SHOWNA                     # Displays the window in its current size and position without activating it
9:  SW_RESTORE                    # Activates and displays the window to its original size and position
10: SW_SHOWDEFAULT                # Sets the show state based on the SW_ value by the program that started the application
win32con.(eval("SW_RESTORE"))

NOT USEFUL:
"SW_HIDE" HIDES THE WINDOW ENTIRELY FROM THE TASKBAR ETC
"SW_RESTORE" DISPLAYS WINDOW IN ITS ORIGINAL STATE (FROM WHEN IT WAS CREATED, UNUSABLE UNLESS ITS A RECENTLY CREATED WINDOW)

--
NÅR JEG HENTER Z-INDEX SÅ REORDERER JEG DET SÅNN AT ALLE KOMMER FORRAN ELLER BAK EKSISTERENDE VINDUER, DETTE KUNNE HA VÆRT EN OPTION
win32gui.SetForegroundWindow(currWin_Handle) selecter vindu (uten å endre laout, blir som å sette det først i stacken av vinduer)
Genererer Layout ved første execution dersom ingen argumenter og default layout ikke eksisterer i samme mappe. På den måten kan man bare kopiere scripter/exe til prosjektene man jobber på, så blir config fil generert og nåværende layout.

Kan også legge inn flag for preview.

Burde ha flag for version.

Burde faktisk kanskje være en class, men tror jeg kan vente med. Burde uansett ha det litt i bakhodet ift struktur.

Config: Before/After Existing Windows. Vil helst ikke ha en egen config fil, ideelt sett bør den kunne fungere selvstendig så måtte evt vært optional.

Mulighet til å bruke template layouts. Enten i fil eller predefinerte layouts man velger mellom. I fil så er all data eksponert, men da blir det en ekstra fil som jeg vil unngå.

Lage egne regler, sånn man kan spesifisere ting som: titler som må eksistere, bare applie på skjerm 2 f.eks. Usikker på hvordan jeg kan finne hvilken skjerm som er hvem, men med det så kunne man ha separert layouts på skjerm, pluss swapping, da blir coordinates relative.
--
ACTIVATE ISTEDENFOR SW_RESTORE

0:  "SW_HIDE"            # Hides the window entirely and activates another window
1:  "SW_SHOWNORMAL"      # Activates and displays a window
2:  "SW_SHOWMINIMIZED"   # Activates the window and displays it as a minimized window
3:  "SW_SHOWMAXIMIZED"   # Activates the window and displays it as a maximized window
4:  "SW_SHOWNOACTIVATE"  # Displays a window in its most recent size and position without activating it
5:  "SW_SHOW"            # Activates the window and displays it in its current size and position
6:  "SW_MINIMIZE"        # Minimizes the specified window and activates the next top-level window in the Z order
7:  "SW_SHOWMINNOACTIVE" # Displays the window as a minimized window without activating it
8:  "SW_SHOWNA"          # Displays the window in its current size and position without activating it
9:  "SW_RESTORE"         # Activates and displays the window to its original size and position
10: "SW_SHOWDEFAULT"     # Sets the show state based on the SW_ value by the program that started the application
11: "SW_FORCEMINIMIZE"   # Minimizes a window, even if the thread that owns the window is not responding

    """
    Returns a list of dictionaries containing the layout data of each currently open file-explorer window
    Each dictionary contains the following keys:
    - 'handle': The handle of the window (int).
    - 'title': The title of the window (str).
    - 'path': The path of the window (str).
    - 'z_index': The z-index of the window (int).
    - 'show_state': The show-state of the window, represented as an integer value. Possible values are:
    -               0=SW_HIDE: Hides the window and activates another window
    -               1=SW_RESTORE: Activates and displays the window
    -               2=SW_MINIMIZE: The window is minimized and appears as an icon on the taskbar.
    -               3=SW_MAXIMIZE: The window is maximized and takes up the entire screen."
    - 'show_state': The state (SW_HIDE,SW_RESTORE,SW_MINIMIZE,SW_MAXIMIZE) of the window (int).
    - 'always_on_top': Whether or not the window is always on top (int).
    - 'position': The position of the window (x, y coordinates) (tuple of int).
    - 'size': The size of the window (width, height) (tuple of int).
    """

            #                   0 (SW_HIDE) - the window is hidden
            #                   1 (SW_RESTORE) - the window is restored to its normal size and position
            #                   2 (SW_MINIMIZE) - the window is minimized
            #                   3 (SW_MAXIMIZE) - the window is maximized

# RETRIEVE AND RETURN LAYOUT DATA FOR CURRENTLY OPEN FILE-EXPLORER WINDOWS
def get_current_layout_data():
    """
    Returns a list of dictionaries containing the layout data of each currently open file-explorer window
    Each dictionary contains the following keys:
    - 'handle': The handle of the window (int).
    - 'title': The title of the window (str).
    - 'path': The path of the window (str).
    - 'z_index': The z-index of the file-explorer window (int).
    - 'show_state': The display state of the window (int)
    - 'always_on_top': Whether or not the window is always on top (bool).
    - 'position': The position of the file-explorer window (x, y coordinates) (tuple of int).
    - 'size': The size of the file-explorer window (width, height) (tuple of int).
    :return: A list of dictionaries containing the layout data of each open file-explorer window.
    :rtype: list[dict[int, str, str, int, int, int, tuple(int, int), tuple(int, int)]]
    """
    """
    Returns a list of dictionaries containing the layout data of each currently open file-explorer window
    Each dictionary contains the following keys:
    - 'handle': The handle of the window (int).
    - 'title': The title of the window (str).
    - 'path': The path of the window (str).
    - 'z_index': The z-index of the window (int).
    - 'show_state': The state (hidden/visible/minimized/maximized) of the window (int).
    - 'always_on_top': Whether or not the window is always on top (int).
    - 'position': The position of the window (x, y coordinates) (tuple of int).
    - 'size': The size of the window (width, height) (tuple of int).
    """

            #                   0 (SW_HIDE) - the window is hidden
            #                   1 (SW_RESTORE) - the window is restored to its normal size and position
            #                   2 (SW_MINIMIZE) - the window is minimized
            #                   3 (SW_MAXIMIZE) - the window is maximized
    # Initialize a list to store data for all (currently open) file-explorer windows
    window_data_retrieved = []
    # Initialize a dynamic list of COM objects through the ShellWindows class (continually tracks open windows in the system)
    shell_windows_instances = win32com.client.Dispatch(SHELL_WINDOWS_CLSID)
    # Mapping of window handles to corresponding instances
    shell_windows_instances_mapping = {instance.HWND: instance for instance in shell_windows_instances}
    # Get the handle of the window that is at the top of the z-order (the window on top)
    current_window_handle = win32gui.GetTopWindow(None)
    # Use a while loop to ensure traversing through all windows (in the z-order)
    while current_window_handle:
        # If the current window handle is in the list of currently open 'ShellWindows'
        if current_window_handle in shell_windows_instances_mapping:
            # Retrieve title and path from the current window
            current_window_shell_window_instance = shell_windows_instances_mapping[current_window_handle]
            current_window_title = win32gui.GetWindowText(current_window_handle)
            current_window_path = urllib.parse.unquote(current_window_shell_window_instance.LocationURL).lstrip('/').replace("\\","/")
            current_window_path = SHELL_WINDOWS_OVERRIDE.get(current_window_title, current_window_path)
            # If a path was retrieved
            if (current_window_path != ''):
                # Retrieve the remaining data for the current window
                current_window_placement = list(win32gui.GetWindowPlacement(current_window_handle))
                current_window_coordinates = current_window_placement[4]
                current_window_data = {key: value for key, value in zip(WINDOW_DATA_KEYS, [
                    current_window_handle,
                    current_window_title,
                    current_window_path,
                    len(window_data_retrieved),
                    current_window_placement[1],
                    0,
                    (current_window_coordinates[0], current_window_coordinates[1]),
                    (current_window_coordinates[2] - current_window_coordinates[0], current_window_coordinates[3] - current_window_coordinates[1])
                ])}
                # Append the layout data for the current window to the list of dictionaries
                window_data_retrieved.append(current_window_data)
        # Get the next window in the z-order
        current_window_handle = win32gui.GetWindow(current_window_handle, win32con.GW_HWNDNEXT)

    # Return the current layout window data (as list with dictionaries for each currently open file-explorer windows)
    return window_data_retrieved



# a = get_current_layout_data()
# for x in a:
#     print(x)
# # for x in a[1]:
# #     print(x)
# time.sleep(9999)


# Save the layout data for (currently open) file-explorer windows to a file.
def save_layout():
    """Save the layout data for (currently open) file-explorer windows to a file."""
    # TODO: Implement this function
    print("save_layout")



# FUNCTION: LOAD AND APPLY LAYOUT (ON FILE-EXPLORER-WINDOWS) FROM SPECIFIED LAYOUTFILE
def apply_layout(input_layout_data):
    """Load the layout data from a file and apply it to (currently open) file-explorer windows."""

    # [COMPARE IMPORTED LAYOUT WITH CURRENT LAYOUT]
    # Retrieve the existing layout (data on all shell-windows already open)
    existingLayout_List = (fnExplorerWindows_GetExistingLayout())[0]
    # Initialize variables for dictionary/list-comprehension (for efficiently consolidating data between existing/imported layout)
    existingLayout_itemIndexLookup = {tuple(currItem[1:3]):currIndex for currIndex, currItem in enumerate(existingLayout_List)}
    importedLayout_itemIndexes = [tuple(currItem[1:3]) for currItem in input_layout_data]
    # Compare existing/imported layout and generate pairs of item-indexes based on whether their title/path match or not [[0]:ExistingLayoutIndexes, [1]:ImportLayoutIndexes]
    pairedLayouts_hwndMatched_IndexPairs = [[existingLayout_itemIndexLookup[currItem], currIndex] for currIndex, currItem in enumerate(importedLayout_itemIndexes) if currItem in existingLayout_itemIndexLookup]
    # Retrieve unmatched/remaining item-indexes from existing/imported layout ([ExistingLayout-Indexes] | [ImportedLayout-Indexes])
    existingLayout_hwndUnmatched_Indexes = [currIndex for currIndex in range(len(existingLayout_List)) if currIndex not in [currPair[0] for currPair in pairedLayouts_hwndMatched_IndexPairs]]
    importedLayout_hwndRemaining_Indexes = [currIndex for currIndex in range(len(input_layout_data)) if currIndex not in [currPair[1] for currPair in pairedLayouts_hwndMatched_IndexPairs]]
    # Retrieve the actual window-data for the layout to apply (separated by corresponding type of action)
    createLayout_Windows_Update = [([existingLayout_List[currItem[0]][0]] + input_layout_data[currItem[1]][1:]) for currItem in pairedLayouts_hwndMatched_IndexPairs]
    createLayout_Windows_Create = [input_layout_data[currItem] for currItem in importedLayout_hwndRemaining_Indexes]
    createLayout_Windows_Unused = [existingLayout_List[currItem] for currItem in existingLayout_hwndUnmatched_Indexes]

    # Initialize variables for printing information (from the resulting action performed on them)
    appliedWindowAction_Skipped   = (createLayout_Windows_Unused if not closeUnspecifiedWindows else [])
    appliedWindowAction_Closed    = (createLayout_Windows_Unused if closeUnspecifiedWindows else [])
    appliedWindowAction_Created   = []
    appliedWindowAction_Unchanged = []
    appliedWindowAction_Updated   = []

    # [CLOSE UNMATCHED WINDOWS]
    # If closeUnspecifiedWindows: Close all remaining unused windows in the existing layout
    [win32api.SendMessage(currItem[0], win32con.WM_CLOSE, 0, 0) if closeUnspecifiedWindows else None for currItem in createLayout_Windows_Unused]

    # [CREATE REMAINING WINDOWS]
    # Collect all existing window-handles in a separate list (used to prevent retrieving the same window more than once)
    windowHandles_Processed = [currItem[0] for currItem in createLayout_Windows_Update]
    # If windows specified in the layout file (which don't already exist) should be created
    if createWindowsThatDontExist and len(createLayout_Windows_Create):
        # For each item in createLayout_Windows_Create (windows specified in layout-data that isn't already open)
        for currIndex, currItem in enumerate(createLayout_Windows_Create):
            # Open a (minimized) window for the current item-path
            openPathCommand = ('start /min explorer %s' % currItem[2])
            openPathCommandResult = subprocess.Popen(openPathCommand, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            # Use a while-loop to wait for the newly created window to initialize
            while True:
                # Retrieve any window-handles that hasn't already been processed
                windowHandles_Current = [currInstance.HWND for currInstance in shellWindows_Instances if currInstance.HWND]
                windowHandles_hwndFound = [currHandle for currHandle in windowHandles_Current if currHandle not in windowHandles_Processed]
                # If a new window-handle has been created (and retrieved)
                if len(windowHandles_hwndFound):
                    # Update the current item in createLayout_Windows_Create and add the window-handle to windowHandles_Processed
                    createLayout_Windows_Create[currIndex][0] = windowHandles_hwndFound[0]
                    windowHandles_Processed.append(windowHandles_hwndFound[0])
                    appliedWindowAction_Created.append(createLayout_Windows_Create[currIndex])
                    break


    # [APPLY LAYOUT]
    # Combine imported items into one list (ordered by z-index)
    layoutData_List = (createLayout_Windows_Update + createLayout_Windows_Create)
    layoutData_List = sorted(layoutData_List, key=lambda currItem: currItem[3], reverse=True)
    # For each item in layoutData_List (these are now windows that are currently open)
    for currItem in layoutData_List:
        # Extract current window-data and convert it to valid format for SetWindowPlacement to work
        currWin_WindowState = currItem[4]
        currWin_Coordinates = tuple([currItem[6][0], currItem[6][1], (currItem[6][0] + currItem[7][0]), (currItem[6][1] + currItem[7][1])])
        currWin_Placement = tuple([0, currItem[4], (-1, -1), (-1, -1), currWin_Coordinates])
        # Determine whether the current window is already correct or not
        currWin_Identical = (currWin_Placement == (win32gui.GetWindowPlacement(currItem[0])))
        appliedWindowAction_Unchanged.append(currItem) if currWin_Identical else appliedWindowAction_Updated.append(currItem)
        # Apply layout for the current item
        currWin_TopMost = (win32con.HWND_NOTOPMOST if not currItem[5] else win32con.HWND_TOPMOST)
        win32gui.SetWindowPos(currItem[0], win32con.HWND_TOPMOST, 0, 0, 0, 0, win32con.SWP_NOMOVE | win32con.SWP_NOSIZE)
        win32gui.SetWindowPos(currItem[0], currWin_TopMost, 0, 0, 0, 0, win32con.SWP_NOMOVE | win32con.SWP_NOSIZE)
        win32gui.SetWindowPlacement(currItem[0], currWin_Placement)

    # [PRINT INFORMATION]
    # Remove duplicates
    appliedWindowAction_Updated = [currItem for currItem in appliedWindowAction_Updated if currItem not in appliedWindowAction_Created]
    # Print information
    appliedLayout_Headers = ['SKIPPED', 'CLOSED', 'CREATED', 'UNCHANGED', 'UPDATED']
    appliedLayout_Actions = []
    appliedLayout_Actions.append(sorted(appliedWindowAction_Skipped, key=lambda currItem: currItem[3], reverse=True))
    appliedLayout_Actions.append(sorted(appliedWindowAction_Closed, key=lambda currItem: currItem[3], reverse=True))
    appliedLayout_Actions.append(sorted(appliedWindowAction_Created, key=lambda currItem: currItem[3], reverse=True))
    appliedLayout_Actions.append(sorted(appliedWindowAction_Unchanged, key=lambda currItem: currItem[3], reverse=True))
    appliedLayout_Actions.append(sorted(appliedWindowAction_Updated, key=lambda currItem: currItem[3], reverse=True))
    appliedLayout_Actions_sortedPairs = sorted(zip(appliedLayout_Headers, appliedLayout_Actions), key=lambda x: len(x[1]), reverse=False)
    appliedLayout_Headers = [currItem[0] for currItem in appliedLayout_Actions_sortedPairs]
    appliedLayout_Actions = [currItem[1] for currItem in appliedLayout_Actions_sortedPairs]
    for currIndex, currItem in enumerate(appliedLayout_Actions):
        print('[%s: %s WINDOWS]' % (appliedLayout_Headers[currIndex], len(currItem)))
        print(tabulate(currItem, headers=layoutFile_Keys, tablefmt="rst")) if len(currItem) else ""
        print('\n')

    # # Create a dictionary using a dictionary comprehension
    # applied_layout_dict = {currHeader: currAction for currHeader, currAction in zip(appliedLayout_Headers, appliedLayout_Actions)}
    # print(applied_layout_dict.keys())


    # print('[SKIPPED: %s WINDOWS]' % len(appliedWindowAction_Skipped))
    # (print(tabulate(appliedWindowAction_Skipped, headers=layoutFile_Keys, tablefmt="rst")) if len(appliedWindowAction_Skipped) else "")
    # print('[CLOSED: %s WINDOWS]' % len(appliedWindowAction_Closed))
    # (print(tabulate(appliedWindowAction_Closed, headers=layoutFile_Keys, tablefmt="rst")) if len(appliedWindowAction_Closed) else "")
    # print('[UNCHANGED: %s WINDOWS]' % len(appliedWindowAction_Unchanged))
    # (print(tabulate(appliedWindowAction_Unchanged, headers=layoutFile_Keys, tablefmt="rst")) if len(appliedWindowAction_Unchanged) else "")
    # print('[UPDATED: %s WINDOWS]' % len(appliedWindowAction_Updated))
    # (print(tabulate(appliedWindowAction_Updated, headers=layoutFile_Keys, tablefmt="rst")) if len(appliedWindowAction_Updated) else "")

# ------------------------------------------------------------------------------------------------------------------------------
def main():
    # Resize terminal window (PosX, PosY, Width, Height)
    os.system('TITLE "Shell Folder" Window Layout (Saver/Loader)')
    win32gui.MoveWindow(win32gui.GetForegroundWindow(), 160, 0, 1600, 720, True)

    # Create the parser
    parser = argparse.ArgumentParser(description='Save or restore the window-layout of "Shell Folder" windows',
                                    formatter_class=lambda prog: argparse.HelpFormatter(prog,max_help_position=30),
                                    add_help=False)
    # Add the help argument
    parser.add_argument('-h', '--help', action='help', default=argparse.SUPPRESS,
                        help='Show this help message and exit.')

    # Add the required single arguments
    required_args_group = parser.add_argument_group('required arguments')
    required_args_group.add_argument('--layout-filename', required=True, type=str, metavar='',
                                       help='Specify the layout-file (e.g. python script.py --layout-filename "window-data.json" --save-layout)')

    # Add the required mutually exclusive arguments
    required_args_group_select = parser.add_argument_group('required arguments (choose one)')
    required_args_group_select = required_args_group_select.add_mutually_exclusive_group(required=True)
    required_args_group_select.add_argument('--save-layout', action='store_true',
                       help='Save current layout to file')
    required_args_group_select.add_argument('--load-layout', action='store_true',
                       help='Load and restore layout from file')

    # Add the optional arguments
    optional_args_group = parser.add_argument_group('optional arguments')
    optional_args_group.add_argument('--no-include-special', action='store_false', dest='include_special',
                                help='If specified, exclude "special" shell-windows (e.g. "Control Panel", "Recycle Bin", etc.)')
    optional_args_group.add_argument('--no-close-unspecified', action='store_false', dest='close_unspecified',
                                help='If specified, do not close or re-use any existing windows when loading a layout')
    optional_args_group.add_argument('--no-create-missing', action='store_false', dest='create_missing',
                                help='If specified, do not create any new windows when loading a layout (only apply layout to existing windows)')


    # Parse the arguments
    args = parser.parse_args()
    # Retrieve input arguments (or default to value if unspecified)
    print('save_layout: %s' % args.save_layout)
    print('load_layout: %s' % args.load_layout)
    print('layout_filename: %s' % args.layout_filename)
    print('include_special: %s' % args.include_special)
    print('close_unspecified: %s' % args.close_unspecified)
    print('create_missing: %s' % args.create_missing)

    # Action was set to: Save
    if (args.save_layout):
        print("save")
        # Check if the layout file already exists
        # if os.path.exists(layout_filepath):
        #     # Prompt the user for input
        #     user_input = input(f'Layout file "{layout_filepath}" already exists. Overwrite? (y/n) ')
        #     # If the user does not want to overwrite the file, exit the program
        #     if user_input.lower() != 'y':
        #         print('Exiting program.')
        #         exit()

        # Retrieve the current layout (all currently open file-explorer windows)
        current_layout = get_current_layout_data()
        # Create and write data to the layout-file (using the JSON module)
        with open(args.layout_filename, 'w') as layout_output_file:
            json.dump(current_layout, layout_output_file, indent=4, sort_keys=False, separators=(',', ': '))
        # Save the layout
        # print(f'Saving layout to file "{layout_filename}" with options: include_special={include_special}')


    # Action was set to: Load
    elif (args.load_layout):
        print("load")
        # Load the layout
        # print(f'Loading layout from file "{layout_filepath}" with options: close_unspecified_windows={close_unspecified_windows} create_missing_windows={create_missing_windows} include_special_folders={include_special_folders}')




# By using the if __name__ == '__main__': construct, you can ensure that the "main" function is only run when the script is being executed directly, rather than when it is being imported.
if __name__ == '__main__':
    # Call the main function
    main()
