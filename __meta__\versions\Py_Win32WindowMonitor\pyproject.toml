[build-system]
requires = ["flit_core >=3.2,<4"]
build-backend = "flit_core.buildapi"

[project]
name = "win32-window-monitor"
authors = [{name = "<PERSON>", email = "<EMAIL>"}]
readme = "README.rst"
description = "Monitor/capture title and process of window global events using SetWinEventHook WIN32 API, making it easy to track the current focused window."
# See https://pypi.org/classifiers/ for all classifiers
classifiers = [
    "Development Status :: 2 - Pre-Alpha",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3 :: Only",
    "Intended Audience :: Developers",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Environment :: Win32 (MS Windows)",
    "Operating System :: Microsoft :: Windows :: Windows Vista",
    "Operating System :: Microsoft :: Windows :: Windows 10",
]
license={ file = "LICENSE" }
keywords=["windows", "focus", "process", "SetWinEventHook", "EVENT_SYSTEM_FOREGROUND", "ctypes"]
# version is retrieved from package symbol __version__
dynamic = ["version"]
requires-python = ">=3.9"
dependencies = [
]

[project.urls]
Home = "https://github.com/blep/win32_window_monitor"
Source = "https://github.com/blep/win32_window_monitor"
Issues = "https://github.com/blep/win32_window_monitor/issues"
Documentation = "http://win32-window-monitor.readthedocs.io/"

[project.scripts]
log_focused_window = "win32_window_monitor.main:main"

[project.optional-dependencies]
test = [
    "pytest ~= 7.3.2",
    "coverage ~= 7.3.0",
]
doc = [
    "sphinx ~= 7.2.3",
    "sphinx-rtd-theme ~= 1.3.0",
    "myst-parser ~= 2.0.0",
    "pip-tools ~= 7.3.0",
]

[tool.pytest.ini_options]
testpaths = "tests"
