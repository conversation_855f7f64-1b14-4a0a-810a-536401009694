# Import modules
import time
import os
import random
import argparse
import signal
# Import pywin32 modules
import win32gui
import win32api
import win32com.client
# Import ppretty (for displaying the structure of objects/classes)
from ppretty import ppretty


# Dispatch the 'Shell.Application' object
shell_windows_obj = win32com.client.Dispatch('Shell.Application')
# Retrieve all currently open File-Explorer Windows
shell_windows = [window for window in shell_windows_obj.Windows()]

# If a File-Explorer Window is currently open
if len(shell_windows):
    # Retrieve and print basic window information
    current_object = shell_windows[0]
    current_window_hwnd = current_object.HWND
    current_window_title = current_object.LocationName
    current_window_url = current_object.LocationURL
    print(f"HWND: {current_window_hwnd} | Title: {current_window_title} | URL: {current_window_url}\n")

    # Get the directory structure of the window object
    window_object_structure = ppretty(
        # Object to represent
        current_object,
        # (type:str)  | Indentation level of the output.
        indent='  ',
        # (type:int)  | Maximum width of the output string.
        width=100,
        # (type:int)  | Maximum depth of introspecion.
        depth=5,
        # (type:int)  | Maximum length of sequences (lists, tuples, etc.).
        seq_length=500,
        # (type:bool) | Show or hide "protected" attributes/members (i.e. those that start with "_").
        show_protected=True,
        # (type:bool) | Show or hide "private" attributes/members (i.e. those that start with "__").
        show_private=False,
        # (type:bool) | Show or hide static attributes/members.
        show_static=True,
        # (type:bool) | Show or hide properties (i.e. those defined with the @property decorator).
        show_properties=True,
        # (type:bool) | Show or hide the memory address of the object.
        show_address=False,
        # (type:int)  | Maximum string length.
        str_length=50
    )

    # Write the output to a text file
    script_name = os.path.splitext(os.path.basename(__file__))[0]
    output_name = (script_name + '_output_result.py')
    with open(output_name, 'w') as file:
        file.write(window_object_structure)

    # Finished
    print('Finished')
