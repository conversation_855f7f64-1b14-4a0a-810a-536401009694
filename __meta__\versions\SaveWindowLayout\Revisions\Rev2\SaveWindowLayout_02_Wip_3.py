# This script retrieves data for currently open ShellWindows (File Explorer windows) and applies a layout to these windows.
# The layout is specified in a JSON file at LAYOUT_FILE_PATH, and the data includes the window handle, title, path, z-index, window state, always-on-top status, position, and size.
# The keys for the retrieved window data are specified using the WINDOW_DATA_KEYS list, and the options for applying the layout are specified using the APPLY_LAYOUT_OPTIONS dictionary.
# The CLSID (Class Identifier) for the ShellWindows COM object is specified using the SHELL_WINDOWS_CLSID constant, and a dynamic list of COM objects is created through the ShellWindows class.



# Import necessary modules
import sys
import os
import time
import json
import subprocess
import urllib.parse
import win32api
import win32gui
import win32com.client
import win32con
import argparse
import commentjson
# from tabulate import tabulate


# Constant: CLSID for ShellWindowsPermalink COM object (alternatively 'Shell.Application' could be used)
SHELL_WINDOWS_CLSID = '{9BA05972-F6A8-11CF-A442-00A0C90A8F39}'

# Constants: Default fallback values if called without arguments
CONFIGURATION_DEFAULTS = {
    'default-auto-save-load': True, # Default: On missing args, automatically determine whether to create or load layout
    'default-layout-filename': ['file_name', 'directory_name'][1], # Default: Layout name from directory name
    'default-stacking-order': ['before', 'after'][0] # Default: Z-index before existing windows
}

# Constants: Initialize aliases to use as reference to the different parts of window data.
WINDOW_DATA_KEYS = [
    'hwnd',  # Window handle
    'z_index',  # Window stacking order
    'title',  # Window title
    'path',  # Window path
    'show_state',  # Window hidden/normal/minimized/maximized state
    'always_on_top',  # Window always-on-top status
    'position',  # Window position (x,y) from top-left corner of the screen
    'size',  # Window size (width, height)
]

# Define the constants and their corresponding string values
WINDOW_DISPLAY_STATES = [
    'SW_HIDE',            # 0: Hides the window entirely and activates another window
    'SW_SHOWNORMAL',      # 1: Activates and displays a window
    'SW_SHOWMINIMIZED',   # 2: Activates the window and displays it as a minimized window
    'SW_SHOWMAXIMIZED',   # 3: Activates the window and displays it as a maximized window
    'SW_SHOWNOACTIVATE',  # 4: Displays a window in its most recent size and position without activating it
    'SW_SHOW',            # 5: Activates the window and displays it in its current size and position
    'SW_MINIMIZE',        # 6: Minimizes the specified window and activates the next top-level window in the Z order
    'SW_SHOWMINNOACTIVE', # 7: Displays the window as a minimized window without activating it
    'SW_SHOWNA',          # 8: Displays the window in its current size and position without activating it
    'SW_RESTORE',         # 9: Activates and displays the window to its original size and position
    'SW_SHOWDEFAULT',     # 10: Sets the show state based on the SW_ value by the program that started the application
    'SW_FORCEMINIMIZE',   # 11: Minimizes a window, even if the thread that owns the window is not responding
]


# Constant: Used for mapping specified window titles to corresponding CLSID's
SHELL_WINDOWS_OVERRIDE = {
    'Desktop': 'shell:::{B4BFCC3A-DB2C-424C-B029-7FE99A87C641}',
    'This PC': 'shell:::{20D04FE0-3AEA-1069-A2D8-08002B30309D}',
    'Recycle Bin': 'shell:::{645FF040-5081-101B-9F08-00AA002F954E}',
    'All Control Panel Items': 'shell:::{5399E694-6CE5-4D6C-8FCE-1D8870FDCBA0}',
    'Network Connections': 'shell:::{7007ACC7-3202-11D1-AAD2-00805FC1270E}'
}

# EKSEMPEL-KOMMENTARER:
"""
    WinExplorer is a utility that shows all system's windows in hierarchical
    display. For every window in the hierarchy, you can view its properties,
    like handle, class name, caption, size, position and more. You can also
    modify some properties, like Caption and Visible/Enabled.
    You can use this utility in all Win32-based operating systems:
    Windows 95,98,ME,NT,2000,XP
"""
"""
    WinLayoutManager is a utility for specific classes of system's windows,
    and for bat-scripts
     in hierarchical
    display. For every window in the hierarchy, you can view its properties,
    like handle, class name, caption, size, position and more. You can also
    modify some properties, like Caption and Visible/Enabled.
    You can use this utility in all Win32-based operating systems:
    Windows 95,98,ME,NT,2000,XP
"""

# # --
# kan evt. åpne prompt som ber brukeren om å velge options (for argumentene) når det kjøres

# Argumenter:
# _ --layout-filepath="%CD%" # if-unspecified: script-dir-path | if-specified: "exact-specified-path"

# # evt: (script-name + ".layout.windows.json") | (script-name + ".layout.commands.json") | (script-name + ".layout.settings.json")
# # ".winLayout.LoadCmds.json"
# # ".winLayout.Settings.json": Load:[--exclude-special | --ignore-unspecified | --skip-missing] |||| Save:[--exclude-special]
# _ --layout-name="thisName" # if-unspecified: (script-name + ".winLayout.json")  | if specified: ("exact-specified-name") + (".layout.load.json", ".layout.commands.json", ".layout.settings.json")

#     misc_args_group.add_argument('--exclude-special', action='store_true',
#                                 help='(flag) If specified, do not include "special" shell-windows (e.g. "Control Panel", "Recycle Bin", etc.)')
#     misc_args_group.add_argument('--ignore-unspecified', action='store_true',
#                                 help='(flag) If specified, do not close or re-use any existing windows when loading a layout')
#     misc_args_group.add_argument('--skip-missing', action='store_true',
#                                 help='(flag) If specified, do not create any new windows when loading a layout (only apply layout to existing windows)')
# Is it possible to comment out the first line in the script (commented out in the json file)?
# config = {
#     "param1": "value1",  # This is a comment
#     "param2": "value2",
# }
# # JSON-LAYOUT
#     # // SETTINGS
#     # {
#     #     // (1:UseExistingIfIdentical | 2:CloseExistingIfIdentical | 3:CreateExtraIfIdentical)
#     #     "load_settings_identical_window_exists": 1,
#     #     "onload_action_existing_identical_window": 1,
#     #     // (1:UseExistingIfIdentical | 2:CloseExistingIfIdentical | 3:CreateExtraIfIdentical)
#     #     "load_settings_close": ?,

#     #     // [1,0]
#     #     "save_settings": [IncludeSpecialFolders, ForceCommand/ReplaceExistingLayoutFile],

#     #     // Autobackup if overwriting existing layout-file (ideally which is different only)
#     # },
#     # // EXPLORER WINDOW
#     # {
#     #     "win_title": "SaveWindowLayout",
#     #     "win_path": "file:///D:/Data/Prosjekter/Scripts/WIP/SaveWindowLayout",
#     #     "win_state": [NORMAL,MIN,MAX],
#     #     "win_visible": [TRUE,FALSE],
#     #     "win_topmost": [TRUE,FALSE],
#     #     "win_position": [2100,1],
#     #     "win_size": [1739,703]
#     # },
#     # // COMMAND
#     # {
#     #     "cmd_command": "start 'c:/batscript.bat'",
#     #     "cmd_title": "",
#     #     "cmd_show_state": [NORMAL,MIN,MAX],
#     #     "cmd_always_on_top": [TRUE,FALSE],
#     #     "cmd_position": [2100,1],
#     #     "cmd_size": [1739,703]
#     # },

# _ --layout-filepath="%CD%"
# _ --layout-filepath="%CD%"
# _ --layout-filepath="%CD%"
# _ --layout-filepath="%CD%"
# _ --layout-filepath="%CD%"


# Dersom script calles med (default i medfølgende bat-script) --layout-filepath="%CD%" så settes det til input, om ikke så settes det til "util_layout.json" eller "utildirname_layout.json"


# Der python-scriptet eksisterer eller der det åpnes ifra (py/exe || bat)

# Burde legge opp for å kunne bruke "%~f0" eller "%CD%" i call til scriptet,

# Hvis jeg gjør sånn at layout alltid opprettes fra der det blir callet ifra (snarvei, bat, whatever) så dekker det alle behov tror jeg
# På denne måten så fungerer det på en god måte dersom det ikke blir overstyrt med argumenter (layout-filepath)

# layout-filepath burde ha et annet navn, fordi det er her ting lagres og leses ifra, men ikke bare layout - også config i fremtiden kanskje

# Burde layout-navn være ett eget flag? Det hadde vært logisk å kunne separere navn og path, så separer disse og sett default-vals if not specified

# Burde kunne navngi layouts jeg lager, for gui og for at man enkelt skal kunne bytte mellom "profiler"/"templates"

# EXE: Snarvei_i_Mappe // Kopi_i_Mappe

# Snarvei_i_Mappe:
# + Slipper å dele kildefil for å dele funksjon/configs/layouts, det holder at script/exe eksisterer ett sted du har tilgang til
# +
# Kopi_i_Mappe:


# PY: 1: util.py | 2: util.bat | X:"util_layout.json" | X:"util_config.json" | X:"util_commands.json"

# # Kopi_i_Mappe
# -: Kopierer util.exe/util.py til mappe, kjører (generer current layout), neste gang den kjøres loader den layout (kan byttes ved å rename/slette layout-fil eller med input-flags)
#    navn på layout kan da blir "util_layout.json" eller "utildirname_layout.json"
# # Snarvei_i_Mappe
# -: Kopierer bat til mappe, kjører (generer current layout), neste gang den kjøres loader den layout (kan byttes ved å rename/slette layout-fil eller med input-flags)
#    må da ha en sjekk i scriptet som finner mappen den ble called fra (bat-script) navn på layout kan da blir "bat_layout.json" eller "batdirname_layout.json"
# -: Lage snarvei til util under mappen, kjører (generer current layout), neste gang den kjøres loader den layout (kan byttes ved å rename/slette layout-fil eller med input-flags)
#    må da ha en sjekk i scriptet som finner mappen den ble called fra (snarveien)

# AVKLART:


# SJEKK UT NIRSOFT WINEXPLORER

# OPTION: Action if layout-item-title already exists (use/close/add-extra)

# PROBLEM MED EKSISTERENDE APPLIKASJONER:
# - DisplayFusion: Den lagrer ikke paths på explorerwindows, og oppretter ikke explorer-vinduer som ikke allerede eksisterer
# - Actual Window Manager: Litt usikker, men fant ingen måte å lagre layouts på

# Vurdere: Kan jeg lagre windowstate som èn variabel istedenfor egne keys? (GetWinState/SaveWinState/LoadWinState: Hidden/Normal/Minimized/Maximized/Visible | NOTOPMOST/TOPMOST)
#          winStateVisibility=[Hidden, Visible] (evt winStateVisibility)
#          winStateDisplay=[Normal, Minimized, Maximized] (evt winTitleButtonAction)
#          winStateOptions=[NOTOPMOST, TOPMOST]:
#          - [NOTOPMOST]:Flyttes bakerst i window-stack og vil alltid være nederst i stack
#          - [TOPMOST]:  Flyttes fremst i window-stack of vil alltid prioriteres ovenfor andre vinduer (med mindre et annet med topmost opprettes forran den)
#          Commands=[MoveToFront/MoveToBack, ]


# ALIGNEMT: top:left/center/right | bottom:left/center/right | right:top/center/bottom | left:top/center/bottom



# TITLER: - WinExplorerSaver (bruke win for å indikere vindu istedenfor operativsystemet windows)
#         - WinLayoutManager

# Istedenfor "LayoutTemplates" så kan de kalles "LayoutProfiles" som det gjør i DisplayFusion


# Flags å vurdere: Caption and Visible/Enabled
# Flags å vurdere: bruke "caption" istedenfor "title"?

# NOTES: - BURDE KANSKJE BRUKE "SYSTEM WINDOWS"
#        - Istedenfor config-fil, så kunne jeg ha lest/skrevet til registry, da hadde jeg sluppet ekstra filer (SaveToReg/LoadFromReg)

# KANSKJE LEGGE TIL VERBOSE, OG DERSOM DEN ER PÅ SÅ PRINTER DEN TING SOM WINDOWCLASS/HWND/ZINDEX I JSON-FILE KOMMENTERT UT

# KANSKJE LEGGE INN VINDU-BREDD/HØYDE I PROSENT ISTEDENFOR PIXLER? KAN HENDE DET HAR NOEN ULEMPER, MEN DE SAMME ULEMPENE KOMMER OM JEG LEGGER INN RELATIVT PR SKJERM
# MÅ KUNNE ENDRE MIN/MAX STØRRELSE

# - AUTO1:  DERSOM INGEN FLAGS ER DEFINERT SÅ GÅR DEN TIL /AUTO (CREATE IF NOT EXIST, LOAD IF EXIST)
#           DETTE KUNNE EVT VÆRT EN MUTUALLY EXCLUSIVE FLAG, MEN DA BLIR DEN JO IKKE AUTO
# - AUTO2:  Genererer Layout ved første execution dersom ingen argumenter og default layout ikke eksisterer i samme mappe.
#           På den måten kan man bare kopiere scripter/exe til prosjektene man jobber på, så blir config fil generert og nåværende layout.


# VIKTIG FEATURE: Vinduer lagres relativt til hver monitor, så kommer det heller et flag på screen:[1,2,3 osv], da deles også z-indeks sortering basert på dette

# - FEATURE1: legge inn mulighet for legge til åpning av programmer/filer (f.eks. notepad.exe "path/to/textfile.txt" // photoshop.exe "path/to/image.psd")
#             Siden dette blir vinduer, så må jeg kunne styre rekkefølgen på de på samme måte som shellwins, så kanskje inkludere i layout-fil?
#             Feks: ProcessFilename.exe WindowClass
# - FEATURE2: Kan også legge inn flag for preview.
# - FEATURE3: Config: Before/After Existing Window Stack. Vil helst ikke ha en egen config fil, ideelt sett bør den kunne fungere selvstendig så måtte evt vært optional.
# - FEATURE4: Mulighet til å bruke template layouts. Enten i fil eller predefinerte layouts man velger mellom. I fil så er all data eksponert, men da blir det en ekstra fil som jeg vil unngå.
# - FEATURE5: Lage egne regler, sånn man kan spesifisere ting som: titler som må eksistere, bare applie på skjerm 2 f.eks.
#             Usikker på hvordan jeg kan finne hvilken skjerm som er hvem, men med det så kunne man ha separert layouts på skjerm, pluss swapping, da blir coordinates relative.


# - KANSKJE1: egen parameter for: should new windows be created behind or infront of the currently existing windows
# - KANSKJE2: Burde faktisk kanskje være en class, men tror jeg kan vente med. Burde uansett ha det litt i bakhodet ift struktur.

# - Z-INDEX: ET ANNET NAVN KUNNE KANSKJE HA VÆRT PRIORITY
# - Z-INDEX: NÅR JEG HENTER Z-INDEX SÅ REORDERER JEG DET SÅNN AT ALLE KOMMER FORRAN ELLER BAK EKSISTERENDE VINDUER, DETTE KUNNE HA VÆRT EN OPTION
# - Z-INDEX: win32gui.SetForegroundWindow(currWin_Handle) selecter vindu (uten å endre laout, blir som å sette det først i stacken av vinduer)
# - Z-INDEX: har både z-index og rekkefølge, men tror det er bedre å bruke rekkefølge enn z-index. Kunne evt brukt z-index dersom den var kommentert inn, men default kommentert ut.

# - FIKS1: Burde ha flag for version.


# --


# RETRIEVE AND RETURN LAYOUT DATA FOR CURRENTLY OPEN FILE-EXPLORER WINDOWS
def get_current_layout_data():
    """
    Returns a list of dictionaries containing the layout data of each currently open file-explorer window
    Each dictionary contains the following keys:
    - 'handle': The handle of the window (int)
    - 'z_index': The z-index of the window (int)
    - 'title': The title of the window (str)
    - 'path': The path of the window (str)
    - 'show_state': The display state of the window (int)
    - 'always_on_top': Whether or not the window is always on top (bool)
    - 'position': The position of the window (x, y) tuple(int,int)
    - 'size': The size of the window (width,height) tuple(int,int)
    :return: A list of dictionaries containing the layout data of each open window
    :rtype: list[dict[int, str, str, int, int, int, tuple(int,int), tuple(int,int)]]
    """
    # Initialize a list to store data for all (currently open) windows
    window_data_retrieved = []
    # Initialize a dynamic list of COM objects through the ShellWindows class (continually tracks open windows in the system)
    shell_windows_instances = win32com.client.Dispatch(SHELL_WINDOWS_CLSID)
    # Mapping of window handles to corresponding instances
    shell_windows_instances_mapping = {instance.HWND: instance for instance in shell_windows_instances}
    # Get the handle of the window that is at the top of the z-order (the window on top)
    current_window_handle = win32gui.GetTopWindow(None)
    # Use a while loop to ensure traversing through all windows (in the z-order)
    while current_window_handle:
        # If the current window handle is in the list of currently open 'ShellWindows'
        if current_window_handle in shell_windows_instances_mapping:
            # Retrieve title and path from the current window
            current_window_shell_window_instance = shell_windows_instances_mapping[current_window_handle]
            current_window_title = win32gui.GetWindowText(current_window_handle)
            current_window_path = urllib.parse.unquote(current_window_shell_window_instance.LocationURL).lstrip('/').replace("\\","/")
            current_window_path = SHELL_WINDOWS_OVERRIDE.get(current_window_title, current_window_path)
            # If a path was retrieved
            if (current_window_path != ''):
                # Retrieve the remaining data for the current window
                current_window_placement = list(win32gui.GetWindowPlacement(current_window_handle))
                current_window_coordinates = current_window_placement[4]
                current_window_data = {key: value for key, value in zip(WINDOW_DATA_KEYS, [
                    current_window_handle,
                    len(window_data_retrieved),
                    current_window_title,
                    current_window_path,
                    WINDOW_DISPLAY_STATES[current_window_placement[1]],
                    False,
                    (current_window_coordinates[0], current_window_coordinates[1]),
                    (current_window_coordinates[2] - current_window_coordinates[0], current_window_coordinates[3] - current_window_coordinates[1])
                ])}
                # Append the layout data for the current window to the list of dictionaries
                window_data_retrieved.append(current_window_data)
        # Get the next window in the z-order
        current_window_handle = win32gui.GetWindow(current_window_handle, win32con.GW_HWNDNEXT)

    # Return the current layout window data (as list with dictionaries for each currently open file-explorer windows)
    return window_data_retrieved



# a = get_current_layout_data()
# for x in a:
#     print(x)
# # for x in a[1]:
# #     print(x)
# time.sleep(9999)


# Save the layout data for (currently open) file-explorer windows to a file.
def save_layout():
    """Save the layout data for (currently open) file-explorer windows to a file."""
    # TODO: Implement this function
    print("save_layout")



# FUNCTION: LOAD AND APPLY LAYOUT (ON FILE-EXPLORER-WINDOWS) FROM SPECIFIED LAYOUTFILE
def apply_layout(input_layout_data):
    """Load the layout data from a file and apply it to (currently open) file-explorer windows."""

    # [COMPARE IMPORTED LAYOUT WITH CURRENT LAYOUT]
    # Retrieve the existing layout (data on all shell-windows already open)
    existingLayout_List = (fnExplorerWindows_GetExistingLayout())[0]
    # Initialize variables for dictionary/list-comprehension (for efficiently consolidating data between existing/imported layout)
    existingLayout_itemIndexLookup = {tuple(currItem[1:3]):currIndex for currIndex, currItem in enumerate(existingLayout_List)}
    importedLayout_itemIndexes = [tuple(currItem[1:3]) for currItem in input_layout_data]
    # Compare existing/imported layout and generate pairs of item-indexes based on whether their title/path match or not [[0]:ExistingLayoutIndexes, [1]:ImportLayoutIndexes]
    pairedLayouts_hwndMatched_IndexPairs = [[existingLayout_itemIndexLookup[currItem], currIndex] for currIndex, currItem in enumerate(importedLayout_itemIndexes) if currItem in existingLayout_itemIndexLookup]
    # Retrieve unmatched/remaining item-indexes from existing/imported layout ([ExistingLayout-Indexes] | [ImportedLayout-Indexes])
    existingLayout_hwndUnmatched_Indexes = [currIndex for currIndex in range(len(existingLayout_List)) if currIndex not in [currPair[0] for currPair in pairedLayouts_hwndMatched_IndexPairs]]
    importedLayout_hwndRemaining_Indexes = [currIndex for currIndex in range(len(input_layout_data)) if currIndex not in [currPair[1] for currPair in pairedLayouts_hwndMatched_IndexPairs]]
    # Retrieve the actual window-data for the layout to apply (separated by corresponding type of action)
    createLayout_Windows_Update = [([existingLayout_List[currItem[0]][0]] + input_layout_data[currItem[1]][1:]) for currItem in pairedLayouts_hwndMatched_IndexPairs]
    createLayout_Windows_Create = [input_layout_data[currItem] for currItem in importedLayout_hwndRemaining_Indexes]
    createLayout_Windows_Unused = [existingLayout_List[currItem] for currItem in existingLayout_hwndUnmatched_Indexes]

    # Initialize variables for printing information (from the resulting action performed on them)
    appliedWindowAction_Skipped   = (createLayout_Windows_Unused if not closeUnspecifiedWindows else [])
    appliedWindowAction_Closed    = (createLayout_Windows_Unused if closeUnspecifiedWindows else [])
    appliedWindowAction_Created   = []
    appliedWindowAction_Unchanged = []
    appliedWindowAction_Updated   = []

    # [CLOSE UNMATCHED WINDOWS]
    # If closeUnspecifiedWindows: Close all remaining unused windows in the existing layout
    [win32api.SendMessage(currItem[0], win32con.WM_CLOSE, 0, 0) if closeUnspecifiedWindows else None for currItem in createLayout_Windows_Unused]

    # [CREATE REMAINING WINDOWS]
    # Collect all existing window-handles in a separate list (used to prevent retrieving the same window more than once)
    windowHandles_Processed = [currItem[0] for currItem in createLayout_Windows_Update]
    # If windows specified in the layout file (which don't already exist) should be created
    if createWindowsThatDontExist and len(createLayout_Windows_Create):
        # For each item in createLayout_Windows_Create (windows specified in layout-data that isn't already open)
        for currIndex, currItem in enumerate(createLayout_Windows_Create):
            # Open a (minimized) window for the current item-path
            openPathCommand = ('start /min explorer %s' % currItem[2])
            openPathCommandResult = subprocess.Popen(openPathCommand, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            # Use a while-loop to wait for the newly created window to initialize
            while True:
                # Retrieve any window-handles that hasn't already been processed
                windowHandles_Current = [currInstance.HWND for currInstance in shellWindows_Instances if currInstance.HWND]
                windowHandles_hwndFound = [currHandle for currHandle in windowHandles_Current if currHandle not in windowHandles_Processed]
                # If a new window-handle has been created (and retrieved)
                if len(windowHandles_hwndFound):
                    # Update the current item in createLayout_Windows_Create and add the window-handle to windowHandles_Processed
                    createLayout_Windows_Create[currIndex][0] = windowHandles_hwndFound[0]
                    windowHandles_Processed.append(windowHandles_hwndFound[0])
                    appliedWindowAction_Created.append(createLayout_Windows_Create[currIndex])
                    break


    # [APPLY LAYOUT]
    # Combine imported items into one list (ordered by z-index)
    layoutData_List = (createLayout_Windows_Update + createLayout_Windows_Create)
    layoutData_List = sorted(layoutData_List, key=lambda currItem: currItem[3], reverse=True)
    # For each item in layoutData_List (these are now windows that are currently open)
    for currItem in layoutData_List:
        # Extract current window-data and convert it to valid format for SetWindowPlacement to work
        currWin_WindowState = currItem[4]
        currWin_Coordinates = tuple([currItem[6][0], currItem[6][1], (currItem[6][0] + currItem[7][0]), (currItem[6][1] + currItem[7][1])])
        currWin_Placement = tuple([0, currItem[4], (-1, -1), (-1, -1), currWin_Coordinates])
        # Determine whether the current window is already correct or not
        currWin_Identical = (currWin_Placement == (win32gui.GetWindowPlacement(currItem[0])))
        appliedWindowAction_Unchanged.append(currItem) if currWin_Identical else appliedWindowAction_Updated.append(currItem)
        # Apply layout for the current item
        currWin_TopMost = (win32con.HWND_NOTOPMOST if not currItem[5] else win32con.HWND_TOPMOST)
        win32gui.SetWindowPos(currItem[0], win32con.HWND_TOPMOST, 0, 0, 0, 0, win32con.SWP_NOMOVE | win32con.SWP_NOSIZE)
        win32gui.SetWindowPos(currItem[0], currWin_TopMost, 0, 0, 0, 0, win32con.SWP_NOMOVE | win32con.SWP_NOSIZE)
        win32gui.SetWindowPlacement(currItem[0], currWin_Placement)

    # [PRINT INFORMATION]
    # Remove duplicates
    appliedWindowAction_Updated = [currItem for currItem in appliedWindowAction_Updated if currItem not in appliedWindowAction_Created]
    # Print information
    appliedLayout_Headers = ['SKIPPED', 'CLOSED', 'CREATED', 'UNCHANGED', 'UPDATED']
    appliedLayout_Actions = []
    appliedLayout_Actions.append(sorted(appliedWindowAction_Skipped, key=lambda currItem: currItem[3], reverse=True))
    appliedLayout_Actions.append(sorted(appliedWindowAction_Closed, key=lambda currItem: currItem[3], reverse=True))
    appliedLayout_Actions.append(sorted(appliedWindowAction_Created, key=lambda currItem: currItem[3], reverse=True))
    appliedLayout_Actions.append(sorted(appliedWindowAction_Unchanged, key=lambda currItem: currItem[3], reverse=True))
    appliedLayout_Actions.append(sorted(appliedWindowAction_Updated, key=lambda currItem: currItem[3], reverse=True))
    appliedLayout_Actions_sortedPairs = sorted(zip(appliedLayout_Headers, appliedLayout_Actions), key=lambda x: len(x[1]), reverse=False)
    appliedLayout_Headers = [currItem[0] for currItem in appliedLayout_Actions_sortedPairs]
    appliedLayout_Actions = [currItem[1] for currItem in appliedLayout_Actions_sortedPairs]
    for currIndex, currItem in enumerate(appliedLayout_Actions):
        print('[%s: %s WINDOWS]' % (appliedLayout_Headers[currIndex], len(currItem)))
        print(tabulate(currItem, headers=layoutFile_Keys, tablefmt="rst")) if len(currItem) else ""
        print('\n')

    # # Create a dictionary using a dictionary comprehension
    # applied_layout_dict = {currHeader: currAction for currHeader, currAction in zip(appliedLayout_Headers, appliedLayout_Actions)}
    # print(applied_layout_dict.keys())


    # print('[SKIPPED: %s WINDOWS]' % len(appliedWindowAction_Skipped))
    # (print(tabulate(appliedWindowAction_Skipped, headers=layoutFile_Keys, tablefmt="rst")) if len(appliedWindowAction_Skipped) else "")
    # print('[CLOSED: %s WINDOWS]' % len(appliedWindowAction_Closed))
    # (print(tabulate(appliedWindowAction_Closed, headers=layoutFile_Keys, tablefmt="rst")) if len(appliedWindowAction_Closed) else "")
    # print('[UNCHANGED: %s WINDOWS]' % len(appliedWindowAction_Unchanged))
    # (print(tabulate(appliedWindowAction_Unchanged, headers=layoutFile_Keys, tablefmt="rst")) if len(appliedWindowAction_Unchanged) else "")
    # print('[UPDATED: %s WINDOWS]' % len(appliedWindowAction_Updated))
    # (print(tabulate(appliedWindowAction_Updated, headers=layoutFile_Keys, tablefmt="rst")) if len(appliedWindowAction_Updated) else "")

# ------------------------------------------------------------------------------------------------------------------------------
def main():
    # Resize terminal window (PosX, PosY, Width, Height)
    os.system('TITLE "Shell Folder" Window Layout (Saver/Loader)')
    win32gui.MoveWindow(win32gui.GetForegroundWindow(), 160, 0, 1600, 720, True)

    # Create the parser
    parser = argparse.ArgumentParser(description='Save or restore the window-layout of "Shell Folder" windows',
                                    formatter_class=lambda prog: argparse.HelpFormatter(prog,max_help_position=30),
                                    add_help=False)
    # Add the help argument
    file_args_group = parser.add_argument_group('[GENERAL]')
    file_args_group.add_argument('-h', '--help', action='help', default=argparse.SUPPRESS,
                        help='Show this help message and exit.')
    # Add the version argument
    file_args_group.add_argument('-v', '--version', action='version', version='%(prog)s 1.0',
        help='Show program version number and exit.')


    # Add the file arguments
    file_args_group = parser.add_argument_group('[FILE]')
    file_args_group.add_argument('--layout-filename', type=str, metavar='= ""',
                            help='(input) Specify layout filepath/name | Example: python script.py --layout-filename="layout-file.json"')
    # Add the action arguments
    action_args_group = parser.add_argument_group('[ACTIONS]')
    action_args_group.add_argument('--save', action='store_true',
                            help='(flag) If specified, Save current layout to file')
    action_args_group.add_argument('--load', action='store_true',
                            help='(flag) If specified, Load and restore layout from file')
    # Add the misc arguments
    misc_args_group = parser.add_argument_group('[MISC]')
    misc_args_group.add_argument('--exclude-special', action='store_true',
                                help='(flag) If specified, do not include "special" shell-windows (e.g. "Control Panel", "Recycle Bin", etc.)')
    misc_args_group.add_argument('--ignore-unspecified', action='store_true',
                                help='(flag) If specified, do not close or re-use any existing windows when loading a layout')
    misc_args_group.add_argument('--skip-missing', action='store_true',
                                help='(flag) If specified, do not create any new windows when loading a layout (only apply layout to existing windows)')
    # on_load_args_group = parser.add_argument_group('on action > load-layout')
    # Add the optional arguments
    # on_save_args_group = parser.add_argument_group('on action > save-layout')


    # Parse the arguments
    args = parser.parse_args()
    print(args)
    # Defaults: Use this value if argument was not provided: "--LAYOUT-FILENAME"
    current_folder_name = (os.path.splitext(os.path.basename(__file__))[0] + '_layout.json')
    current_script_name = (os.path.basename(os.path.dirname(os.path.realpath(__file__))) + '_layout.json')
    layout_filename_default = [current_folder_name, current_script_name][1]

    layout_filename = (args.layout_filename if args.layout_filename else layout_filename_default)
    layout_file_exists = os.path.exists(layout_filename)
    layout_file_default = (True if (layout_filename == layout_filename_default) else False)

    save_layout = (False if layout_file_exists and not args.save else True)
    load_layout = (True if args.load or layout_file_exists and not save_layout else False)

    # layout_exists = os.path.exists(layout_filename)
    # save_layout = (True if args.save or not args.load else False)
    # load_layout = (True if not save_layout and os.path.exists(layout_filename) else True)


    # MISSING ARG:"--layout-filename --save-layout" | LAYOUT FILE EXISTS >> LOAD
    # MISSING ARG:"--layout-filename" | LAYOUT FILE EXISTS >> LOAD
    print('layout_filename: %s' % layout_filename)
    print('save_layout: %s' % save_layout)
    print('load_layout: %s' % load_layout)
    # Retrieve input arguments (or default to value if unspecified)
    # print('layout_filename: %s' % args.layout_filename)
    # print('save_layout: %s' % args.save)
    # print('load_layout: %s' % args.load)
    # print('include_special: %s' % args.include_special)
    # print('close_unspecified: %s' % args.close_unspecified)
    # print('create_missing: %s' % args.create_missing)

    # print(defaults_layout_filename)
    # print(defaults_save_layout)
    # print(defaults_save_layout)
    # print(args.layout_filename)
    # Action was set to: Save
    if (args.save):
        # Check if the layout file already exists
        if os.path.exists(layout_filename):
            # Prompt the user for input
            user_input = input(f'Layout file "{layout_filename}" already exists. Overwrite? (y/n) ')
            # If the user does not want to overwrite the file, exit the program
            if user_input.lower() != 'y':
                print('Exiting program.')
                exit()

        # Retrieve the current layout (all currently open file-explorer windows)
        current_layout = get_current_layout_data()
        for x in current_layout:
            print(x)
        # Create and write data to the layout-file (using the JSON module)
        with open(args.layout_filename, 'w') as layout_output_file:
            json.dump(current_layout, layout_output_file, indent=4, sort_keys=False, separators=(',', ': '))
        # Save the layout
        # print(f'Saving layout to file "{layout_filename}" with options: include_special={include_special}')


    # Action was set to: Load
    elif (args.load):
        print("load")
        # Load the layout
        # print(f'Loading layout from file "{layout_filename}" with options: close_unspecified_windows={close_unspecified_windows} create_missing_windows={create_missing_windows} include_special_folders={include_special_folders}')




# By using the if __name__ == '__main__': construct, you can ensure that the "main" function is only run when the script is being executed directly, rather than when it is being imported.
if __name__ == '__main__':
    # # Defaults: Use this value if argument was not provided: "--layout-filename"
    # defaults_save_layout = (True if args.SAVE or not args.SAVE and not args.LOAD else False)
    # # args.SAVE
    # # current_script_name = os.path.dirname(os.path.realpath(__file__))
    # # current_folder_name = os.path.basename(current_script_dir)
    # print(defaults_save_layout)
    # print(defaults_save_layout)
    # print(defaults_save_layout)
    # print(defaults_save_layout)


    # print(default_layout_filename)
    # print(default_layout_filename)
    # Call the main function
    main()
