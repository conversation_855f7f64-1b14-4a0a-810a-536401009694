# Import modules
import os
import sys
import shutil
import time
import datetime
from time import process_time as tic, process_time as toc
import win32gui
import win32process
import win32api
import win32con
import psutil
# Specify a title for self (this will be excluded from layout)
selfTitle = 'Windows.Layout'

print(selfTitle)

os.system('TITLE %s' % selfTitle)
time.sleep(0.1)
# def winEnumHandler( hwnd, ctx ):
#     if win32gui.IsWindowVisible( hwnd ):
#         window_rect  = win32gui.GetWindowRect(hwnd)
#         window_title = win32gui.GetWindowText(hwnd)

#         print(str(window_title) + " - " + str(window_rect))
#         # print ( hex( hwnd ), win32gui.GetWindowText( hwnd ) )

# win32gui.EnumWindows( winEnumHandler, None )
# -----------------------------------------------------------------------------------------------------------------------------
# RESIZE WINDOW (CMD) | SET TITLE | ENABLE ANSI ESCAPE-SEQUENCES (FOR COLORED TEXT)
# -----------------------------------------------------------------------------------------------------------------------------
# Resize terminal window (PosX, PosY, Width, Height)
# win32gui.MoveWindow(win32gui.GetForegroundWindow(), 160, 0, 1600, 720, True)

# import win32gui
# explorerOpened = win32gui.FindWindow('CabinetWClass', None) != 0
# print(explorerOpened)
import win32gui
from win32con import WM_GETTEXTLENGTH, WM_GETTEXT
import ctypes
import win32com.client as win32
import urllib.parse
# import win32gui
def getEditText(hwnd):
    # api returns 16 bit characters so buffer needs 1 more char for null and twice the num of chars
    buf_size = (win32gui.SendMessage(hwnd, WM_GETTEXTLENGTH, 0, 0) + 1) * 2
    target_buff = ctypes.create_string_buffer(buf_size)
    win32gui.SendMessage(hwnd, WM_GETTEXT, buf_size,
                         ctypes.addressof(target_buff))
    # remove the null char on the end
    return target_buff.raw.decode('utf16')[:-1]

explorerWindows = []
# hwnd = win32gui.GetForegroundWindow()
# _, pid = win32process.GetWindowThreadProcessId(hwnd)

# Handler which is called for each open window and saves its information.
# FUNCTION: RETRIEVE WINDOW-DATA
def fnWinEnumHandler( inputHwnd, returnData ):
    # If the input-window is visible
    if win32gui.IsWindowVisible(inputHwnd):
        # print(str(win32gui.GetWindowText(inputHwnd)))
        # only explorer windows have class 'CabinetWClass'
        isExplorerWindow = ('CabinetWClass' in win32gui.GetClassName(inputHwnd))
        if isExplorerWindow:
            text = getEditText(inputHwnd)
            print(text)
            # win32gui.GetClassName(addr_child)
            # children = list(set(searchChildWindows(window)))
            # _, pid = win32process.GetWindowThreadProcessId(inputHwnd)
            # hndl = win32api.OpenProcess(win32con.PROCESS_QUERY_INFORMATION | win32con.PROCESS_VM_READ, 0, pid)
            # path = win32process.GetModuleFileNameEx(hndl, 0)
            # hwnd = win32gui.GetForegroundWindow()
            # path = psutil.Process(pid).exe()

            # print (path)
            # returnData.append(win32gui.GetWindowText(inputHwnd))
            # return win32gui.GetWindowText(inputHwnd)

win32gui.EnumWindows(fnWinEnumHandler, explorerWindows)

explorerOpened = len(explorerWindows) > 0
# print(explorerWindows)
