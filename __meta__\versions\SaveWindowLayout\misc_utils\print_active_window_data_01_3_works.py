# Import modules
import os
import sys
import re
import time
import random
import ctypes
import urllib.parse
from enum import Enum
# Error handling
import traceback
import pywintypes
# Import pywin32 modules
import win32com.client
import win32com.shell.shellcon as shellcon
import win32con
import win32gui
import win32api
import win32process
# Module for coloring the prompt
from ansimarkup import ansiprint, AnsiMarkup, parse





# Resize terminal window (PosX, PosY, Width, Height)
cmd_hwnd = win32gui.GetForegroundWindow()
win32gui.MoveWindow(cmd_hwnd, 160, 120, 1600, 720, True)


# GET ACTIVE/FOREGROUND WINDOW DATA
def get_active_window_info():
    # Get foreground window hwnd
    hwnd = win32gui.GetForegroundWindow()
    if not hwnd:
        return False

    # These will fail if not window
    # if not win32gui.IsWindow(hwnd):
    #     return False

    # Get the current monitor
    monitor_handle = win32api.MonitorFromWindow(hwnd, win32con.MONITOR_DEFAULTTONEAREST)
    monitor_info = win32api.GetMonitorInfo(monitor_handle)
    monitor_device = (monitor_info["Device"])

    # Get general window data: Intermediate values used to compute state/position/size
    hwnd_placement = win32gui.GetWindowPlacement(hwnd)
    hwnd_rect = win32gui.GetWindowRect(hwnd)
    hwnd_controls_state = hwnd_placement[1]
    hwnd_position = (hwnd_rect[0], hwnd_rect[1])
    hwnd_size = (hwnd_rect[2] - hwnd_rect[0], hwnd_rect[3] - hwnd_rect[1])

    # Get general hwnd (Error if 'GetWindowPlacement' or 'GetWindowRect')
    hwnd_visibility_state = win32gui.IsWindowVisible(hwnd)
    hwnd_title = win32gui.GetWindowText(hwnd)
    hwnd_class = win32gui.GetClassName(hwnd)

    # Prepare data for windows with a process handle
    hwnd_process_path = None
    hwnd_process_id = None

    # win32api: Retrieve the executable filename by accessing the current window's process
    # hwnd_thread_id, hwnd_process_id = win32process.GetWindowThreadProcessId(hwnd)
    # hwnd_process_handle = win32api.OpenProcess(win32con.PROCESS_QUERY_INFORMATION, False, hwnd_process_id)

    # CTYPES: Retrieve the process handle of the window
    hwnd_thread_id, hwnd_process_id = win32process.GetWindowThreadProcessId(hwnd)
    hwnd_process_query = (win32con.PROCESS_QUERY_INFORMATION | win32con.PROCESS_VM_READ)
    hwnd_process_handle = ctypes.windll.kernel32.OpenProcess(hwnd_process_query, False, hwnd_process_id)
    # If a process handle was optained
    if hwnd_process_handle:
        try:
            hwnd_process_path = win32process.GetModuleFileNameEx(hwnd_process_handle, 0)
        except pywintypes.error:
            print(traceback.format_exc())


    # Return a dictionary with keys and values for each piece of information
    return {
        "monitor":monitor_device,
        "title":hwnd_title,
        "class":hwnd_class,
        "hwnd":hwnd,
        "visibility":hwnd_visibility_state,
        "controls_state":hwnd_controls_state,
        "position":hwnd_position,
        "size":hwnd_size,
        "placement":hwnd_placement,
        # "rect":hwnd_rect,
        "process_path":hwnd_process_path,
        "process_id":hwnd_process_id
    }


# PRINT COLOR RANGE
# for color_int in range(0,255):
#     os.system('color')
#     color_print = lambda x: ansiprint(f"<fg {color_int}>{x}</fg {color_int}>")
#     color_print('%s: this is the current color' % color_int)


# Specify colors to exclude (dark colors)
dark_colors_exclude = [0] + list(range(16, 22)) + list(range(52, 55)) + list(range(58, 64)) + list(range(232, 241))


# Store the previous window
previous_window_data = None
while True:
    # Get the active window information
    active_window_data = get_active_window_info()
    # If any changes has been made to the current window (continous check)
    if (active_window_data != False) and (active_window_data != previous_window_data):
        try:
            # Enable ANSI escapes and specify a random color (to print to terminal)
            os.system('color')
            color_int = random.randint(15,255)
            while color_int in dark_colors_exclude:
                color_int = random.randint(15,255)
            color_print = lambda x: ansiprint(f"<fg {color_int}>{x}</fg {color_int}>")

            # Check specific changes
            monitor_changed = (previous_window_data and (active_window_data["monitor"] != previous_window_data["monitor"]))
            hwnd_changed = (previous_window_data and (active_window_data["hwnd"] != previous_window_data["hwnd"]))
            pos_changed = (previous_window_data and (active_window_data["position"] != previous_window_data["position"]))
            size_changed = (previous_window_data and (active_window_data["size"] != previous_window_data["size"]))
            placement_changed = (previous_window_data and (active_window_data["placement"] != previous_window_data["placement"]))

            # If the window handle has been changed, log information
            if hwnd_changed or size_changed or pos_changed:
                # Set cmd title (to monitor)
                os.system('TITLE "%s   |   Size:%s   |   Pos:%s"' % (str(active_window_data["monitor"]), str(active_window_data["size"]), str(active_window_data["position"])))


                tmpData = {}
                tmpData.update(active_window_data)
                del tmpData["monitor"]

                # Print separator line relative to window width
                cmd_hwnd_rect = win32gui.GetWindowRect(cmd_hwnd)
                cmd_hwnd_width = (cmd_hwnd_rect[2] - cmd_hwnd_rect[0])
                cmd_line_width = int(cmd_hwnd_width * 0.11)
                color_print("-" * (cmd_line_width))
                # Print (right-justified) window data
                keys = [str(item) for item in list(tmpData.keys())]
                values = [str(item) for item in list(tmpData.values())]
                max_key_length = max(len(key) for key in keys)
                for key, value in zip(keys, values):
                    color_print(key.rjust(max_key_length + 20) + " : " + value)
            # Update the handle to the previous window
            previous_window_data = active_window_data
            # Wait for a short period of time before checking the active window again
            time.sleep(0.15)
        except:
            # color_print('<fg 15>ERROR: %s</fg 15>' % str(active_window_data))
            print(str(previous_window_data))
            print(str(active_window_data))
            print(win32gui.GetForegroundWindow())
            pass
