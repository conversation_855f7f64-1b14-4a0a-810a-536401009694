# Import modules
import os
import sys
import re
import time
import ctypes
import urllib.parse
from enum import Enum
# Import pywin32 modules
import win32com.client
import win32com.shell.shellcon as shellcon
import win32con
import win32gui
import win32process


# TODO:
# - Use os.path.expandvars() to expand environment variables like %appdata% or %programfiles% in the path variable.
#   os.path.expandvars("%USERPROFILE%")
# - Lage egen klasse for WindowSerializer ({window}>str / str>{window} | for save/load)
# - Remove non-printable characters from the title
#   self.hwnd_title = re.sub(r'[^\x00-\x7F]+','', self.hwnd_title)

# TROR WINDOW_STATE ER SAMME ATTRIBUTT SOM TOPMOST (ALWAYS ON TOP)
# TRENGER I SÅ FALL IKKE Å HEMTE DETTE SOM TO ATTRIBUTTER
# hwnd = win32gui.GetForegroundWindow()
# placement = win32gui.GetWindowPlacement(hwnd)
# if placement[1] == win32con.SW_SHOWMINIMIZED:
#     print("The window is minimized")
# elif placement[1] == win32con.SW_SHOWMAXIMIZED:
#     print("The window is maximized")
# elif placement[1] == win32con.SW_SHOWNORMAL:
#     print("The window is normal")
# elif placement[1] == win32con.HWND_TOPMOST:
#     print("The window is always on top")

# Creates a Windows Shell COM object instance and retrieve 'special folder' paths.
def initialize_shell_windows_instance():
    """
    Initializes a COM object instance of the 'Shell.Application' CLSID/ProgID
    and retrieves special folder paths. Return the result of these operations
    as mapped dictionaries (to make them retrievable by 'hwnd' and 'title').
    """

    # In Windows, 'Shell.Application' refers to a COM class that provide access
    # to the top-level object of the Windows Shell (shell32.dll). This includes
    # functionality related to special folders, file explorer windows, accessing
    # folder view settings, desktop, taskbar, and other shell components.
    #
    # Create a COM object instance of the 'Shell.Application' CLSID/ProgID.
    CLSID_SHELL_WINDOWS = 'Shell.Application'
    shell_windows_instance = win32com.client.Dispatch(CLSID_SHELL_WINDOWS)
    # Create a dictionary mapping {'HWND':COM} to retrieve instance from 'hwnd'.
    shell_windows_instance_mapping = {window.HWND: window for window in shell_windows_instance.Windows()}

    # In Windows, 'special folder' is a folder represented as an interface rather
    # than a path, such as 'Desktop', 'Control Panel', 'Recycle Bin', etc.
    # These are registered on the system as constants identified by 'CSIDL'.
    # The following steps are taken to retrieve the paths of these folders:
    #
    # Use 'shellcon' to list the 'CSIDL' identifiers and retrieve their constants.
    CSIDL_CONSTANTS = [getattr(shellcon, name) for name in dir(shellcon) if name.startswith("CSIDL_")]
    # Use 'shell_windows_instance' to create shell object namespaces for each constant.
    csidl_namespaces = [shell_windows_instance.Namespace(constant) for constant in CSIDL_CONSTANTS]
    # Filter out invalid namespaces.
    valid_namespaces = [namespace for namespace in csidl_namespaces if hasattr(namespace, 'Application')]
    # Retrieve identifier ('Name') and path ('Path') from each namespace.
    special_folders = [[namespace.Self.Name, namespace.Self.Path] for namespace in valid_namespaces]
    # Create a dictionary mapping {'Name':'Path'} to enable path to be reached from 'hwnd.title'.
    special_folders_mapping = {item[0]: item[1] for item in special_folders}

    # Return the result
    return shell_windows_instance_mapping, special_folders_mapping



# The 'WindowType' class represents classification of specific window types.
class WindowType(Enum):
    """
    Enumeration class to represent the different types of windows.
    - SPECIAL_FOLDER: explorer window with mapped path to CSIDL constant.
    - NORMAL_FOLDER: explorer window with a retrievable path.
    - UNSPECIFIED: window with a process other than explorer.
    - UNLINKED: window with title and class but no process.
    - UNKNOWN: any remaining windows not matched with a type.
    """
    SPECIAL_FOLDER = 1, 'SPECIAL_FOLDER'
    NORMAL_FOLDER = 2, 'NORMAL_FOLDER'
    UNSPECIFIED = 3, 'UNSPECIFIED'
    UNLINKED = 4, 'UNLINKED'
    UNKNOWN = 5, 'UNKNOWN'


# The 'Window' class represents a window object and holds its properties and methods.
class Window:
    def __init__(self, hwnd):
        # Initialize instance variable for the handle passed from the WindowManager class
        self.hwnd = hwnd

        # Initialize intermediate values used to compute state, position and size
        self._placement = win32gui.GetWindowPlacement(self.hwnd)
        self._rect = win32gui.GetWindowRect(self.hwnd)

        # Initialize instance variables with general window data
        self.hwnd_visibility_state = win32gui.IsWindowVisible(self.hwnd)
        self.hwnd_title = win32gui.GetWindowText(self.hwnd)
        self.hwnd_class = win32gui.GetClassName(self.hwnd)
        self.hwnd_controls_state = self._placement[1]
        self.hwnd_position = (self._rect[0], self._rect[1])
        self.hwnd_size = (self._rect[2] - self._rect[0], self._rect[3] - self._rect[1])

        # Initialize instance variables for type-specific window data
        self.hwnd_process = None
        self.hwnd_process_id = None
        self.hwnd_create_cmd = None
        self.hwnd_type = None


    # Method for retrieving additional (type-specific) window data.
    def get_additional_window_data(self, shell_windows_instance_mapping, special_folders_mapping):
        """
        Parameters:
        - 'shell_windows_instance_mapping': Mapping of 'hwnd' to shell instance, used to
           obtain the path of the folder in case of 'NORMAL_FOLDER'.
        - 'special_folders_mapping': Mapping of 'title' to 'special folder' path, used to
           obtain the path of folder in case of 'SPECIAL_FOLDER'.

        This function updates the following instance variables:
        - 'process': The process associated with the window.
        - 'process_id': The process id associated with the window.
        - 'type': The type of the window ('SPECIAL_FOLDER', 'NORMAL_FOLDER', 'UNSPECIFIED', 'UNLINKED', 'UNKNOWN').
        - 'create_cmd': The command to open the window, if applicable.
        """

        # Retrieve the process handle of the window
        hwnd_thread_id, hwnd_process_id = win32process.GetWindowThreadProcessId(self.hwnd)
        hwnd_process_query = (win32con.PROCESS_QUERY_INFORMATION | win32con.PROCESS_VM_READ)
        hwnd_process_handle = ctypes.windll.kernel32.OpenProcess(hwnd_process_query, False, hwnd_process_id)

        # If a process handle was optained
        if hwnd_process_handle:
            # Update instance variables with the executable path and id
            self.hwnd_process = win32process.GetModuleFileNameEx(hwnd_process_handle, 0)
            self.hwnd_process_id = hwnd_process_id

            # If this is a Windows File-Explorer Window (typically a folder/directory)
            if self.hwnd_class == 'CabinetWClass':
                # Retrieve the folder path through its shell instance
                hwnd_shell_instance = shell_windows_instance_mapping[self.hwnd]
                hwnd_shell_path = hwnd_shell_instance.LocationURL

                # If it's a 'SPECIAL_FOLDER' (explorer window without retrievable path):
                # - Update the instance variable 'type' with the identifier 'SPECIAL_FOLDER'.
                # - Check if the path refers to a GUID (global unique identification number).
                # - Transform the path into a cmd-command (using 'Shell:{GUID}' or 'File:/URI'),
                #   this is to make the path executable (in that it creates actual the window).
                # - Update the instance variable 'create_cmd' with the modified path-command.
                if self.hwnd_title in special_folders_mapping:
                    self.hwnd_type = WindowType.SPECIAL_FOLDER
                    folder_path = special_folders_mapping[self.hwnd_title]
                    folder_path_guid_match = bool(re.search(r'::{[\w-]*}', folder_path))
                    folder_path_is_guid = folder_path_guid_match if not os.path.exists(folder_path) else False
                    command_prefix = 'Shell:' if folder_path_is_guid else 'File:/'
                    create_command = os.path.normpath(urllib.parse.unquote(f'{command_prefix}{folder_path}'))
                    self.hwnd_create_cmd = create_command

                # Else it's a 'NORMAL_FOLDER' (explorer window with a retrievable path):
                # - Update the instance variable 'type' with the identifier 'NORMAL_FOLDER'.
                # - Update the instance variable 'create_cmd' with the normalized path/URI.
                elif (hwnd_shell_path != ''):
                    self.hwnd_type = WindowType.NORMAL_FOLDER
                    self.hwnd_create_cmd = os.path.normpath(urllib.parse.unquote(hwnd_shell_path))

            # Else if this is another type of window process (there's no path to retrieve):
            # - Update the instance variable 'type' with the identifier 'UNSPECIFIED'.
            elif self.hwnd_process and not self.hwnd_type:
                self.hwnd_type = WindowType.UNSPECIFIED


        # If a title and class was found but no type has been retrieved (no associated window process):
        # - Set instance variable 'type' to 'UNLINKED'.
        if self.hwnd_title and self.hwnd_class and not self.hwnd_type:
            self.hwnd_type = WindowType.UNLINKED

        # If no type has been retrieved (not much to go on for this window):
        # - Set instance variable 'type' to 'UNKNOWN'.
        if not self.hwnd_type:
            self.hwnd_type = WindowType.UNKNOWN



class WindowManager:
    def __init__(self):
        # Initialize an empty list to store Window objects.
        self.all_windows = []

        # Initialize condition: Visible (dette blir en input).
        self.visible_windows_only = True

        # Initialize a Windows Shell COM object and retrieve 'special folder' paths.
        a,b = initialize_shell_windows_instance()
        print(a)
        print(b)
        """
        Initializes a COM object instance of the 'Shell.Application' CLSID/ProgID
        and retrieves special folder paths. Return the result of these operations
        as mapped dictionaries (to make them retrievable by 'hwnd' and 'title').
        """

        # In Windows, 'Shell.Application' refers to a COM class that provide access
        # to the top-level object of the Windows Shell (shell32.dll). This includes
        # functionality related to special folders, file explorer windows, accessing
        # folder view settings, desktop, taskbar, and other shell components.
        #
        # Create a COM object instance of the 'Shell.Application' CLSID/ProgID.
        CLSID_SHELL_WINDOWS = 'Shell.Application'
        shell_windows_instance = win32com.client.Dispatch(CLSID_SHELL_WINDOWS)
        # Create a dictionary mapping {'HWND':COM} to retrieve instance from 'hwnd'.
        shell_windows_instance_mapping = {window.HWND: window for window in shell_windows_instance.Windows()}

        # In Windows, 'special folder' is a folder represented as an interface rather
        # than a path, such as 'Desktop', 'Control Panel', 'Recycle Bin', etc.
        # These are registered on the system as constants identified by 'CSIDL'.
        # The following steps are taken to retrieve the paths of these folders:
        #
        #
        # Instead of using win32gui.EnumWindows for retrieving all windows, we are using a
        # continous while loop
        # RETRIEVE AND RETURN LAYOUT DATA FOR CURRENTLY OPEN FILE-EXPLORER WINDOWS
        # def GET_WINS_ZORDER():
        #     # Initialize a list to store data for all (currently open) window handles in
        #     window_data_retrieved = []
        #     # Get the handle of the window that is at the top of the z-order (the window on top)
        #     current_window_handle = win32gui.GetTopWindow(None)
        #     # Use a while loop to ensure traversing through all windows (in the z-order)
        #     while current_window_handle:
        #         window_data_retrieved.append(current_window_handle)
        #         # Get the next window in the z-order
        #         current_window_handle = win32gui.GetWindow(current_window_handle, win32con.GW_HWNDNEXT)

        #     # Return the current layout window data (as list with dictionaries for each currently open file-explorer windows)
        #     return window_data_retrieved
        # a = GET_WINS_ZORDER()

        # Create a COM object instance of the 'Shell.Application' CLSID/ProgID
        #
        initialize_shell_windows_instance()
        # represents the desktop shell (allows access to various additional window data).
        CLSID_SHELL_WINDOWS = 'Shell.Application'
        shell_windows_instance = win32com.client.Dispatch(CLSID_SHELL_WINDOWS)

        # Initialize a dictionary mapping of instance objects to corresponding hwnd's (window handles),
        # this enables access to the instance by hwnd (avoids having to re-initialize the COM object).
        self.shell_windows_instance_mapping = {window.HWND: window for window in shell_windows_instance.Windows()}


        # In Windows, a 'special folder' is a folder that is represented as an abstract path rather
        # than an absolute path, such as 'Desktop', 'Control Panel', 'Recycle Bin', etc.
        # The following steps are taken to retrieve the absolute paths of these folders:
        #  - Use 'shellcon' to list the 'CSIDL' identifiers and retrieve their constants.
        #  - Use 'shell_windows_instance' to create shell object namespaces for each constant.
        #  - Filter out invalid namespaces.
        #  - Retrieve identifier and corresponding path from each namespace.
        CSIDL_CONSTANTS = [getattr(shellcon, name) for name in dir(shellcon) if name.startswith("CSIDL_")]
        csidl_namespaces = [shell_windows_instance.Namespace(constant) for constant in CSIDL_CONSTANTS]
        valid_namespaces = [namespace for namespace in csidl_namespaces if hasattr(namespace, 'Application')]
        special_folders = [[item.Self.Name, item.Self.Path] for item in valid_namespaces]

        print(shell_windows_instance.Windows())
        print(type(shell_windows_instance.Windows()))
        # print((shell_windows_instance.Windows())[0])
        # print(type((shell_windows_instance.Windows())[0]))

        # Initialize a dictionary that maps each item in 'special_folders' to its corresponding
        # 'Name' identifier, this enables access to the data by a window's 'title'.
        self.special_folders_mapping = {item[0]: item[1] for item in special_folders}



# the win32gui.EnumWindows function simply enumerates all open windows and calls a callback function for each window
# it does not create any COM objects or interact with the windows in any way
# enumerate all top-level windows,
# win32gui.EnumWindows is a function that enumerates all open windows and calls a callback function for each window, passing the handle to the window (hwnd) as an argument.

# The win32gui.EnumWindows function is a function that iterates over a list of all open windows on the system and calls a specified callback function for each window.

# The win32gui.EnumWindows function takes a callback function as an argument and calls the callback function for each open window,
# passing the handle to the window (hwnd) as the first argument. The callback function can then use the hwnd value to retrieve additional
# information about the window, such as the window title, the window class, the window style, etc.

# win32gui.EnumWindows is a function in the win32gui module of the pywin32 library that allows you to enumerate all top-level windows on the screen.
# It takes a callback function and a data argument as arguments, and it calls the callback function for each window, passing the window handle (HWND)
# and the data argument as arguments. The callback function should return True to continue enumerating windows, or False to stop enumerating.
# EnumWindows(callback[, data])
# The callback argument is a function that will be called for each window. It should have the following signature:

# The hwnd argument is the window handle (HWND) of the current window. The data argument is the data argument that was passed to EnumWindows.
# The callback function should return True to continue enumerating windows, or False to stop enumerating.

    # def get_all_open_windows_enumerate(self):
    #     def window_enum_handler(hwnd, ctx):
    #         if win32gui.IsWindowVisible(hwnd):
    #             print ( hex( hwnd ), win32gui.GetWindowText( hwnd ) )

    #     win32gui.EnumWindows( window_enum_handler, None )
    def get_open_windows(self):
        # Reset the list of Window objects
        self.all_windows = []

        #
        # Instead of using
    #     # Retrieve all currently open windows in the Z order
    #     # Instead of using enumerating all windows through win32gui.EnumWindows,
    #     # a continous while loopto enumerate all windows, for retrieving all windows, we are using a
    #     # continous while loop
    #     # RETRIEVE AND RETURN LAYOUT DATA FOR CURRENTLY OPEN FILE-EXPLORER WINDOWS
    #     # Initialize a list to store data for all (currently open) window handles in
    #     window_data_retrieved = []

    #     # Get the handle of the window that is at the top of the z-order (the window on top)
    #     current_hwnd = win32gui.GetTopWindow(None)
    #     # Use a while loop to ensure traversing through all windows (in the z-order)
    #     while current_hwnd:
    #         window_data_retrieved.append(current_hwnd)
    #         # Get the next window in the z-order
    #         current_hwnd = win32gui.GetWindow(current_hwnd, win32con.GW_HWNDNEXT)

    #     # Return the current layout window data (as list with dictionaries for each currently open file-explorer windows)
    #     return window_data_retrieved
    #     a = GET_WINS_ZORDER()

    def wm_get_open_windows(self):
        # Reset the list of Window objects
        self.all_windows = []
        # Enumerate all windows and pass them to the wm_enumerate_windows_callback method
        win32gui.EnumWindows(self.wm_enumerate_windows_callback, None)

    def wm_enumerate_windows_callback(self, hwnd, _):
        # visibleOnly or AllWindows
        if (self.visible_windows_only and win32gui.IsWindowVisible(hwnd)) or (not self.visible_windows_only):
            # Create a Window object for the current window
            window = Window(hwnd)
            # Get additional type-specific window data for the current window
            window.get_additional_window_data(self.shell_windows_instance_mapping, self.special_folders_mapping)
            # Add the current window to the list of windows
            self.all_windows.append(window)











    def wm_save(self, filename):
        # Open the file at the specified filepath in write mode
        # with open(filename, "w", encoding="utf-8") as file:
        with open(filename, "w") as file:
            # Iterate through the list of retrieved windows
            for window in self.all_windows:
                # print(win32gui.GetWindow(window.hwnd, GWL_EXSTYLE))
                # print('%s: %s' % (window.hwnd, window.title))
                # print(win32gui.GetWindowPlacement(window.hwnd))
                print('hwnd: %s' % str(window.hwnd))
                print('hwnd_visibility_state: %s' % str(window.hwnd_visibility_state))
                print('hwnd_title: %s' % str(window.hwnd_title))
                print('hwnd_class: %s' % str(window.hwnd_class))
                print('hwnd_controls_state: %s' % str(window.hwnd_controls_state))
                print('hwnd_position: %s' % str(window.hwnd_position))
                print('hwnd_size: %s' % str(window.hwnd_size))
                print('hwnd_process: %s' % str(window.hwnd_process))
                print('hwnd_process_id: %s' % str(window.hwnd_process_id))
                print('hwnd_create_cmd: %s' % str(window.hwnd_create_cmd))
                print('hwnd_type: %s' % str(window.hwnd_type))
                print('-')
                print('hwnd_placement: %s' % str(window._placement))
                print('hwnd_rect: %s' % str(window._rect))
                print('--------------------------------------\n')
                # Save the current window to the file
                # window.save(file)
                # file.write(f"\n\n")
                # file.write(f"{window.type}\n")
                # file.write(f"{window.title}\n")
                # file.write(f"{window.class_name}\n")
                # file.write(f"{window.position[0]}, {window.position[1]}\n")
                # file.write(f"{window.size[0]}, {window.size[1]}\n")
                # if window.process:
                #     file.write(f"{window.process}\n")
                # if window.path:
                #     file.write(f"{window.path}\n")
            print("---------------------")
            print(len(self.all_windows))

def main(args):
    # If the first command-line argument is 'save'
    if args[0] == "save":
        # Create a new WindowManager object
        manager = WindowManager()
        # Update the list of windows in the WindowManager object
        manager.wm_get_open_windows()
        # Save the windows to the file at the specified filepath
        manager.wm_save(args[1])
    # If the first command-line argument is 'load'
    elif args[0] == "load":
        pass


manager = WindowManager()
manager.wm_get_open_windows()
manager.wm_save("windows.txt")
