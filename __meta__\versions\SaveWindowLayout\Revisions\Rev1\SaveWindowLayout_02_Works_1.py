# Import modules
import os
import win32gui
import win32com.client as win32

from win32api import GetSystemMetrics
# print ("width = %s" % GetSystemMetrics (0))
# print ("height = %s" % GetSystemMetrics (1))

# FUNCTION: RETRIEVE AND RETURN FILE-EXPLORER WINDOWS (EXPLORER WINDOW-HANDLES AND THEIR PATHS)
def fnRetrieveOpenExplorerWindows():
    # Create return variables (windowHandles/windowPaths)
    explorerHwndData = []
    # COM-Object CLSID (ShellWindowsPermalink)
    clsIdShellWindows = '{9BA05972-F6A8-11CF-A442-00A0C90A8F39}'
    # For each explorer window currently open
    for currWin in win32.Dispatch(clsIdShellWindows):
        # Retrieve and append the current window-handle and its path
        currWinHwnd = currWin.HWND
        currWinPath = currWin.LocationURL.replace('file:///','')
        print(currWin.Height)
        print(currWin.Width)
        print(currWin.Top)
        print(currWin.Left)

        explorerHwndData.append([currWinHwnd, currWinPath])
    # Return the result
    return explorerHwndData


openExplorerWindows = fnRetrieveOpenExplorerWindows()

for currIndex, currItem in enumerate(openExplorerWindows):
    print(currIndex)
    currWinHwnd = openExplorerWindows[currIndex][0]
    currWinPath = openExplorerWindows[currIndex][1]
    currWinTitle = win32gui.GetWindowText(currWinHwnd)
    currWinSize = win32gui.GetWindowRect(currWinHwnd)
    print(currWinHwnd)
    print(currWinPath)
    print(currWinTitle)
    print(currWinSize)
    print('\n')
