{
	"folders":
	[
		{
			"path": "C:\\Users\\<USER>\\Desktop\\PRJ\\GIT\\JHP\\WORKFLOW\\PRJ__WIP\\Py_Win32WindowMonitor"
		}
	],
    "build_systems": [
        {
            "name": "<Py_Prj> \t Py_Win32WindowMonitor",

            // default:
            // "shell_cmd": "\"%APPDATA%/_py_venvs/Py_Win32WindowMonitor__Python310/venv/Scripts/python.exe\" -u \"$file\"",

            // args:
            // "shell_cmd": "\"%APPDATA%/_py_venvs/Py_Win32WindowMonitor__Python310/venv/Scripts/python.exe\" -u \"$file\" -i bookmarks.json -o output_dir",
            "shell_cmd": "\"%APPDATA%/_py_venvs/Py_Win32WindowMonitor__Python310/venv/Scripts/python.exe\" -m \"$file\" -i bookmarks.json -o output_dir",
            // "shell_cmd": "\"%APPDATA%/_py_venvs/Py_Win32WindowMonitor__Python310/venv/Scripts/python.exe\" -m \"${project_path}/py_bookmarkfolderizer/main.py\"",
            "selector": "source.python",
            "env": {"PYTHONIOENCODING": "utf-8"},
            "working_dir": "${project_path}",
            "file_regex": "^[ ]*File \"(...*?)\", line ([0-9]*)"
        }
    ],
}
