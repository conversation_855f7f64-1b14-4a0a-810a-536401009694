# Import modules
import os
import sys
import re
import time
import ctypes
import urllib.parse
from enum import Enum
# Import pywin32 modules
import win32com.client
import win32com.shell.shellcon as shellcon
import win32con
import win32gui
import win32process

# WINDOW:"STRINGS"
# window.hwnd.get()
# window.title.get()
# window.class.get()
# window.process.get()
# window.process_id.get()
# window.type.get()
# window.create_cmd.get()

# WINDOW:[OBJECTS]
# window.focus.get() :: true / false
# window.focus.set() :: true / false
# window.state.get() :: min / max / (normal/original/unchanged)
# window.state.set() :: min / max / (normal/original/unchanged)
# window.visible.get() :: true / false
# window.visible.set() :: true / false
# window.position.set() :: (x, y)
# window.position.get() :: (x, y)
# window.size.set() :: (width, height)
# window.size.get() :: (width, height)
# window.monitor.get() :: 0/1/2/etc
# window.monitor.set() :: 0/1/2/etc
# window.topmost.set() :: true / false
# window.topmost.get() :: true / false

# WINDOW.FOLDER:[OBJECTS]
# window.folder.view_options.icon_size.get() :: 16/32/64/etc
# window.folder.view_options.icon_size.set() :: 16/32/64/etc
# window.folder.view_options.view_mode.get() :: PREDEFINED_DICT[?] :: 1/2/3/4/5/6/7/8
# window.folder.view_options.view_mode.set() :: PREDEFINED_DICT[?] :: 1/2/3/4/5/6/7/8
# window.folder.view_options.sort_column.get() :: [- / + | VALUE] :: PREDEFINED_DICT[?] :: (examples "prop:-System.Size;")
# window.folder.view_options.sort_column.set() :: [- / + | VALUE] :: PREDEFINED_DICT[?] :: (examples "prop:-System.Size;")
# window.folder.view_options.group_by.get() :: [- / + | VALUE] :: PREDEFINED_DICT[?] :: (examples "System.DateModified" | "System.Null")
# window.folder.view_options.group_by.set() :: [- / + | VALUE] :: PREDEFINED_DICT[?] :: (examples "System.DateModified" | "System.Null")

# WINDOW.FILES:[OBJECTS]
# window.folder.files.selected_files.get() :: LIST ["filename1.txt", "*.png"] :: (legge inn mulighet for wildcards)
# window.folder.files.selected_files.set() :: LIST ["filename1.txt", "*.png"] :: (legge inn mulighet for wildcards)
# window.folder.files.focused_file.get() :: "filename1.txt"
# window.folder.files.focused_file.set() :: "filename1.txt"
# window.folder.files.all_files.get() :: TRENGER IKKE Å EKSPONERE DENNE
# window.folder.files.all_files.set() :: TRENGER IKKE Å EKSPONERE DENNE



# [FOLDER]
# window.hwnd
# window.title

# window.visibility_state (get/set)
# window.placement (get/set)

# window.rect (get/set) DENNE KAN KANSKJE EKSKLUDERES PGA PLACEMENT

# window.title (get)
# window.class (get)
# window.controls_state (get/set) :: min/max/normal/topmost
# window.position :: hentes fra placement :: screen er også en del av position (screenspace?)
# window.size :: hentes fra placement
# window.process (get)
# window.process_id (get)
# window.type (get)
# window.create_cmd (get) :: kan settes fra tekstfil?

# window.folder.path

        # self.hwnd = hwnd
        # self._placement = win32gui.GetWindowPlacement(self.hwnd)
        # self._rect = win32gui.GetWindowRect(self.hwnd)
        # self.visibility_state = win32gui.IsWindowVisible(self.hwnd)
        # self.title = win32gui.GetWindowText(self.hwnd)
        # self.class = win32gui.GetClassName(self.hwnd)
        # self.controls_state = self._placement[1]
        # self.position = (self._rect[0], self._rect[1])
        # self.size = (self._rect[2] - self._rect[0], self._rect[3] - self._rect[1])

        # self.process = None
        # self.process_id = None

        # self.type = None
        # self.create_cmd = None

        # self.folder_icon_size = None
        # self.folder_view_mode = None
        # self.folder_sort_column = None
        # self.folder_group_by = None
        # self.folder_selected_files = None
        # self.folder_all_files = None
        # self.folder_focused_file = None


# BURDE SKILLE MELLOM FOLDER / WINDOW

# [FOLDER] : SHELL WINDOWS (RETRIEVES WINDOW DATA THROUGH COM OBJECT)
# - shell_explorer_window
# - special_folder_window
# [WINDOW] : GENERAL WINDOWS (RETRIEVES WINDOW DATA DIRECTLY FROM WINDOW HANDLE)
# process_window
# unlinked_window
# unknown_window

# STRATEGY PATTERN

    # In Windows, 'special folder' is a folder represented as an interface rather
    # than a path, such as 'Desktop', 'Control Panel', 'Recycle Bin', etc.
    # These are registered on the system as constants identified by 'CSIDL'.
# shell_folder | special_folder |
# PATTERN FOR Å FINNE VINDU OG/ELLER VINDUKONTROLLERE

# MÅ LEGGE INN RIBBON-MINIMIZE/MAXIMIZE

# MÅ LEGGE INN MULIGHET FOR RULLERING I STACKEN

# TODO:
# - Use os.path.expandvars() to expand environment variables like %appdata% or %programfiles%
#   in the path variable. os.path.expandvars("%USERPROFILE%")
# - Lage egen klasse for WindowSerializer ({window}>str / str>{window} | for save/load)
# - Remove non-printable characters from the title
#   self.hwnd_title = re.sub(r'[^\x00-\x7F]+','', self.hwnd_title)

# TROR WINDOW_STATE ER SAMME ATTRIBUTT SOM TOPMOST (ALWAYS ON TOP)
# TRENGER I SÅ FALL IKKE Å HEMTE DETTE SOM TO ATTRIBUTTER
# hwnd = win32gui.GetForegroundWindow()
# placement = win32gui.GetWindowPlacement(hwnd)
# if placement[1] == win32con.SW_SHOWMINIMIZED:
#     print("The window is minimized")
# elif placement[1] == win32con.SW_SHOWMAXIMIZED:
#     print("The window is maximized")
# elif placement[1] == win32con.SW_SHOWNORMAL:
#     print("The window is normal")
# elif placement[1] == win32con.HWND_TOPMOST:
#     print("The window is always on top")

"""
One weakness of the pywin32 library is that it's a wrapper around the --Win32 API--,
so it's only available on Windows and can only be used to interact with
Windows-specific features. Additionally, since it's a wrapper around the Win32 API,
it can be quite low-level and may require a lot of code to accomplish certain tasks.
Additionally, the library is relatively old and some of the functionality may be
deprecated and not recommended to use, or could have some bugs. Another weakness
is that the library requires some knowledge of Windows internals and the Win32 API,
so it might have a steeper learning curve for some developers.
"""




# Creates a Windows Shell COM object instance and retrieve 'special folder' paths.
def initialize_shell_windows_instance():
    """
    Initializes a COM object instance of the 'Shell.Application' CLSID/ProgID
    and retrieves special folder paths. Return the result of these operations
    as mapped dictionaries (to make them retrievable by 'hwnd' and 'title').
    """

    # In Windows, 'Shell.Application' refers to a COM class that provide access
    # to the top-level object of the Windows Shell (shell32.dll). This includes
    # functionality related to special folders, file explorer windows, accessing
    # folder view settings, desktop, taskbar, etc.
    #
    # The following steps are taken:
    # - Create a COM object instance of the 'Shell.Application' CLSID/ProgID.
    # - Dictionary map {'HWND':COM} to retrieve instance from 'hwnd'.
    CLSID_SHELL_WINDOWS = 'Shell.Application'
    shell_windows_instance = win32com.client.Dispatch(CLSID_SHELL_WINDOWS)
    shell_windows_instance_mapping = {window.HWND: window for window in shell_windows_instance.Windows()}

    # In Windows, 'special folder' is a folder represented as an interface rather
    # than a path, such as 'Desktop', 'Control Panel', 'Recycle Bin', etc.
    # These are registered on the system as constants identified by 'CSIDL'.
    #
    # The following steps are taken:
    # - Use 'shellcon' to list the 'CSIDL' identifiers and retrieve their constants.
    # - Use 'shell_windows_instance' to create shell object namespaces for each constant.
    # - Filter out invalid namespaces.
    # - Retrieve identifier ('Name') and path ('Path') from each namespace.
    # - Dictionary map {'Name':'Path'} to enable path to be reached from 'title'.

    # NEW COMMENT: WHEN ADDING A PATH TO THE NAMESPACE, YOU CREATE AN OBJECT FOR THE FOLDER WHICH ALLOWS YOU TO RETRIEVE/PERFORM OPERATIONS
    #              shell_windows_obj = win32com.client.Dispatch('Shell.Application')
    #              folder_path = os.path.normpath("D:/Data/Prosjekter/Scripts/WIP/SaveWindowLayout/misc_utils")
    CSIDL_CONSTANTS = [getattr(shellcon, name) for name in dir(shellcon) if name.startswith("CSIDL_")]
    csidl_namespaces = [shell_windows_instance.Namespace(constant) for constant in CSIDL_CONSTANTS]
    valid_namespaces = [namespace for namespace in csidl_namespaces if hasattr(namespace, 'Application')]
    special_folders = [[namespace.Self.Name, namespace.Self.Path] for namespace in valid_namespaces]
    special_folders_mapping = {item[0]: item[1] for item in special_folders}

    # Return the result
    return shell_windows_instance_mapping, special_folders_mapping



# The 'WindowType' class represents classification of specific window types.
class WindowType(Enum):
    """
    Enumeration class to represent the different types of windows.
    - SPECIAL_FOLDER: explorer window with mapped path to CSIDL constant.
    - NORMAL_FOLDER: explorer window with a retrievable path.
    - UNSPECIFIED: window with a process other than explorer.
    - UNLINKED: window with title and class but no process.
    - UNKNOWN: any remaining windows not matched with a type.
    """
    SPECIAL_FOLDER = 1, 'SPECIAL_FOLDER'
    NORMAL_FOLDER = 2, 'NORMAL_FOLDER'
    UNSPECIFIED = 3, 'UNSPECIFIED'
    UNLINKED = 4, 'UNLINKED'
    UNKNOWN = 5, 'UNKNOWN'


# The 'Window' class represents a window object and holds its properties and methods.
class Window:
    def __init__(self, hwnd):
        # Initialize instance variables with general window data.
        self.hwnd = hwnd
        self._placement = win32gui.GetWindowPlacement(self.hwnd)
        self._rect = win32gui.GetWindowRect(self.hwnd)
        self.hwnd_visibility_state = win32gui.IsWindowVisible(self.hwnd)
        self.hwnd_title = win32gui.GetWindowText(self.hwnd)
        self.hwnd_class = win32gui.GetClassName(self.hwnd)
        self.hwnd_controls_state = self._placement[1]
        self.hwnd_position = (self._rect[0], self._rect[1])
        self.hwnd_size = (self._rect[2] - self._rect[0], self._rect[3] - self._rect[1])

        # Prepare instance variables for the window process.
        self.hwnd_process = None
        self.hwnd_process_id = None

        # Prepare instance variables for the window type and create-command.
        self.hwnd_type = None
        self.hwnd_create_cmd = None

        # Prepare instance variables for (explorer) folder view options.
        self.folder_icon_size = None
        self.folder_view_mode = None
        self.folder_sort_column = None
        self.folder_group_by = None
        # Prepare instance variable for file selection in folder.
        self.folder_selected_files = None
        self.folder_all_files = None
        self.folder_focused_file = None


    # Method for retrieving the window processes.
    def get_window_process(self):
        # Retrieve the process handle of the window
        hwnd_thread_id, hwnd_process_id = win32process.GetWindowThreadProcessId(self.hwnd)
        hwnd_process_query = (win32con.PROCESS_QUERY_INFORMATION | win32con.PROCESS_VM_READ)
        hwnd_process_handle = ctypes.windll.kernel32.OpenProcess(hwnd_process_query, False, hwnd_process_id)

        # If a process handle was optained
        if hwnd_process_handle:
            # Update instance variables with the executable path and id
            self.hwnd_process = win32process.GetModuleFileNameEx(hwnd_process_handle, 0)
            self.hwnd_process_id = hwnd_process_id
            # Update 'type' with the identifier 'UNSPECIFIED'.
            self.hwnd_type = WindowType.UNSPECIFIED

        # Else if no process was retrieved, set 'type' to 'UNLINKED'.
        elif not self.hwnd_type:
            self.hwnd_type = WindowType.UNLINKED


    # Method for retrieving additional (type-specific) window data.
    def get_explorer_windows(self, shell_windows_instance_mapping, special_folders_mapping):
        """
        Parameters:
        - 'shell_windows_instance_mapping': Mapping of 'hwnd' to shell instance, used to
           obtain the path of the folder in case of 'NORMAL_FOLDER'.
        - 'special_folders_mapping': Mapping of 'title' to 'special folder' path, used to
           obtain the path of folder in case of 'SPECIAL_FOLDER'.

        This function updates the following instance variables:
        - 'process': The process associated with the window.
        - 'process_id': The process id associated with the window.
        - 'type': The type of the window ('SPECIAL_FOLDER', 'NORMAL_FOLDER', 'UNSPECIFIED', 'UNLINKED', 'UNKNOWN').
        - 'create_cmd': The command to open the window, if applicable.
        """
        # If this is a Windows File-Explorer Window (typically a folder/directory)
        if self.hwnd_class == 'CabinetWClass':
            # Retrieve the folder path through its shell instance
            hwnd_shell_instance = shell_windows_instance_mapping[self.hwnd]
            hwnd_shell_path = hwnd_shell_instance.LocationURL

            # If it's a 'SPECIAL_FOLDER' (explorer window without retrievable path):
            # - Update the instance variable 'type' with the identifier 'SPECIAL_FOLDER'.
            # - Check if the path refers to a GUID (global unique identification number).
            # - Transform the path into a cmd-command (using 'Shell:{GUID}' or 'File:/URI'),
            #   this is to make the path executable (in that it creates the actual window).
            # - Update the instance variable 'create_cmd' with the modified path-command.
            if self.hwnd_title in special_folders_mapping:
                self.hwnd_type = WindowType.SPECIAL_FOLDER
                folder_path = special_folders_mapping[self.hwnd_title]
                folder_path_guid_match = bool(re.search(r'::{[\w-]*}', folder_path))
                folder_path_is_guid = folder_path_guid_match if not os.path.exists(folder_path) else False
                command_prefix = 'Shell:' if folder_path_is_guid else 'File:/'
                create_command = os.path.normpath(urllib.parse.unquote(f'{command_prefix}{folder_path}'))
                self.hwnd_create_cmd = create_command

            # Else it's a 'NORMAL_FOLDER' (explorer window with a retrievable path):
            elif (hwnd_shell_path != ''):
                # Update the instance variable 'type' with the identifier 'NORMAL_FOLDER'.
                self.hwnd_type = WindowType.NORMAL_FOLDER
                # Update the instance variable 'create_cmd' with the path (URI).
                self.hwnd_create_cmd = os.path.normpath(urllib.parse.unquote(hwnd_shell_path))
                # Update the instance variables for folder view options.
                folder_obj = hwnd_shell_instance.Document
                self.folder_icon_size = folder_obj.IconSize
                self.folder_view_mode = folder_obj.CurrentViewMode
                self.folder_sort_column = folder_obj.SortColumns
                self.folder_group_by = folder_obj.GroupBy
                print(self.folder_group_by)
                # Update the instance variables for files in folder.
                self.folder_selected_files = [file.Name for file in folder_obj.SelectedItems()]
                self.folder_all_files = [file.Name for file in folder_obj.Folder.Items()]
                self.folder_focused_file = folder_obj.FocusedItem.Name if folder_obj.FocusedItem else None


        # If a title and class was found but no type has been retrieved (no associated window process):
        # - Set instance variable 'type' to 'UNLINKED'.
        if self.hwnd_title and self.hwnd_class and not self.hwnd_type:
            self.hwnd_type = WindowType.UNLINKED





class WindowManager:
    def __init__(self):
        # Initialize an empty list to store Window objects.
        self.all_windows = []

        # Initialize condition: Visible (dette blir en input).
        self.visible_windows_only = True

        # Initialize a Windows Shell COM object and retrieve 'special folder' paths.
        self.shell_instance_mapped, self.special_folders_mapped = initialize_shell_windows_instance()


        self.counter = 0

        # Instead of using win32gui.EnumWindows for retrieving all windows, we are using a
        # continous while loop
        # RETRIEVE AND RETURN LAYOUT DATA FOR CURRENTLY OPEN FILE-EXPLORER WINDOWS
        # def GET_WINS_ZORDER():
        #     # Initialize a list to store data for all (currently open) window handles in
        #     window_data_retrieved = []
        #     # Get the handle of the window that is at the top of the z-order (the window on top)
        #     current_window_handle = win32gui.GetTopWindow(None)
        #     # Use a while loop to ensure traversing through all windows (in the z-order)
        #     while current_window_handle:
        #         window_data_retrieved.append(current_window_handle)
        #         # Get the next window in the z-order
        #         current_window_handle = win32gui.GetWindow(current_window_handle, win32con.GW_HWNDNEXT)

        #     # Return the current layout window data (as list with dictionaries for each currently open file-explorer windows)
        #     return window_data_retrieved
        # a = GET_WINS_ZORDER()




# the win32gui.EnumWindows function simply enumerates all open windows and calls a callback function for each window
# it does not create any COM objects or interact with the windows in any way
# enumerate all top-level windows,
# win32gui.EnumWindows is a function that enumerates all open windows and calls a callback function for each window, passing the handle to the window (hwnd) as an argument.

# The win32gui.EnumWindows function is a function that iterates over a list of all open windows on the system and calls a specified callback function for each window.

# The win32gui.EnumWindows function takes a callback function as an argument and calls the callback function for each open window,
# passing the handle to the window (hwnd) as the first argument. The callback function can then use the hwnd value to retrieve additional
# information about the window, such as the window title, the window class, the window style, etc.

# win32gui.EnumWindows is a function in the win32gui module of the pywin32 library that allows you to enumerate all top-level windows on the screen.
# It takes a callback function and a data argument as arguments, and it calls the callback function for each window, passing the window handle (HWND)
# and the data argument as arguments. The callback function should return True to continue enumerating windows, or False to stop enumerating.
# EnumWindows(callback[, data])
# The callback argument is a function that will be called for each window. It should have the following signature:

# The hwnd argument is the window handle (HWND) of the current window. The data argument is the data argument that was passed to EnumWindows.
# The callback function should return True to continue enumerating windows, or False to stop enumerating.

    # def get_all_open_windows_enumerate(self):
    #     def window_enum_handler(hwnd, ctx):
    #         if win32gui.IsWindowVisible(hwnd):
    #             print ( hex( hwnd ), win32gui.GetWindowText( hwnd ) )

    #     win32gui.EnumWindows( window_enum_handler, None )
    def get_open_windows(self):
        # Reset the list of Window objects
        self.all_windows = []

        #
        # Instead of using
    #     # Retrieve all currently open windows in the Z order
    #     # Instead of using enumerating all windows through win32gui.EnumWindows,
    #     # a continous while loopto enumerate all windows, for retrieving all windows, we are using a
    #     # continous while loop
    #     # RETRIEVE AND RETURN LAYOUT DATA FOR CURRENTLY OPEN FILE-EXPLORER WINDOWS
    #     # Initialize a list to store data for all (currently open) window handles in
    #     window_data_retrieved = []

    #     # Get the handle of the window that is at the top of the z-order (the window on top)
    #     current_hwnd = win32gui.GetTopWindow(None)
    #     # Use a while loop to ensure traversing through all windows (in the z-order)
    #     while current_hwnd:
    #         window_data_retrieved.append(current_hwnd)
    #         # Get the next window in the z-order
    #         current_hwnd = win32gui.GetWindow(current_hwnd, win32con.GW_HWNDNEXT)

    #     # Return the current layout window data (as list with dictionaries for each currently open file-explorer windows)
    #     return window_data_retrieved
    #     a = GET_WINS_ZORDER()

    def wm_get_open_windows(self):
        # Reset the list of Window objects
        self.all_windows = []
        # Enumerate all windows and pass them to the wm_enumerate_windows_callback method
        win32gui.EnumWindows(self.wm_enumerate_windows_callback, None)

    def wm_enumerate_windows_callback(self, hwnd, _):
        # visibleOnly or AllWindows
        if (self.visible_windows_only and win32gui.IsWindowVisible(hwnd)) or (not self.visible_windows_only):
            # Create a Window object for the current window
            window = Window(hwnd)
            #
            window.get_window_process()
            # Get additional type-specific window data for the current window
            window.get_explorer_windows(self.shell_instance_mapped, self.special_folders_mapped)

            # Add the current window to the list of windows
            self.all_windows.append(window)





    def return_windows(self):
        # Reset the list of Window objects
        return self.all_windows






    def wm_save(self, filename):
        # Open the file at the specified filepath in write mode
        # with open(filename, "w", encoding="utf-8") as file:
        with open(filename, "w") as file:
            # Iterate through the list of retrieved windows
            for window in self.all_windows:
                if window.hwnd_type in [WindowType.SPECIAL_FOLDER, WindowType.NORMAL_FOLDER]:
                    # print(win32gui.GetWindow(window.hwnd, GWL_EXSTYLE))
                    # print('%s: %s' % (window.hwnd, window.title))
                    # print(win32gui.GetWindowPlacement(window.hwnd))
                    print('hwnd: %s' % str(window.hwnd))
                    print('hwnd_visibility_state: %s' % str(window.hwnd_visibility_state))
                    print('hwnd_title: %s' % str(window.hwnd_title))
                    print('hwnd_class: %s' % str(window.hwnd_class))
                    print('hwnd_controls_state: %s' % str(window.hwnd_controls_state))
                    print('hwnd_position: %s' % str(window.hwnd_position))
                    print('hwnd_size: %s' % str(window.hwnd_size))
                    print('hwnd_process: %s' % str(window.hwnd_process))
                    print('hwnd_process_id: %s' % str(window.hwnd_process_id))
                    print('hwnd_create_cmd: %s' % str(window.hwnd_create_cmd))
                    print('hwnd_type: %s' % str(window.hwnd_type))
                    # print('-')
                    print('hwnd_placement: %s' % str(window._placement))
                    print('hwnd_rect: %s' % str(window._rect))
                    #
                    print('folder_icon_size: %s' % str(window.folder_icon_size))
                    print('folder_view_mode: %s' % str(window.folder_view_mode))
                    print('folder_sort_column: %s' % str(window.folder_sort_column))
                    print('folder_group_by: %s' % str(window.folder_group_by))
                    print('folder_selected_files: %s' % str(window.folder_selected_files))
                    print('folder_all_files: %s' % str(window.folder_all_files))
                    print('folder_focused_file: %s' % str(window.folder_focused_file))
                    print('--------------------------------------\n')
                    # Save the current window to the file
                    # window.save(file)
                    # file.write(f"\n\n")
                    # file.write(f"{window.type}\n")
                    # file.write(f"{window.title}\n")
                    # file.write(f"{window.class_name}\n")
                    # file.write(f"{window.position[0]}, {window.position[1]}\n")
                    # file.write(f"{window.size[0]}, {window.size[1]}\n")
                    # if window.process:
                    #     file.write(f"{window.process}\n")
                    # if window.path:
                    #     file.write(f"{window.path}\n")
            print("---------------------")
            print(len(self.all_windows))

def main(args):
    # If the first command-line argument is 'save'
    if args[0] == "save":
        # Create a new WindowManager object
        manager = WindowManager()
        # Update the list of windows in the WindowManager object
        manager.wm_get_open_windows()
        # Save the windows to the file at the specified filepath
        manager.wm_save(args[1])
    # If the first command-line argument is 'load'
    elif args[0] == "load":
        pass


# manager = WindowManager()
# manager.wm_get_open_windows()
# manager.wm_save("windows.txt")

manager = WindowManager()
manager.wm_get_open_windows()
a = manager.return_windows()
print(dir(a))
print(len(a))
print(a)
for x in a:
    print(x.hwnd_title)
    print(dir(x))
