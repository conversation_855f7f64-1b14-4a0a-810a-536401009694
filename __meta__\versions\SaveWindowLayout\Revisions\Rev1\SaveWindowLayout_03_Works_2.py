# Import modules
import os
import subprocess
import json
import win32gui
import win32com.client as win32

import time
# import win32process
# import psutil
# import uuid

# https://www.tenforums.com/tutorials/3123-clsid-key-guid-shortcuts-list-windows-10-a.html

# subprocess.run(['explorer', '/e,{B4BFCC3A-DB2C-424C-B029-7FE99A87C641}'])


# time.sleep(999)
# subprocess.run(['explorer', '/e,::{B4BFCC3A-DB2C-424C-B029-7FE99A87C641}'])

# CLSID Key / GUID: This PC
# explorer /e,::{20D04FE0-3AEA-1069-A2D8-08002B30309D}
# CLSID Key / GUID: Control Panel
# explorer /e,::{21EC2020-3AEA-1069-A2DD-08002B30309D}
# CLSID Key / GUID: Recycle Bin
# explorer /e,::{645FF040-5081-101B-9F08-00AA002F954E}


# Open an explorer window in the desired path
# path = "file:///D:/Dokumenter%20O.l/Documens"
# subprocess.run(['explorer', '/e,{B4BFCC3A-DB2C-424C-B029-7FE99A87C641}'])
# os.startfile(path)

# Wait for the window to be fully initialized
# time.sleep(1.3)
# win32gui.sleep(1000)

# Find the window handle for the explorer window
# win32gui.MoveWindow(win32gui.GetForegroundWindow(), 160, 0, 1600, 720, True)
# time.sleep(999)




# FUNCTION: RETRIEVE AND RETURN FILE-EXPLORER WINDOWS (WINDOW-HANDLES AND THEIR FOLDER-PATHS)
def fnRetrieveOpenExplorerWindows():
    # Create a variable for the result: (Handle | Title | Path | Pos[X,Y] | Size[X,Y])
    fileExplorerWindows_HwndData = []

    # Specify the CLSID (Class Identifier) for COM-Object: ShellWindowsPermalink
    ShellWindows_CLSID = '{9BA05972-F6A8-11CF-A442-00A0C90A8F39}'
    # For each File Explorer Window currently open (instances of COM-objects with specified CLSID)
    for currWin in win32.Dispatch(ShellWindows_CLSID):
        # Retrieve data from the current window
        currWinHwnd  = currWin.HWND
        currWinTitle = win32gui.GetWindowText(currWinHwnd)
        currWinPath  = currWin.LocationURL
        currWinPos   = [currWin.Left, currWin.Top]
        currWinSize  = [currWin.Width, currWin.Height]
        # Manual handling of windows that doesn't return a path
        currWinPath = ('file:///' if (currWinTitle == 'This PC') else currWinPath)
        currWinPath = ('file:///%s/Desktop' % (os.environ['USERPROFILE']).replace('\\', '/') if (currWinTitle == 'Desktop') else currWinPath)
        currWinPath = ('shell:RecycleBinFolder' if (currWinTitle == 'Recycle Bin') else currWinPath)
        currWinPath = ('control' if (currWinTitle == 'All Control Panel Items') else currWinPath)
        # If the current window is valid
        if (currWinPath != ''):
            # Add it's [Handle, Title, Path, Position, Size] to the result
            fileExplorerWindows_HwndData.append([currWinHwnd, currWinTitle, currWinPath, currWinPos, currWinSize])

    # Sort and return the result
    fileExplorerWindows_HwndData_Sorted = sorted(fileExplorerWindows_HwndData, key=lambda currItem: currItem[2])
    return fileExplorerWindows_HwndData_Sorted

# Retrieve window information: ['Handle', 'Title', 'Path', Pos:[X,Y], Size:[X,Y]]
fileExplorerWindows_HwndData = fnRetrieveOpenExplorerWindows()

# ------------------------------------------------------------------------------------------------------------------------------------

# Convert the window-data into a dictionary (for json dump): [{'Handle': 'Handle', 'Title': 'Title', 'Path': 'Path', 'Pos': [X,Y], 'Size': [X,Y]}]
jsonItemHandles = ['Handle', 'Title', 'Path', 'Pos', 'Size']
jsonDataDump = [{jsonHandle:itemValue for jsonHandle,itemValue in zip(jsonItemHandles, currItem)} for currItem in fileExplorerWindows_HwndData]

# Write the JSON file
with open((os.getcwd() + "/WindowLayout.json"), 'w') as jsonfile:
    json.dump(jsonDataDump, jsonfile, indent=4, sort_keys=True, separators=(',', ': '))

# ------------------------------------------------------------------------------------------------------------------------------------

# Load data from the JSON file
with open((os.getcwd() + "/WindowLayout.json"), 'r') as jsonfile:
    jsonData_Dict = [{jsonHandle: currItem[jsonHandle] for jsonHandle in jsonItemHandles} for currItem in json.load(jsonfile)]
    jsonData_List = [[currItem['Handle'],currItem['Title'],currItem['Path'],currItem['Pos'],currItem['Size']] for currItem in jsonData_Dict]
    print("\n")
    print(jsonData_List)
# ------------------------------------------------------------------------------------------------------------------------------------




#     for currItem in jsonData:
# 'Handle','Title','Path','Pos','Size'
#     # Print the data
#     print(data)
#     print(data[0]['Handle'])  # prints "value1"
    # print(data['key2'])  # prints "value2"
    # print(data['key3'])  # prints [1, 2, 3]

    # json.dump(data, jsonfile, indent=4)

    # # os.system("file:\\")
    # time.sleep(10)
    # # For each item retrieved
    # for currIndex in range(0, len(fileExplorerWindows_HwndData["Handle"])):
    #     # Store the current item in separate variables (for readability purposes)
    #     currWinHwnd = fileExplorerWindows_HwndData["Handle"][currIndex]
    #     currWinTitle = fileExplorerWindows_HwndData["Title"][currIndex]
    #     currWinPath = fileExplorerWindows_HwndData["Path"][currIndex]
    #     currWinPos = fileExplorerWindows_HwndData["Pos"][currIndex]
    #     currWinSize = fileExplorerWindows_HwndData["Size"][currIndex]

    #     # Open a new instance of the current window (if it's not already open)
    #     alreadyOpen = win32gui.FindWindow('CabinetWClass', currWinTitle)
    #     if not alreadyOpen: os.system("explorer %s" % currWinPath)
    #     # explorerOpened = win32gui.FindWindow('CabinetWClass', currWinTitle)
    #     # hWnd = win32gui.FindWindow(class_name, caption)
    #     print(alreadyOpen)

        # win32gui.MoveWindow(currWinHwnd, currWinPos[0], currWinPos[1], currWinSize[0], currWinSize[1], True)

        # print('WinHandle: %s' % currWinHwnd)
        # print('WinTitle:  %s' % currWinTitle)
        # print('WinPath:   %s' % currWinPath)
        # print('WinPos:    %s' % currWinPos)
        # print('WinSize:   %s' % currWinSize)
        # print(fileExplorerWindows_HwndData["Handle"][currIndex])
        # # (0:HwndHandle | 1:FromLeftEdge | 2:FromTop | 3:Width | 4:Height | 5:Refresh)
        # currWinSize = win32gui.GetWindowRect(currWinHwnd)
        # currWinY = currWin.Top
        # currWinWidth = currWin.Width
        # currWinHeight = currWin.Height
        # currWinPos = [currWin.Left, currWin.Top, currWin.Width, currWin.Height]
        # # print(currWin.Width)
        # # print(currWin.Left)
        # # print(currWin.Top)
        # # currWinPath = currWin.LocationURL.replace('file:///','')
        # if currWinTitle == "SaveWindowLayoutx":
        #     print(currWinTitle)
        #     print(currWinPath)
        #     print(win32gui.GetWindowRect(currWinHwnd))
        #     print(currWinPos)
        #     # print([currWinX,currWinY,currWinWidth,currWinHeight])
        #     # print(currWin)
        #     # print(currWin)
        #     # print(currWin)
        #     time.sleep(3)
        #     win32gui.MoveWindow(currWinHwnd, currWinPos[0], currWinPos[1], currWinSize[0], currWinSize[1], True)
        #     # win32gui.MoveWindow(currWinHwnd, currWinSize[0], currWinSize[1], currWinSize[2], currWinSize[3], True)
        # # print(currWinPath)
        # # print(win32gui.GetWindowText(currWinHwnd))
        # print('\n')

        # print(currWin.Height)
        # print(currWin.Width)
        # print(currWin.Left)
        # print(currWin.Top)

        # explorerHwndData.append([currWinHwnd, currWinPath])
    # Return the result
    # return fileExplorerWindows_HwndData

# for x in dir(win32):
    # print(x)
# win32gui.MoveWindow(win32gui.GetForegroundWindow(), 160, 0, 1600, 720, True)
# file:\\ (åpner "this pc")

# for currIndex, currItem in enumerate(openExplorerWindows):
#     # print(currIndex)
#     currWinHwnd = openExplorerWindows[currIndex][0]
#     currWinPath = openExplorerWindows[currIndex][1]
#     currWinTitle = win32gui.GetWindowText(currWinHwnd)
#     currWinSize = win32gui.GetWindowRect(currWinHwnd)
    # print(currWinHwnd)
    # print(currWinPath)
    # print(currWinTitle)
    # print(currWinSize)
    # print('\n')
