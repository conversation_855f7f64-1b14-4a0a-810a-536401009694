# Import modules
import os
import sys
import re
import time
import random
import ctypes
from ctypes import byref
import urllib.parse
from enum import Enum
# Error handling
import traceback
import pywintypes
# Import pywin32 modules
import comtypes
import win32com.client
from win32com.shell import shell
import win32com.shell.shellcon as shellcon
import win32con
import win32gui
import win32api
import win32process
# Module for coloring the prompt
from ansimarkup import ansiprint, AnsiMarkup, parse
#
import pywintypes
import pythoncom

import winreg


# winreg.SetValueEx(key, "Prop13",


# time.sleep(3131)

"""
1. Import modules (pywin32)
2. Retrieve all File Explorer Windows (using win32com.client.Dispatch('Shell.Application'))
3. If (hwnd.LocationName == 'TestFolder') do the following:
   - Set current viewmode to FVM_DETAILS (using hwnd.Document.CurrentViewMode = 4)
   - Retrieve selected files (using hwnd.Document.SelectedItems())
   - Retrieve the column it sorts by (using hwnd.Document.SortColumns)
   - Retrive the visible columns
   - Retrieve the sizes of the columns ('Name' and 'Size')
"""

# def get_folder_view_settings(folder_path):
#     shell = win32com.client.Dispatch("Shell.Application")
#     folder = shell.NameSpace(folder_path)
#     print(dir(folder))
#     return folder.ViewSettings
# print(dir(get_folder_view_settings(("D:\\Data\\Prosjekter\\Scripts\\WIP\\SaveWindowLayout\\misc_utils"))))
# for i in range(50005):
#     aaa=(pythoncom.CreateGuid())


# wmi = win32com.client.GetObject("winmgmts:")
# processes = wmi.InstancesOf("Win32_Process")
# print(processes)

# for process in processes:
#     if process.Name == "explorer.exe":
#         pid = process.Properties_("ProcessId").Value
#         hwnd = win32api.OpenProcess(win32con.PROCESS_ALL_ACCESS, False, pid)
#         style = win32api.GetWindowLong(hwnd, win32con.GWL_STYLE)
#         print(style)

# time.sleep(9999)
# shell = win32com.client.Dispatch("WScript.Shell")
# # app = shell.Application
# for x in dir(shell):
#     print(x)
# time.sleep(9999)
# import os
# from ctypes import byref
# from win32com.shell import shell, shellcon

# import os
# from comtypes import POINTER, IUnknown
# from win32com.shell import IID_IShellView, IShellView, SHCreateShellFolderView
# from win32com.shell import SHGetDesktopFolder, SHParseDisplayName
# # import win32com.shell.shellcon as shellcon
# folder_path = os.path.abspath("D:\\Data\\Prosjekter\\Scripts\\WIP\\SaveWindowLayout\\misc_utils")
# folder_pidl = shell.SHParseDisplayName(folder_path, shellcon.SHGDN_FORPARSING)[0]
# desktop_folder = shell.SHGetDesktopFolder()
# folder = desktop_folder.BindToObject(folder_pidl, None, shell.IID_IShellFolder2)

# # Get the default columns
# default_columns = folder.GetDefaultColumn
# # print((default_columns()))
# # Get the value or title of the first column
# # first_column_value = folder.GetDetailsOf(None, default_columns[0])
# # Get the unique identifier (FMTID, pid) of a column
# column_id = folder.MapColumnToSCID(1)
# # Get the details of an item by Column ID
# item_details = folder.GetDetailsEx(item_pidl, column_id)

# # print the details of the item
# print(item_details)

# # view = folder.CreateViewObject(None, shell.IID_IShellView)
# # print(folder)
# print(dir(shell))
# print(dir(folder))
# # print(folder.GetDefaultColumn())
# # print(dir(folder.GetDefaultColumn))
# # Get the default columns
# default_columns = folder.GetDefaultColumn()

# # Get the value or title of the first column
# first_column_value = folder.GetDetailsOf(None, default_columns[0])

# # Print the value or title of the first column
# print(first_column_value)



# current_view_mode = view.QueryInterface(folder)
# # print(view.GetCurrentInfo())
# print(current_view_mode)
# print(dir(current_view_mode))
# visible_columns = view.GetVisibleColumns()
# sort_order = view.GetSortColumns()

# print("Current view mode: ", current_view_mode)
# print("Visible columns: ", visible_columns)
# print("Sort order: ", sort_order)
# folder_path = os.path.abspath("D:\\Data\\Prosjekter\\Scripts\\WIP\\SaveWindowLayout\\misc_utils")
# folder_pidl = shell.SHParseDisplayName(folder_path, shellcon.SHGDN_FORPARSING)[0]
# print(folder_pidl)
# desktop_folder = shell.SHGetDesktopFolder()
# folder = comtypes.POINTER(comtypes.IUnknown)()
# print(dir(desktop_folder))
# # print(dir(comtypes))
# desktop_folder.BindToObject(folder_pidl, None, byref(folder))
# time.sleep(8888)
# view = comtypes.POINTER(comtypes.IShellView)()
# SHCreateShellFolderView(None, folder, byref(IID_IShellView), byref(view))


# folder_path = os.path.abspath("D:\\Data\\Prosjekter\\Scripts\\WIP\\SaveWindowLayout\\misc_utils")
# folder_pidl = shell.SHParseDisplayName(folder_path, shellcon.SHGDN_FORPARSING)[0]
# folder = shell.SHGetDesktopFolder()
# folder.BindToObject(folder_pidl, None, shell.IID_IShellFolder)

# view = shell.IID_IShellView
# shell.SHCreateShellFolderView(None, byref(folder), shell.IID_IShellView, byref(view))

# folder_path = os.path.abspath("D:\\Data\\Prosjekter\\Scripts\\WIP\\SaveWindowLayout\\misc_utils")
# folder_pidl = shell.SHParseDisplayName(folder_path, shellcon.SHGDN_FORPARSING)[0]
# folder = shell.SHGetDesktopFolder()
# folder.BindToObject(folder_pidl, None, shell.IID_IShellFolder)
# print(dir(shell))
# view = shell.IID_IShellView
# shell.SHCreateShellFolderView(None, byref(folder), shell.IID_IShellView, byref(view))

# current_view = view.GetCurrentViewMode()
# print("Current view mode:", current_view)

# visible_columns = (shellcon.SHCOLUMNID * 100)()
# num_columns = view.GetVisibleColumns(visible_columns, 100)
# print("Visible columns:", visible_columns[:num_columns])

# sort_order = (shellcon.SORTCOLUMN * 10)()
# num_sort_columns = view.GetSortColumns(sort_order, 10)
# print("Sort order:", sort_order[:num_sort_columns])

# Initializes a COM object instance of the 'Shell.Application' CLSID/ProgID
CLSID_SHELL_WINDOWS = 'Shell.Application'
shell_windows_instance = win32com.client.Dispatch(CLSID_SHELL_WINDOWS)
shell_windows_instance_mapping = {window.HWND: window for window in shell_windows_instance.Windows()}

# Retrieve special folders
CSIDL_CONSTANTS = [getattr(shellcon, name) for name in dir(shellcon) if name.startswith("CSIDL_")]
csidl_namespaces = [shell_windows_instance.Namespace(constant) for constant in CSIDL_CONSTANTS]
valid_namespaces = [namespace for namespace in csidl_namespaces if hasattr(namespace, 'Application')]
special_folders = [[namespace.Self.Name, namespace.Self.Path] for namespace in valid_namespaces]
special_folders_mapping = {item[0]: item[1] for item in special_folders}


# # Retrieve all hwnd's
# windows_all_hwnds = []
# current_hwnd = win32gui.GetTopWindow(None)
# while current_hwnd:
#     windows_all_hwnds.append(current_hwnd)
#     current_hwnd = win32gui.GetWindow(current_hwnd, win32con.GW_HWNDNEXT)
# # Retrieve hwnd's that are part of shell windows
# shell_hwnds = []
# for hwnd in windows_all_hwnds:
#     class_name = win32gui.GetClassName(hwnd)
#     if class_name == "CabinetWClass":
#         shell_hwnds.append(hwnd)
# # Get the IShellFolder interface for the desktop folder
# desktop = shell.SHGetDesktopFolder()
# for x in dir(desktop):
#     print(x)
# time.sleep(999)


# # Iterate through the shell windows to find the File Explorer one
# for hwnd in shell_hwnds:
#     if win32gui.IsWindowVisible(hwnd):
#         # Get the current view mode
#         view_mode = desktop.GetViewMode()
#         if view_mode == shell.FVM_ICON:
#             print("Current view: Extra large icons")
#         elif view_mode == shell.FVM_SMALLICON:
#             print("Current view: Small icons")
#         elif view_mode == shell.FVM_LIST:
#             print("Current view: List")
#         elif view_mode == shell.FVM_DETAILS:
#             print("Current view: Details")
#         else:
#             print("Current view: unknown")
# time.sleep(9999)


# Retrieve all hwnd's
windows_all_hwnds = []
current_hwnd = win32gui.GetTopWindow(None)
while current_hwnd:
    windows_all_hwnds.append(current_hwnd)
    current_hwnd = win32gui.GetWindow(current_hwnd, win32con.GW_HWNDNEXT)

# Retrieve hwnd's that are part of shell windows
shell_hwnds = []
for hwnd in windows_all_hwnds:
    if hwnd in shell_windows_instance_mapping:
        shell_hwnds.append(shell_windows_instance_mapping[hwnd])

print('any windows: %s' % len(windows_all_hwnds))
print('shell windows: %s' % len(shell_hwnds))
# print(dir(shell_hwnds[0]))
# print(dir(shell_hwnds[0].Document))
# print(dir(shell_hwnds[0].Document.Folder))






for hwnd in shell_hwnds:
    #
    print('-' * 100)
    # print(dir(win32com.client))
    # pidl, folder_view = win32com.client.CoClassBaseClass(
    #     win32com.client.CLSIDToClass,
    #     None,
    #     win32com.client.constants.CLSCTX_INPROC_SERVER,
    #     win32com.client.IID_IShellFolderView
    # )
    # folder_view.BindToObject(hwnd, None, win32com.client.IID_IShellFolder)
    # # Retrieve the column sizes
    # columns = folder_view.GetColumns()
    # for column in columns:
    #     print("Column: %s Width: %s" % (column.Name, column.Width))
    # # Retrieve the column ordering
    # column_order = folder_view.GetColumnOrder()
    # print("Column Order: %s" % column_order)
    # # print(dir(hwnd))
    # # print(hwnd.GetViewObject(None))


    # ------------------------------------------------------------
    # OBJ(FOLDER):
    # Title
    current_folder_title = hwnd.LocationName
    # print('current_folder_title: %s' % current_folder_title)
    # ------------------------------------------------------------
    # Path
    current_folder_path = hwnd.LocationURL
    print('current_folder_path: %s' % current_folder_path)
    # ------------------------------------------------------------

    # print(dir(hwnd.Document.Application))

    #
    specific_if_path = ("D:/Data/Prosjekter/Scripts/WIP/SaveWindowLayout/misc_utils")
    if specific_if_path in current_folder_path:
        current_folder_path = os.path.normpath(specific_if_path) if specific_if_path in current_folder_path else current_folder_path
        print(current_folder_path)
        # Get the IShellFolder interface for the folder ('PyIShellFolder')
        folder = shell.SHGetDesktopFolder()
        # SHGDN = SHGetNameFromIDList
        folder_pidl = shell.SHParseDisplayName(current_folder_path, shellcon.SHGDN_FORPARSING)[0]
        folder.BindToObject(folder_pidl, None, shell.IID_IShellFolder)
        print(folder.GetDisplayNameOf(folder_pidl, shellcon.SHGDN_NORMAL))
        # folder.CreateViewObject(hwnd.HWND, shell.IID_IShellFolder)
    # print(folder.GetShellFolder)
    # print((dir(folder)))
    # print((folder_pidl))
    # print(dir(folder))
    # print(folder.GetDisplayNameOf)
    # for x in folder.EnumObjects():
    #     print(x)
    # print((folder.EnumObjects()))
    # print(type(folder))
    # print(dir(folder))
    # Create the shell folder view
# current_window_instance.Document.CurrentViewMode
    # from ctypes import byref

    # folder_path = os.path.abspath("D:\\Data\\Prosjekter\\Scripts\\WIP\\SaveWindowLayout\\misc_utils")
    # folder_pidl = shell.SHParseDisplayName(folder_path, shellcon.SHGDN_FORPARSING)[0]
    # folder = shell.SHGetDesktopFolder()
    # folder.BindToObject(folder_pidl, None, shell.IID_IShellFolder)
    # printshell.SHParseDisplayName(current_folder_path)
    # folder_pidl = shell.SHParseDisplayName(current_folder_path)[0]
    # folder = shell.SHGetDesktopFolder()
    # folder.BindToObject(folder_pidl, None, shell.IID_IShellFolder)


    # # view = win32com.client.IID_IShellView
    # view=shell.SHCreateShellFolderView(hwnd, None)
    # # aaa = view.GetItemObject.IID_IShellFolder
    # print(dir(view))
    # # print(dir(aaa))
    # # print((view.GetItemObject))
    # current_view = view.GetCurrentViewMode()
    # print("Current view mode:", current_view)


    # hr, view = shell.SHCreateShellFolderView(folder, None)
    # print("hhh")
    # # Get the current view mode
    # hr, current_view = view.GetCurrentViewMode(None)
    # # Get the list of visible columns
    # hr, visible_columns = view.GetVisibleColumns()
    # # Get the sort order
    # hr, sort_order = view.GetSortColumns()
    # # Do something with the retrieved information
    # print("Current view mode:", current_view)
    # print("Visible columns:", visible_columns)
    # print("Sort order:", sort_order)


    # ------------------------------------------------------------
    # FOLDER(CONTENT):
    # Focused Filename (1) (str)
    focused_item_object = hwnd.Document.FocusedItem
    focused_item = focused_item_object.Name if focused_item_object else focused_item_object
    # print('focused_item: %s' % focused_item)
    # ------------------------------------------------------------
    # Selected Filenames (list)
    selected_files = [current.Name for current in hwnd.Document.SelectedItems()]
    # print('selected_files: %s' % selected_files)
    # ------------------------------------------------------------


    # ------------------------------------------------------------
    # FOLDER(VIEW):
    # Folder Zoom / Icon Size (px) (int)
    current_view_zoom = hwnd.Document.IconSize
    # print('current_view_zoom: %s' % current_view_zoom)
    # ------------------------------------------------------------
    # View-mode (List/Details/etc) (int --> Str)
    FOLDER_VIEW_MODE = [
                            [-1, 'FVM_AUTO'],
                            [1, 'FVM_ICON'],
                            [2, 'FVM_SMALLICON'],
                            [3, 'FVM_LIST'],
                            [4, 'FVM_DETAILS'],
                            [5, 'FVM_THUMBNAIL'],
                            [6, 'FVM_TILE'],
                            [7, 'FVM_THUMBSTRIP'],
                            [8, 'FVM_CONTENT'],
                        ]
    current_view = hwnd.Document.CurrentViewMode
    # print('current_view: %s' % str(FOLDER_VIEW_MODE[current_view]))
    # ------------------------------------------------------------
    # Sort-by
    sort_column = hwnd.Document.SortColumns
    sort_column = sort_column if focused_item else False
    # print(sort_column)
    # Viktig: Denne kan feile om man prøver å sette read only (control panel etc)
    # if sort_column:
    #     hwnd.Document.SortColumns = "prop:-System.Size;"
    # ------------------------------------------------------------
    # print(dir(hwnd.Document))
    # ---
    # USIKKER PÅ HVA DETTE EGENLIG ER
    view_options = hwnd.Document.ViewOptions
    # print(view_options)
    # Define a dictionary to store the option names and their corresponding integer values
    VIEW_OPTIONS_STR = {
        0x0001: "FVO_VISTALAYOUT",
        0x0002: "FVO_CUSTOMPOSITION",
        0x0004: "FVO_CUSTOMORDERING",
        0x0008: "FVO_CUSTOMPLACEMENT",
        0x0010: "FVO_CUSTOMGROUPING",
        0x0020: "FVO_CUSTOMSORTING",
        0x0040: "FVO_CUSTOMFILTERING",
        0x0080: "FVO_CUSTOMGROUPINGPRIMARY",
        0x0100: "FVO_CUSTOMGROUPINGSECONDARY",
        0x0200: "FVO_CUSTOMGROUPINGTERTIARY",
        0x0400: "FVO_CUSTOMASYNCPLACEMENT",
        0x0800: "FVO_CUSTOMASYNCORDERING",
        0x1000: "FVO_CUSTOMASYNCFILTERING",
        0x2000: "FVO_CUSTOMASYNCGROUPING",
        0x4000: "FVO_CUSTOMASYNCSORTING",
        0x8000: "FVO_CUSTOMASYNCNAVIGATION",
    }
    # for option, option_name in VIEW_OPTIONS_STR.items():
    #     if view_options & option:
    #         print(f"{option_name} is enabled")
    # ------------------------------------------------------------



    # shell = Dispatch("Shell.Application")
    # shellWindows = shell.Windows()
    # # Enumerate the windows, and find the one you want
    # for win in shellWindows:
    #     if win.Name == "My Window":
    #         folder = win.Document.Application

    # view = hwnd.Document.Application.IShellView()
    # print(view)
    # print(dir(hwnd.Document.Application))
    # print(hwnd.Document.Application.IShellView)
    # folder = shell.SHGetDesktopFolder().CreateViewObject
    # print(dir(folder))
    # Create the IShellView for the folder
    # view = folder.IID_IShellView
    # print(folder)
    # shell.SHCreateViewObject(win.HWND, byref(folder), shell.IID_IShellView,

    # print(dir(hwnd.Document))


    # print(dir(hwnd))
    # WScript.Shell.Application.Document.SortColumns
    # print(dir(hwnd.Document.SortColumns))
    # print(hwnd.Document.SortColumns)
    # print(hwnd.Document.SortColumns.dispid)
    # # sort_column = hwnd.Document.SortColumns
    # print('sort_by_column: %s' % sort_column)
    # prop:System.ItemNameDisplay;
    # sort_by_column: prop:-System.Size;

    # print(dir(hwnd.Document))
    # print((hwnd.Document.SortColumns))





    # print(hwnd.LocationName)


    # print(hwnd.Name)
    # print(hwnd.FullName)
    # print(dir(hwnd))

    # print(hwnd.Document)
    # print(dir(hwnd.Document))

    # selected_files_names =
    # print(len(selected_files))
    # for current_file in selected_files:
    #     print(current_file)

    # print(type(current_view))
    # print(dir(hwnd.Document))

        # FVM_ICON Extra large icons
        # FVM_SMALLICON Small icons
        # FVM_LIST List
        # FVM_DETAILS Details
        # FVM_THUMBNAIL Thumbnails
        # FVM_TILES Tiles
        # FVM_THUMBSTRIP Thumbnail strip
    # print((hwnd.Document.CurrentViewMode))
    # hwnd.Document.CurrentViewMode = 4

    # if current_view == 0:
    #     print("Current view: Extra large icons")
    #     print(current_view)
    # elif current_view == 1:
    #     print("Current view: Large icons")
    #     print(current_view)
    # elif current_view == 2:
    #     print("Current view: Small icons")
    #     print(current_view)
    # elif current_view == 3:
    #     print("Current view: List")
    #     print(current_view)
    # elif current_view == 4:
    #     print("Current view: Details")
    #     print(current_view)
    # elif current_view == 6:
    #     print("Current view: Tiles")
    #     print(current_view)
    # elif current_view == 8:
    #     print("Current view: Content")
    #     print(current_view)
    # else:
    #     print("Current view: Unknown")
    #     print(current_view)
    # print(dir(hwnd))
    # time.sleep(9999)
    # print(hwnd.LocationName)
    # print(hwnd.FullName)

# import win32com.client

# shell = win32com.client.Dispatch("Shell.Application")
# windows = shell.Windows()

# # Iterate through all the open windows
# for i in range(windows.Count):
#     window = windows.Item(i)
#     print("Title: ", window.LocationName)
#     print("URL: ", window.FullName)

time.sleep(9999)
# Resize terminal window (PosX, PosY, Width, Height)
cmd_hwnd = win32gui.GetForegroundWindow()
win32gui.MoveWindow(cmd_hwnd, 160, 120, 1600, 720, True)


# GET ACTIVE/FOREGROUND WINDOW DATA
def get_active_window_info():
    # Get foreground window hwnd
    hwnd = win32gui.GetForegroundWindow()
    if not hwnd:
        return False

    # These will fail if not window
    # if not win32gui.IsWindow(hwnd):
    #     return False

    # Get the current monitor
    monitor_handle = win32api.MonitorFromWindow(hwnd, win32con.MONITOR_DEFAULTTONEAREST)
    monitor_info = win32api.GetMonitorInfo(monitor_handle)
    monitor_device = (monitor_info["Device"])

    # Get general window data: Intermediate values used to compute state/position/size
    hwnd_placement = win32gui.GetWindowPlacement(hwnd)
    hwnd_rect = win32gui.GetWindowRect(hwnd)
    hwnd_controls_state = hwnd_placement[1]
    hwnd_position = (hwnd_rect[0], hwnd_rect[1])
    hwnd_size = (hwnd_rect[2] - hwnd_rect[0], hwnd_rect[3] - hwnd_rect[1])

    # Get general hwnd (Error if 'GetWindowPlacement' or 'GetWindowRect')
    hwnd_visibility_state = win32gui.IsWindowVisible(hwnd)
    hwnd_title = win32gui.GetWindowText(hwnd)
    hwnd_class = win32gui.GetClassName(hwnd)

    # Prepare data for windows with a process handle
    hwnd_process_path = None
    hwnd_process_id = None

    # win32api: Retrieve the executable filename by accessing the current window's process
    # hwnd_thread_id, hwnd_process_id = win32process.GetWindowThreadProcessId(hwnd)
    # hwnd_process_handle = win32api.OpenProcess(win32con.PROCESS_QUERY_INFORMATION, False, hwnd_process_id)

    # CTYPES: Retrieve the process handle of the window
    hwnd_thread_id, hwnd_process_id = win32process.GetWindowThreadProcessId(hwnd)
    hwnd_process_query = (win32con.PROCESS_QUERY_INFORMATION | win32con.PROCESS_VM_READ)
    hwnd_process_handle = ctypes.windll.kernel32.OpenProcess(hwnd_process_query, False, hwnd_process_id)
    # If a process handle was optained
    if hwnd_process_handle:
        # Get the window style
        # style = win32api.GetWindowLong(hwnd_process_handle, win32con.GWL_STYLE)
        # # Check the window style for the specific view setting
        # if style & win32con.LVS_TYPEMASK == win32con.LVS_ICON:
        #     print("Current view: Extra large icons")
        # elif style & win32con.LVS_TYPEMASK == win32con.LVS_SMALLICON:
        #     print("Current view: Small icons")
        # elif style & win32con.LVS_TYPEMASK == win32con.LVS_LIST:
        #     print("Current view: List")
        # elif style & win32con.LVS_TYPEMASK == win32con.LVS_REPORT:
        #     print("Current view: Details")
        # else:
        #     print("Current view: unknown")
        try:
            hwnd_process_path = win32process.GetModuleFileNameEx(hwnd_process_handle, 0)
        except pywintypes.error:
            print(traceback.format_exc())


    # Return a dictionary with keys and values for each piece of information
    return {
        "monitor":monitor_device,
        "title":hwnd_title,
        "class":hwnd_class,
        "hwnd":hwnd,
        "visibility":hwnd_visibility_state,
        "controls_state":hwnd_controls_state,
        "position":hwnd_position,
        "size":hwnd_size,
        "placement":hwnd_placement,
        # "rect":hwnd_rect,
        "process_path":hwnd_process_path,
        "process_id":hwnd_process_id
    }


# PRINT COLOR RANGE
# for color_int in range(0,255):
#     os.system('color')
#     color_print = lambda x: ansiprint(f"<fg {color_int}>{x}</fg {color_int}>")
#     color_print('%s: this is the current color' % color_int)


# Specify colors to exclude (dark colors)
dark_colors_exclude = [0] + list(range(16, 22)) + list(range(52, 55)) + list(range(58, 64)) + list(range(232, 241))


# Store the previous window
previous_window_data = None
while True:
    # Get the active window information
    active_window_data = get_active_window_info()
    # If any changes has been made to the current window (continous check)
    if (active_window_data != False) and (active_window_data != previous_window_data):
        try:
            # Enable ANSI escapes and specify a random color (to print to terminal)
            os.system('color')
            color_int = random.randint(15,255)
            while color_int in dark_colors_exclude:
                color_int = random.randint(15,255)
            color_print = lambda x: ansiprint(f"<fg {color_int}>{x}</fg {color_int}>")

            # Check specific changes
            monitor_changed = (previous_window_data and (active_window_data["monitor"] != previous_window_data["monitor"]))
            hwnd_changed = (previous_window_data and (active_window_data["hwnd"] != previous_window_data["hwnd"]))
            pos_changed = (previous_window_data and (active_window_data["position"] != previous_window_data["position"]))
            size_changed = (previous_window_data and (active_window_data["size"] != previous_window_data["size"]))
            placement_changed = (previous_window_data and (active_window_data["placement"] != previous_window_data["placement"]))

            # If the window handle has been changed, log information
            if hwnd_changed or size_changed or pos_changed:
                # Set cmd title (to monitor)
                os.system('TITLE "%s   |   Size:%s   |   Pos:%s"' % (str(active_window_data["monitor"]), str(active_window_data["size"]), str(active_window_data["position"])))


                tmpData = {}
                tmpData.update(active_window_data)
                del tmpData["monitor"]

                # Print separator line relative to window width
                cmd_hwnd_rect = win32gui.GetWindowRect(cmd_hwnd)
                cmd_hwnd_width = (cmd_hwnd_rect[2] - cmd_hwnd_rect[0])
                cmd_line_width = int(cmd_hwnd_width * 0.11)
                color_print("-" * (cmd_line_width))
                # Print (right-justified) window data
                keys = [str(item) for item in list(tmpData.keys())]
                values = [str(item) for item in list(tmpData.values())]
                max_key_length = max(len(key) for key in keys)
                for key, value in zip(keys, values):
                    color_print(key.rjust(max_key_length + 20) + " : " + value)
            # Update the handle to the previous window
            previous_window_data = active_window_data
            # Wait for a short period of time before checking the active window again
            time.sleep(0.15)
        except:
            # color_print('<fg 15>ERROR: %s</fg 15>' % str(active_window_data))
            print(str(previous_window_data))
            print(str(active_window_data))
            print(win32gui.GetForegroundWindow())
            pass
