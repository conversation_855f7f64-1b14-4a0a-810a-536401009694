# Window Tiler Configuration

## Quick Start

**Default usage:**
```python
from window_tiler import WindowTilerApp
WindowTilerApp().run()
```

**Custom configuration:**
```python
from window_tiler import WindowTilerConfig, WindowTilerApp

config = WindowTilerConfig()
config.add_process_type('BROWSER', ['brave.exe'])
config.exclude_process('myapp.exe')
WindowTilerApp(config).run()
```

## Configuration API

```python
config = WindowTilerConfig()

# Add processes to window types
config.add_process_type('BROWSER', ['brave.exe', 'vivaldi.exe'])
config.add_process_type('TERMINAL', ['alacritty.exe'])

# Create new window types
config.process_types['MEDIA'] = ['vlc.exe', 'spotify.exe']

# Manage exclusions
config.exclude_process('steam.exe')
config.include_process('conhost.exe')
```

## Default Classifications

**Window Types:**
- BROWSER: chrome.exe, firefox.exe, msedge.exe, brave.exe
- TERMINAL: cmd.exe, powershell.exe, windowsterminal.exe
- EDITOR: notepad.exe, code.exe, sublime_text.exe
- IDE: devenv.exe, pycharm, idea64, rider64.exe
- EXPLORER: explorer.exe

**Excluded:** System processes like dwm.exe, svchost.exe, etc.

See `example_custom_config.py` for complete examples.
