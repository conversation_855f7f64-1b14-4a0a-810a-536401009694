# Window Tiler

Simple, reliable window tiling for Windows.

## Quick Start

**Default usage:**
```python
from window_tiler import WindowTilerApp
WindowTilerApp().run()
```

**Custom configuration:**
```python
from window_tiler import Config, WindowTilerApp

config = Config()
config.types['brave.exe'] = 'Browser'
config.skip.add('myapp.exe')
WindowTilerApp(config).run()
```

## Configuration

```python
config = Config()

# Add applications
config.types.update({
    'brave.exe': 'Browser',
    'alacritty.exe': 'Terminal',
    'vlc.exe': 'Media'
})

# Skip processes
config.skip.update({'steam.exe', 'nvidia-share.exe'})
```

## Default Types

- **Browser:** chrome.exe, firefox.exe, msedge.exe
- **Terminal:** cmd.exe, powershell.exe, windowsterminal.exe
- **Editor:** notepad.exe, code.exe, sublime_text.exe
- **IDE:** devenv.exe, pycharm64.exe, idea64.exe
- **Explorer:** explorer.exe

## Window Distribution

When you have more windows than grid cells, **all windows are distributed** across the available cells:

- **5 windows, 1×3 grid**: Cells get 2, 2, 1 windows respectively
- **4 windows, 1×2 grid**: Each cell gets 2 windows
- **7 windows, 2×2 grid**: Cells get 2, 2, 2, 1 windows respectively

Multiple windows in the same cell are arranged in a sub-grid for optimal space usage.

See `example_custom_config.py` and `test_layout_behavior.py` for examples.
