#
# This file is autogenerated by pip-compile with Python 3.11
# by the following command:
#
#    pip-compile --all-extras --output-file='E:\prg\prj\win32_window_monitor\docs\requirements.txt' pyproject.toml
#
alabaster==0.7.13
    # via sphinx
babel==2.12.1
    # via sphinx
build==1.0.3
    # via pip-tools
certifi==2023.7.22
    # via requests
charset-normalizer==3.2.0
    # via requests
click==8.1.7
    # via pip-tools
colorama==0.4.6
    # via
    #   build
    #   click
    #   pytest
    #   sphinx
coverage==7.3.1
    # via win32-window-monitor (pyproject.toml)
docutils==0.18.1
    # via
    #   myst-parser
    #   sphinx
    #   sphinx-rtd-theme
idna==3.4
    # via requests
imagesize==1.4.1
    # via sphinx
iniconfig==2.0.0
    # via pytest
jinja2==3.1.2
    # via
    #   myst-parser
    #   sphinx
markdown-it-py==3.0.0
    # via
    #   mdit-py-plugins
    #   myst-parser
markupsafe==2.1.3
    # via jinja2
mdit-py-plugins==0.4.0
    # via myst-parser
mdurl==0.1.2
    # via markdown-it-py
myst-parser==2.0.0
    # via win32-window-monitor (pyproject.toml)
packaging==23.1
    # via
    #   build
    #   pytest
    #   sphinx
pip-tools==7.3.0
    # via win32-window-monitor (pyproject.toml)
pluggy==1.3.0
    # via pytest
pygments==2.16.1
    # via sphinx
pyproject-hooks==1.0.0
    # via build
pytest==7.3.2
    # via win32-window-monitor (pyproject.toml)
pyyaml==6.0.1
    # via myst-parser
requests==2.31.0
    # via sphinx
snowballstemmer==2.2.0
    # via sphinx
sphinx==7.2.5
    # via
    #   myst-parser
    #   sphinx-rtd-theme
    #   sphinxcontrib-applehelp
    #   sphinxcontrib-devhelp
    #   sphinxcontrib-htmlhelp
    #   sphinxcontrib-jquery
    #   sphinxcontrib-qthelp
    #   sphinxcontrib-serializinghtml
    #   win32-window-monitor (pyproject.toml)
sphinx-rtd-theme==1.3.0
    # via win32-window-monitor (pyproject.toml)
sphinxcontrib-applehelp==1.0.7
    # via sphinx
sphinxcontrib-devhelp==1.0.5
    # via sphinx
sphinxcontrib-htmlhelp==2.0.4
    # via sphinx
sphinxcontrib-jquery==4.1
    # via sphinx-rtd-theme
sphinxcontrib-jsmath==1.0.1
    # via sphinx
sphinxcontrib-qthelp==1.0.6
    # via sphinx
sphinxcontrib-serializinghtml==1.1.9
    # via sphinx
urllib3==2.0.4
    # via requests
wheel==0.41.2
    # via pip-tools

# The following packages are considered to be unsafe in a requirements file:
# pip
# setuptools
